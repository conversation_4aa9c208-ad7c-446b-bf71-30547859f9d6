#!/bin/bash

# SPEAR RustDesk Server Test Script
# This script tests your RustDesk server setup

echo "🧪 SPEAR RustDesk Server Test"
echo "============================"
echo ""

# Test 1: Check Docker containers
echo "1️⃣ Testing Docker Containers..."
cd ~/spear-rustdesk-server
CONTAINERS=$(docker compose -f docker-compose-simple.yml ps --format "table {{.Name}}\t{{.Status}}" | grep -c "Up")

if [ "$CONTAINERS" -eq 2 ]; then
    echo "   ✅ Both RustDesk containers are running"
    docker compose -f docker-compose-simple.yml ps --format "table {{.Name}}\t{{.Status}}"
else
    echo "   ❌ RustDesk containers not running properly"
    docker compose -f docker-compose-simple.yml ps
fi
echo ""

# Test 2: Check ports
echo "2️⃣ Testing Port Connectivity..."
LOCAL_IP="*************"

# Test ID Server (21116)
if nc -z $LOCAL_IP 21116 2>/dev/null; then
    echo "   ✅ ID Server (21116) is accessible"
else
    echo "   ❌ ID Server (21116) is not accessible"
fi

# Test Relay Server (21117)
if nc -z $LOCAL_IP 21117 2>/dev/null; then
    echo "   ✅ Relay Server (21117) is accessible"
else
    echo "   ❌ Relay Server (21117) is not accessible"
fi

# Test Web Client (21118)
if nc -z $LOCAL_IP 21118 2>/dev/null; then
    echo "   ✅ Web Client (21118) is accessible"
else
    echo "   ❌ Web Client (21118) is not accessible"
fi
echo ""

# Test 3: Check encryption key
echo "3️⃣ Testing Encryption Key..."
if [ -f "data/id_ed25519.pub" ]; then
    KEY=$(cat data/id_ed25519.pub)
    echo "   ✅ Encryption key found: $KEY"
else
    echo "   ❌ Encryption key not found"
fi
echo ""

# Test 4: Check Spear application
echo "4️⃣ Testing Spear Application..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "   ✅ Spear application is running at http://localhost:3000"
else
    echo "   ❌ Spear application is not accessible"
fi
echo ""

# Test 5: Check environment variables
echo "5️⃣ Testing Environment Variables..."
cd /Users/<USER>/spear
if grep -q "RUSTDESK_SERVER_IP=\"*************\"" .env; then
    echo "   ✅ Spear .env file configured with local RustDesk server"
else
    echo "   ❌ Spear .env file not properly configured"
fi
echo ""

# Summary
echo "📋 Test Summary"
echo "==============="
echo "🔑 Your RustDesk Server Key: $(cat ~/spear-rustdesk-server/data/id_ed25519.pub)"
echo "🌐 Local Server Address: *************:21116"
echo "🔗 Web Client URL: http://*************:21118"
echo "🎮 Spear Application: http://localhost:3000"
echo ""
echo "📱 Configure your devices with:"
echo "   ID Server: *************:21116"
echo "   Relay Server: *************:21117"
echo "   Key: $(cat ~/spear-rustdesk-server/data/id_ed25519.pub)"
echo ""
echo "🎉 Your SPEAR RustDesk Server is ready!"
