const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase database connection...\n');
  
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL not found in environment variables');
    return;
  }
  
  console.log('📋 Database URL configured:', databaseUrl.replace(/\/\/[^:]+:[^@]+@/, '//[username]:[password]@'));
  
  const client = new Client({
    connectionString: databaseUrl,
  });
  
  try {
    console.log('🔌 Connecting to Supabase database...');
    await client.connect();
    console.log('✅ Connected successfully to Supabase!');
    
    console.log('\n🧪 Testing basic query...');
    const result = await client.query('SELECT 1 as test, NOW() as timestamp');
    console.log('✅ Query successful:', result.rows[0]);
    
    console.log('\n📊 Testing database info...');
    const versionResult = await client.query('SELECT version()');
    console.log('✅ PostgreSQL version:', versionResult.rows[0].version.split(' ')[0] + ' ' + versionResult.rows[0].version.split(' ')[1]);
    
    console.log('\n🎉 Supabase connection test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('🔍 Error details:', error);
  } finally {
    await client.end();
    console.log('🔌 Connection closed.');
  }
}

testSupabaseConnection();
