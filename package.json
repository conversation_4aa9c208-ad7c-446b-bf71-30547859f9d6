{"name": "newxxxapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "postbuild": "node scripts/generate-sitemap.js", "start": "next start", "start:railway": "next start", "start:dev": "node scripts/load-env.js && next start", "lint": "next lint", "generate-sitemap": "node scripts/generate-sitemap.js", "generate-secret": "node scripts/generate-nextauth-secret.js", "postinstall": "prisma generate", "db:seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts", "db:seed-js": "node scripts/seed-database.js", "generate:secret": "node scripts/generate-secret.js", "db:migrate-passwords": "ts-node --compiler-options {\"module\":\"CommonJS\"} scripts/migrate-passwords.ts", "validate-env": "node scripts/validate-env.js", "load-env": "node scripts/load-env.js", "vercel-build": "node scripts/load-env.js && prisma generate && next build"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@ai-sdk/openai": "^1.3.15", "@auth/prisma-adapter": "^2.8.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@paypal/paypal-server-sdk": "^1.0.0", "@prisma/client": "^6.6.0", "@radix-ui/react-accordion": "^1.2.9", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "autoprefixer": "^10.4.21", "bcrypt": "^6.0.0", "chrome-aws-lambda": "^10.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "framer-motion": "^12.7.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.488.0", "next": "15.3.0", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "postcss": "^8.5.6", "prisma": "^6.6.0", "puppeteer": "^10.4.0", "puppeteer-core": "^10.4.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "sonner": "^2.0.3", "stripe": "^18.1.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "20.17.44", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.3", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.0", "globby": "^13.2.2", "prettier": "^3.1.0", "typescript": "5.8.3"}}