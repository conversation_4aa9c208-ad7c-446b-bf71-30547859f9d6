import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Store active connections for real-time updates
const connections = new Map<string, ReadableStreamDefaultController>();

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const orderId = params.id;

    // Verify user has access to this order
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: { user: true },
    });

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }

    // Check if user is admin or owns the order
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
    });

    if (!user || (user.role !== 'ADMIN' && order.userId !== session.user.id)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Create Server-Sent Events stream
    const stream = new ReadableStream({
      start(controller) {
        // Store connection for this order
        const connectionKey = `${orderId}-${session.user.id}`;
        connections.set(connectionKey, controller);

        // Send initial connection message
        controller.enqueue(`data: ${JSON.stringify({
          type: 'connected',
          orderId,
          timestamp: new Date().toISOString(),
        })}\n\n`);

        // Send current order status
        sendOrderUpdate(controller, order);

        // Set up heartbeat to keep connection alive
        const heartbeat = setInterval(() => {
          try {
            controller.enqueue(`data: ${JSON.stringify({
              type: 'heartbeat',
              timestamp: new Date().toISOString(),
            })}\n\n`);
          } catch (error) {
            clearInterval(heartbeat);
            connections.delete(connectionKey);
          }
        }, 30000); // 30 seconds

        // Clean up on close
        request.signal.addEventListener('abort', () => {
          clearInterval(heartbeat);
          connections.delete(connectionKey);
          try {
            controller.close();
          } catch (error) {
            // Connection already closed
          }
        });
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    });
  } catch (error) {
    console.error('Error setting up status stream:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function sendOrderUpdate(controller: ReadableStreamDefaultController, order: any) {
  try {
    controller.enqueue(`data: ${JSON.stringify({
      type: 'order_update',
      orderId: order.id,
      status: order.status,
      trackingNumber: order.trackingNumber,
      updatedAt: order.updatedAt.toISOString(),
      timestamp: new Date().toISOString(),
    })}\n\n`);
  } catch (error) {
    console.error('Error sending order update:', error);
  }
}

// Function to broadcast updates to all connected clients for an order
export function broadcastOrderUpdate(orderId: string, updateData: any) {
  const relevantConnections = Array.from(connections.entries())
    .filter(([key]) => key.startsWith(orderId));

  relevantConnections.forEach(([key, controller]) => {
    try {
      controller.enqueue(`data: ${JSON.stringify({
        type: 'order_update',
        orderId,
        ...updateData,
        timestamp: new Date().toISOString(),
      })}\n\n`);
    } catch (error) {
      console.error('Error broadcasting update:', error);
      connections.delete(key);
    }
  });
}

// Export for use in other API routes
export { connections };
