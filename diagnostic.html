<!DOCTYPE html>
<html>
<head>
    <title>RustDesk Diagnostic Tool</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        h1 {
            color: #333;
        }
        h2 {
            color: #555;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px 0;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RustDesk Diagnostic Tool</h1>
        
        <div class="card">
            <h2>Device Information</h2>
            <div id="deviceInfo"></div>
        </div>
        
        <div class="card">
            <h2>Network Tests</h2>
            <button onclick="testPort('*************', 21116)">Test Port 21116 (ID Server)</button>
            <div id="port21116Result" class="result"></div>
            
            <button onclick="testPort('*************', 21117)">Test Port 21117 (Relay Server)</button>
            <div id="port21117Result" class="result"></div>
            
            <button onclick="testPort('*************', 21118)">Test Port 21118 (WebSocket)</button>
            <div id="port21118Result" class="result"></div>
            
            <button onclick="testPort('*************', 21119)">Test Port 21119 (WebSocket Relay)</button>
            <div id="port21119Result" class="result"></div>
        </div>
        
        <div class="card">
            <h2>RustDesk Connection</h2>
            <p>Device ID: <input type="text" id="deviceId" value="1681512408"></p>
            <button onclick="connectToRustDesk()">Connect to RustDesk</button>
            <div id="rustDeskResult" class="result"></div>
        </div>
    </div>

    <script>
        // Display device information
        function showDeviceInfo() {
            const deviceInfo = document.getElementById('deviceInfo');
            deviceInfo.innerHTML = `
                <p><strong>User Agent:</strong> ${navigator.userAgent}</p>
                <p><strong>Platform:</strong> ${navigator.platform}</p>
                <p><strong>Screen Resolution:</strong> ${window.screen.width}x${window.screen.height}</p>
                <p><strong>Window Size:</strong> ${window.innerWidth}x${window.innerHeight}</p>
                <p><strong>Connection Type:</strong> ${navigator.connection ? navigator.connection.effectiveType : 'Unknown'}</p>
            `;
        }
        
        // Test if a port is open
        function testPort(host, port) {
            const resultElement = document.getElementById(`port${port}Result`);
            resultElement.innerHTML = `Testing connection to ${host}:${port}...`;
            resultElement.className = 'result';
            
            fetch(`http://${host}:${port}`, { mode: 'no-cors', timeout: 5000 })
                .then(() => {
                    resultElement.innerHTML = `✅ Port ${port} appears to be reachable`;
                    resultElement.className = 'result success';
                })
                .catch(error => {
                    resultElement.innerHTML = `❌ Error connecting to port ${port}: ${error.message}`;
                    resultElement.className = 'result error';
                });
        }
        
        // Connect to RustDesk
        function connectToRustDesk() {
            const deviceId = document.getElementById('deviceId').value.trim();
            const resultElement = document.getElementById('rustDeskResult');
            
            if (!deviceId) {
                resultElement.innerHTML = '❌ Please enter a device ID';
                resultElement.className = 'result error';
                return;
            }
            
            resultElement.innerHTML = `Attempting to connect to device ${deviceId}...`;
            
            // Try to launch RustDesk with the device ID
            const url = `rustdesk://${deviceId}?server=*************`;
            resultElement.innerHTML += `<p>Opening URL: ${url}</p>`;
            
            try {
                window.location.href = url;
                resultElement.innerHTML += `<p class="success">✅ RustDesk connection URL opened</p>`;
            } catch (error) {
                resultElement.innerHTML += `<p class="error">❌ Error opening RustDesk: ${error.message}</p>`;
            }
        }
        
        // Initialize
        window.onload = function() {
            showDeviceInfo();
        };
    </script>
</body>
</html>
