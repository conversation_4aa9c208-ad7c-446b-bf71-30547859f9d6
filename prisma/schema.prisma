generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                String              @id @default(uuid())
  email             String              @unique
  name              String?
  role              Role                @default(CLIENT)
  password          String?
  applicationPurpose String?            // What the client plans to use SPEAR for
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  blogPosts         BlogPost[]
  comments          Comment[]
  devices           Device[]
  invoices          Invoice[]
  paymentMethods    PaymentMethod[]
  subscriptions     Subscription[]
  notifications     Notification[]
  shippingAddresses ShippingAddress[]
  deviceSubmissions DeviceSubmission[]
  tradeInRequests   TradeInRequest[]
  supportTickets    SupportTicket[]
  ticketComments    TicketComment[]
  orders            Order[]             // Orders placed by this user
}

model Device {
  id            String    @id @default(uuid())
  name          String
  userId        String?
  lastCheckInId String?   @unique
  createdAt     DateTime  @default(now())
  model         String?
  password      String?
  rustDeskId    String?   @unique
  deviceType    String    @default("desktop") // "desktop" or "mobile"
  status        String    @default("pending") // "pending", "active", "inactive", "offline", "available", "assigned", "shipped", "delivered"
  updatedAt     DateTime  @updatedAt
  checkIns      CheckIn[] @relation("DeviceCheckIns")
  lastCheckIn   CheckIn?  @relation("LastCheckIn", fields: [lastCheckInId], references: [id])
  assignedTo    User?     @relation(fields: [userId], references: [id])
  orders        Order[]   // Orders that this device is assigned to
}

model Subscription {
  id                   String            @id @default(uuid())
  stripeSubId          String?           @unique
  squareSubId          String?           @unique
  paypalOrderId        String?           @unique
  paypalCaptureId      String?
  paypalSubscriptionId String?           @unique
  status               String            @default("active")
  currentPeriodEnd     DateTime          @default(now())
  userId               String
  createdAt            DateTime          @default(now())
  updatedAt            DateTime          @updatedAt
  plan                 String            @default("basic")
  planType             String?           // For Square plan variation ID
  price                String            @default("$350.00")
  paymentMethod        String            @default("stripe") // "stripe", "square", or "paypal"
  user                 User              @relation(fields: [userId], references: [id])
  shippingAddresses    ShippingAddress[]
}

model CheckIn {
  id             String   @id @default(uuid())
  deviceId       String
  latitude       Float
  longitude      Float
  createdAt      DateTime @default(now())
  device         Device   @relation("DeviceCheckIns", fields: [deviceId], references: [id])
  lastCheckInFor Device?  @relation("LastCheckIn")
}

model PaymentMethod {
  id        String   @id @default(uuid())
  userId    String
  type      String   @default("credit_card")
  brand     String?
  last4     String?
  expMonth  Int?
  expYear   Int?
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])
}

model Invoice {
  id          String   @id @default(uuid())
  userId      String
  amount      String
  status      String   @default("paid")
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id])
}

model BlogPost {
  id          String    @id @default(uuid())
  title       String
  slug        String    @unique
  excerpt     String
  content     String
  status      String    @default("draft")
  author      String
  category    String
  publishedAt DateTime?
  views       Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  userId      String
  user        User      @relation(fields: [userId], references: [id])
  comments    Comment[]
}

model Comment {
  id         String   @id @default(uuid())
  content    String
  author     String
  blogPostId String
  userId     String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  blogPost   BlogPost @relation(fields: [blogPostId], references: [id])
  user       User     @relation(fields: [userId], references: [id])
}

model Setting {
  id        String   @id @default(uuid())
  key       String   @unique
  value     String
  type      String   @default("string")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Notification {
  id        String   @id @default(uuid())
  title     String
  message   String
  type      String   // 'info', 'success', 'warning', 'error'
  userId    String?  // null means notification for all admins
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Coupon {
  id             String   @id @default(uuid())
  code           String   @unique
  discountType   String   // 'percentage' or 'fixed'
  discountValue  Float    // percentage (0-100) or fixed amount
  maxUses        Int?     // null means unlimited
  usedCount      Int      @default(0)
  validFrom      DateTime @default(now())
  validTo        DateTime?
  applicablePlans String[] // array of plan IDs this coupon applies to
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
}

model ShippingAddress {
  id             String        @id @default(uuid())
  userId         String
  subscriptionId String?
  name           String
  streetAddress  String
  city           String
  state          String
  postalCode     String
  country        String
  phoneNumber    String
  isDefault      Boolean       @default(false)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  user           User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription   Subscription? @relation(fields: [subscriptionId], references: [id])
}

model DeviceSubmission {
  id          String   @id @default(uuid())
  userId      String
  deviceName  String
  rustDeskId  String
  deviceType  String   @default("desktop")
  status      String   @default("pending") // "pending", "approved", "rejected"
  adminNotes  String?
  submittedAt DateTime @default(now())
  reviewedAt  DateTime?
  reviewedBy  String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model TradeInRequest {
  id              String   @id @default(uuid())
  userId          String
  deviceModel     String   @default("Samsung A14")
  deviceCondition String
  estimatedValue  Float
  actualValue     Float?
  status          String   @default("pending") // "pending", "shipped", "received", "processed", "completed"
  trackingNumber  String?
  rebateAmount    Float?
  rebateApplied   Boolean  @default(false)
  adminNotes      String?
  submittedAt     DateTime @default(now())
  processedAt     DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model SupportTicket {
  id          String          @id @default(uuid())
  userId      String
  subject     String
  description String
  category    String          @default("technical") // "technical", "billing", "account", "feature", "other"
  status      String          @default("open") // "open", "in_progress", "closed"
  priority    String          @default("medium") // "low", "medium", "high", "urgent"
  adminNotes  String?
  assignedTo  String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  closedAt    DateTime?
  user        User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  comments    TicketComment[]
}

model TicketComment {
  id        String        @id @default(uuid())
  ticketId  String
  userId    String
  content   String
  isAdmin   Boolean       @default(false)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  ticket    SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  user      User          @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Order {
  id                String              @id @default(uuid())
  userId            String
  subscriptionPlan  String              // "single_user" or "two_user_bundle"
  amount            Float               // Amount paid (199.00 or 298.00)
  paymentId         String?             // Square payment ID
  status            String              @default("device_prep") // "device_prep", "device_assigned", "shipped", "delivered", "connected", "active"
  shippingAddress   Json?               // JSON object with shipping details
  trackingNumber    String?
  deviceId          String?             // Assigned device ID
  notes             String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  user              User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  device            Device?             @relation(fields: [deviceId], references: [id])
  statusHistory     OrderStatusHistory[]
}

model OrderStatusHistory {
  id          String   @id @default(uuid())
  orderId     String
  status      String   // Status that was set
  notes       String?  // Optional notes about the status change
  adminUserId String?  // Admin who made the change (if applicable)
  createdAt   DateTime @default(now())
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

enum Role {
  ADMIN
  CLIENT
}
