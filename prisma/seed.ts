import { PrismaClient } from '@prisma/client';
import * as crypto from 'crypto';

const prisma = new PrismaClient();

/**
 * Hash a password using SHA-256 for seed script
 * Note: In production, we use bcrypt in the auth-utils.ts file
 * but for seeding, we use SHA-256 for compatibility
 */
async function hashPassword(password: string): Promise<string> {
  return crypto.createHash('sha256').update(password).digest('hex');
}

async function main() {
  console.log('Seeding database...');

  // Hash passwords using bcrypt
  const adminPassword = await hashPassword('password');
  const clientPassword = await hashPassword('password');

  // Create admin user if it doesn't exist
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON><PERSON>',
      password: adminPassword,
      role: 'ADMIN',
    },
  });
  console.log('Admin user created or updated:', adminUser);

  // Create client user
  const clientUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test Client',
      password: clientPassword,
      role: 'CLIENT',
    },
  });
  console.log('Client user created or updated:', clientUser);

  // Create or update the RustDesk device
  const device = await prisma.device.upsert({
    where: { rustDeskId: '1681512408' },
    update: {
      userId: clientUser.id,
      status: 'online',
    },
    create: {
      name: 'Samsung A14',
      rustDeskId: '1681512408',
      password: '82AirmaN@$',
      model: 'Android Device',
      status: 'online',
      userId: clientUser.id,
    },
  });
  console.log('Device created or updated:', device);

  // Create subscription for client user
  const subscription = await prisma.subscription.upsert({
    where: { stripeSubId: 'sub_test_client' },
    update: {},
    create: {
      stripeSubId: 'sub_test_client',
      plan: 'basic',
      status: 'active',
      price: '$350.00',
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      userId: clientUser.id,
    },
  });
  console.log('Subscription created or updated:', subscription);

  // Create payment method for client user
  const paymentMethod = await prisma.paymentMethod.upsert({
    where: { id: 'pm_test_client' },
    update: {},
    create: {
      id: 'pm_test_client',
      type: 'credit_card',
      brand: 'Visa',
      last4: '4242',
      expMonth: 12,
      expYear: 2024,
      isDefault: true,
      userId: clientUser.id,
    },
  });
  console.log('Payment method created or updated:', paymentMethod);

  // Create invoice for client user
  const invoice = await prisma.invoice.upsert({
    where: { id: 'inv_test_client' },
    update: {},
    create: {
      id: 'inv_test_client',
      amount: '$350.00',
      status: 'paid',
      description: 'Basic Plan - Monthly Subscription',
      userId: clientUser.id,
    },
  });
  console.log('Invoice created or updated:', invoice);

  // Create blog post
  const blogPost = await prisma.blogPost.upsert({
    where: { slug: 'getting-started-with-rustdesk' },
    update: {},
    create: {
      title: 'Getting Started with RustDesk',
      slug: 'getting-started-with-rustdesk',
      excerpt: 'Learn how to set up and use RustDesk for remote device management.',
      content: 'RustDesk is an open-source remote desktop software that allows you to control your devices from anywhere. In this guide, we\'ll walk you through the setup process and show you how to get the most out of RustDesk.',
      status: 'published',
      author: 'Marquise Holton',
      category: 'Tutorials',
      publishedAt: new Date(),
      views: 42,
      userId: adminUser.id,
    },
  });
  console.log('Blog post created or updated:', blogPost);

  // Create settings
  const generalSettings = [
    { key: 'general.companyName', value: 'SPEAR Technologies', type: 'string' },
    { key: 'general.supportEmail', value: '<EMAIL>', type: 'string' },
    { key: 'general.supportPhone', value: '+****************', type: 'string' },
    { key: 'general.defaultDeviceLimit', value: '5', type: 'number' },
  ];

  for (const setting of generalSettings) {
    await prisma.setting.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: setting,
    });
  }
  console.log('General settings created or updated');

  console.log('Database seeding completed.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
