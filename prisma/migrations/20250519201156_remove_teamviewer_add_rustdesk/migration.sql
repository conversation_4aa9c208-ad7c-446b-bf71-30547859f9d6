/*
  Warnings:

  - You are about to drop the column `teamViewerDeviceId` on the `Device` table. All the data in the column will be lost.
  - You are about to drop the column `teamViewerUser` on the `User` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[rustDeskId]` on the table `Device` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "Device_teamViewerDeviceId_key";

-- First, add the new columns with default values for existing rows
-- AlterTable: Add new columns to Device table with defaults
ALTER TABLE "Device"
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "model" TEXT,
ADD COLUMN     "password" TEXT,
ADD COLUMN     "rustDeskId" TEXT NOT NULL DEFAULT '1681512408', -- Default to the known RustDesk ID
ADD COLUMN     "status" TEXT NOT NULL DEFAULT 'offline',
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable: Add timestamp columns to Subscription table
ALTER TABLE "Subscription"
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable: Add timestamp columns to User table
ALTER TABLE "User"
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- Now drop the TeamViewer columns
ALTER TABLE "Device" DROP COLUMN "teamViewerDeviceId";
ALTER TABLE "User" DROP COLUMN "teamViewerUser";

-- Create the unique index for rustDeskId
CREATE UNIQUE INDEX "Device_rustDeskId_key" ON "Device"("rustDeskId");
