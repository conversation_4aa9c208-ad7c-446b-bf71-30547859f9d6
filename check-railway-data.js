const { Client } = require('pg');

async function checkRailwayData() {
  console.log('🔍 Checking Railway database for existing data...\n');
  
  // Railway connection string
  const railwayUrl = 'postgresql://postgres:<EMAIL>:31227/railway?schema=public';
  
  const client = new Client({
    connectionString: railwayUrl,
  });
  
  try {
    console.log('🔌 Connecting to Railway database...');
    await client.connect();
    console.log('✅ Connected to Railway successfully!');
    
    // Check if tables exist and count records
    const tables = ['User', 'Device', 'Subscription', 'CheckIn', 'PaymentMethod', 'Invoice', 'Setting', 'Notification', 'Coupon'];
    
    for (const table of tables) {
      try {
        const result = await client.query(`SELECT COUNT(*) as count FROM "${table}"`);
        const count = parseInt(result.rows[0].count);
        
        if (count > 0) {
          console.log(`📊 ${table}: ${count} records found`);
          
          // Show sample data for tables with records
          const sampleResult = await client.query(`SELECT * FROM "${table}" LIMIT 3`);
          console.log(`   Sample data:`, sampleResult.rows);
        } else {
          console.log(`📊 ${table}: 0 records (empty)`);
        }
      } catch (error) {
        console.log(`❌ ${table}: Table does not exist or error - ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Railway connection failed:', error.message);
    console.log('ℹ️  This is expected if Railway database is no longer accessible');
  } finally {
    await client.end();
    console.log('🔌 Railway connection closed.');
  }
}

checkRailwayData();
