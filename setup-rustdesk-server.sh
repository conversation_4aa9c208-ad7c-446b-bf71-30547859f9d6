#!/bin/bash

# SPEAR RustDesk Server Setup Script
# License: +iOXAUS4gnKPPzWl6YUqGQ== (Individual)

set -e

echo "🚀 Setting up SPEAR RustDesk Server..."
echo "License: +iOXAUS4gnKPPzWl6YUqGQ=="
echo "Type: Individual"
echo ""

# Get system info
ARCH=$(uname -m)
OS=$(uname -s)

echo "System: $OS $ARCH"

# Create directories
mkdir -p ~/spear-rustdesk-server/{data,logs,scripts}
cd ~/spear-rustdesk-server

# Get public IP
echo "🌐 Getting your public IP address..."
PUBLIC_IP=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo "127.0.0.1")
echo "Public IP: $PUBLIC_IP"

# Get local IP
LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
echo "Local IP: $LOCAL_IP"

# Download RustDesk Server binaries
echo "📥 Downloading RustDesk Server binaries..."

if [[ "$OS" == "Darwin" ]]; then
    # macOS - use Linux binaries with Docker or try direct download
    echo "macOS detected. We'll use Docker for compatibility."
    
    # Create a simple docker-compose for quick setup
    cat > docker-compose-simple.yml << EOF
services:
  hbbs:
    image: rustdesk/rustdesk-server:latest
    container_name: spear-hbbs
    ports:
      - "21115:21115"
      - "21116:21116"
      - "21116:21116/udp"
      - "21118:21118"
    volumes:
      - ./data:/root
    restart: unless-stopped
    command: hbbs -r $PUBLIC_IP:21117

  hbbr:
    image: rustdesk/rustdesk-server:latest
    container_name: spear-hbbr
    ports:
      - "21117:21117"
    volumes:
      - ./data:/root
    restart: unless-stopped
    command: hbbr
EOF

    echo "✅ Docker Compose file created: docker-compose-simple.yml"
    echo ""
    echo "To start the server, run:"
    echo "  cd ~/spear-rustdesk-server"
    echo "  docker compose -f docker-compose-simple.yml up -d"
    
else
    # Linux
    echo "Linux detected. Downloading binaries..."
    
    if [[ "$ARCH" == "x86_64" ]]; then
        DOWNLOAD_URL="https://github.com/rustdesk/rustdesk-server/releases/latest/download/rustdesk-server-linux-amd64.tar.gz"
    elif [[ "$ARCH" == "aarch64" ]]; then
        DOWNLOAD_URL="https://github.com/rustdesk/rustdesk-server/releases/latest/download/rustdesk-server-linux-arm64.tar.gz"
    else
        echo "❌ Unsupported architecture: $ARCH"
        exit 1
    fi
    
    curl -L "$DOWNLOAD_URL" -o rustdesk-server.tar.gz
    tar -xzf rustdesk-server.tar.gz
    chmod +x hbbs hbbr
    
    echo "✅ Binaries downloaded and extracted"
fi

# Generate encryption keys
echo "🔐 Generating encryption keys..."
if [[ -f "./hbbs" ]]; then
    ./hbbs --key
else
    echo "Will generate keys when Docker containers start"
fi

# Create startup scripts
echo "📝 Creating startup scripts..."

# HBBS (ID Server) startup script
cat > scripts/start-hbbs.sh << EOF
#!/bin/bash
cd ~/spear-rustdesk-server
echo "Starting SPEAR RustDesk ID Server (HBBS)..."
echo "Public IP: $PUBLIC_IP"
echo "Local IP: $LOCAL_IP"
./hbbs -r $PUBLIC_IP:21117 -k ./data/id_ed25519
EOF

# HBBR (Relay Server) startup script
cat > scripts/start-hbbr.sh << EOF
#!/bin/bash
cd ~/spear-rustdesk-server
echo "Starting SPEAR RustDesk Relay Server (HBBR)..."
./hbbr -k ./data/id_ed25519
EOF

chmod +x scripts/*.sh

# Create systemd service files (Linux only)
if [[ "$OS" == "Linux" ]]; then
    echo "🔧 Creating systemd service files..."
    
    # HBBS Service
    sudo tee /etc/systemd/system/spear-rustdesk-hbbs.service > /dev/null << EOF
[Unit]
Description=SPEAR RustDesk ID Server (HBBS)
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$HOME/spear-rustdesk-server
ExecStart=$HOME/spear-rustdesk-server/hbbs -r $PUBLIC_IP:21117 -k $HOME/spear-rustdesk-server/data/id_ed25519
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

    # HBBR Service
    sudo tee /etc/systemd/system/spear-rustdesk-hbbr.service > /dev/null << EOF
[Unit]
Description=SPEAR RustDesk Relay Server (HBBR)
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$HOME/spear-rustdesk-server
ExecStart=$HOME/spear-rustdesk-server/hbbr -k $HOME/spear-rustdesk-server/data/id_ed25519
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl daemon-reload
    
    echo "✅ Systemd services created"
    echo ""
    echo "To start services:"
    echo "  sudo systemctl enable spear-rustdesk-hbbs spear-rustdesk-hbbr"
    echo "  sudo systemctl start spear-rustdesk-hbbs spear-rustdesk-hbbr"
fi

# Create configuration summary
cat > SPEAR_RUSTDESK_CONFIG.md << EOF
# SPEAR RustDesk Server Configuration

## Server Details
- **License**: +iOXAUS4gnKPPzWl6YUqGQ== (Individual)
- **Public IP**: $PUBLIC_IP
- **Local IP**: $LOCAL_IP
- **Installation Path**: ~/spear-rustdesk-server

## Ports Used
- **21115**: NAT type test
- **21116**: ID registration and heartbeat (TCP/UDP)
- **21117**: Relay services
- **21118**: Web client support

## For Spear Application
Update your Spear .env file with:

\`\`\`
RUSTDESK_SERVER_HOST=$PUBLIC_IP
RUSTDESK_SERVER_PORT=21116
RUSTDESK_RELAY_HOST=$PUBLIC_IP
RUSTDESK_RELAY_PORT=21117
RUSTDESK_WEB_CLIENT_URL=http://$PUBLIC_IP:21118
\`\`\`

## Client Configuration
Configure RustDesk clients to use:
- **ID Server**: $PUBLIC_IP:21116
- **Relay Server**: $PUBLIC_IP:21117
- **Key**: (will be generated in ./data/id_ed25519.pub)

## Firewall Rules Needed
\`\`\`bash
# Allow RustDesk ports
sudo ufw allow 21115
sudo ufw allow 21116
sudo ufw allow 21117
sudo ufw allow 21118
\`\`\`

## Router Port Forwarding
Forward these ports from your router to $LOCAL_IP:
- 21115 → $LOCAL_IP:21115
- 21116 → $LOCAL_IP:21116 (TCP & UDP)
- 21117 → $LOCAL_IP:21117
- 21118 → $LOCAL_IP:21118
EOF

echo ""
echo "🎉 SPEAR RustDesk Server setup complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Check the configuration: cat SPEAR_RUSTDESK_CONFIG.md"
echo "2. Configure your router port forwarding"
echo "3. Update your Spear application environment variables"
echo "4. Start the server using the appropriate method for your OS"
echo ""
echo "📁 Files created:"
echo "  - SPEAR_RUSTDESK_CONFIG.md (configuration summary)"
echo "  - scripts/start-hbbs.sh (ID server startup)"
echo "  - scripts/start-hbbr.sh (relay server startup)"
if [[ "$OS" == "Darwin" ]]; then
    echo "  - docker-compose-simple.yml (Docker setup)"
else
    echo "  - systemd service files (auto-start on boot)"
fi
echo ""
echo "🔑 Your RustDesk Pro License: +iOXAUS4gnKPPzWl6YUqGQ=="
