const crypto = require('crypto');

/**
 * Hash a password using SHA-256 (same method as auth-utils.ts)
 */
async function hashPasswordSha256(password) {
  // Use Node.js crypto (equivalent to Web Crypto API)
  const hash = crypto.createHash('sha256');
  hash.update(password);
  return hash.digest('hex');
}

async function generateCorrectHash() {
  const password = 'password';
  
  try {
    const hash = await hashPasswordSha256(password);
    console.log('🔧 SHA-256 hash for password "password" (using same method as app):');
    console.log(hash);
    console.log('');
    console.log('🎯 SQL Command:');
    console.log(`UPDATE "User" SET password = '${hash}' WHERE email = '<EMAIL>';`);
    
    // Also try to create a bcrypt-like hash manually
    console.log('');
    console.log('🔧 Alternative: Try setting role to ADMIN as well:');
    console.log(`UPDATE "User" SET password = '${hash}', role = 'ADMIN' WHERE email = '<EMAIL>';`);
    
    return hash;
  } catch (error) {
    console.error('Error generating hash:', error);
  }
}

generateCorrectHash();
