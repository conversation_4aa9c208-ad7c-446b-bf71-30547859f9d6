#!/bin/bash

# SPEAR RustDesk Security Configuration
echo "🔒 Configuring RustDesk Security..."

# 1. Generate strong encryption keys
echo "🔑 Generating encryption keys..."
openssl genpkey -algorithm Ed25519 -out id_ed25519
openssl pkey -in id_ed25519 -pubout -out id_ed25519.pub

# 2. Set proper file permissions
chmod 600 id_ed25519
chmod 644 id_ed25519.pub

# 3. Configure fail2ban for brute force protection
if command -v fail2ban-client &> /dev/null; then
    echo "🛡️ Configuring fail2ban..."
    cat > /etc/fail2ban/jail.d/rustdesk.conf << 'EOF'
[rustdesk]
enabled = true
port = 21115,21116,21117,21118,21119
protocol = tcp
filter = rustdesk
logpath = /var/log/rustdesk/*.log
maxretry = 5
bantime = 3600
findtime = 600
EOF
    systemctl restart fail2ban
fi

# 4. Setup log rotation
echo "📝 Setting up log rotation..."
cat > /etc/logrotate.d/rustdesk << 'EOF'
/var/log/rustdesk/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 rustdesk rustdesk
    postrotate
        systemctl reload rustdesk-hbbs rustdesk-hbbr
    endscript
}
EOF

# 5. Create monitoring script
cat > /usr/local/bin/rustdesk-monitor.sh << 'EOF'
#!/bin/bash
# Monitor RustDesk services and restart if needed

check_service() {
    if ! systemctl is-active --quiet $1; then
        echo "$(date): $1 is down, restarting..." >> /var/log/rustdesk/monitor.log
        systemctl restart $1
    fi
}

check_service rustdesk-hbbs
check_service rustdesk-hbbr

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is ${DISK_USAGE}%" >> /var/log/rustdesk/monitor.log
fi
EOF

chmod +x /usr/local/bin/rustdesk-monitor.sh

# 6. Add to crontab for monitoring
(crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/rustdesk-monitor.sh") | crontab -

echo "✅ Security configuration complete!"
echo "🔍 Monitor logs: tail -f /var/log/rustdesk/monitor.log"
