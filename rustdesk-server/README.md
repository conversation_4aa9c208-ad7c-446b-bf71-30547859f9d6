# 🚀 SPEAR RustDesk Self-Hosted Server

## 📋 Quick Start

This directory contains everything needed to deploy a self-hosted RustDesk server for SPEAR, providing faster and more reliable connections for clients.

## 🎯 Why Self-Host?

- **2-5x faster** connection establishment vs public servers
- **Better reliability** - no dependency on overloaded public servers  
- **Enhanced security** with private encryption keys
- **Full control** over server configuration and performance

## 📁 Files Overview

| File | Purpose |
|------|---------|
| `DEPLOYMENT_GUIDE.md` | Comprehensive setup instructions |
| `docker-compose.yml` | Docker deployment configuration |
| `vps-install.sh` | Automated VPS installation script |
| `railway-deploy.sh` | Railway deployment script |
| `security-config.sh` | Security hardening configuration |

## ⚡ Quick Deploy

### Option 1: DigitalOcean VPS (Recommended)

```bash
# 1. Create Ubuntu 22.04 droplet (2 CPU, 2GB RAM)
# 2. SSH into server
ssh root@your-server-ip

# 3. Run installation
wget https://raw.githubusercontent.com/your-repo/spear/main/rustdesk-server/vps-install.sh
chmod +x vps-install.sh
./vps-install.sh

# 4. Get server key
cat /root/id_ed25519.pub
```

### Option 2: Railway

```bash
cd rustdesk-server
npm install -g @railway/cli
./railway-deploy.sh
```

## 🔧 SPEAR Integration

After deployment, update your SPEAR environment variables:

```env
RUSTDESK_SERVER_IP=rustdesk.spear-app.com
RUSTDESK_SERVER_PORT=21116
RUSTDESK_SERVER_KEY=your-server-key-here
```

## 📊 Benefits for Sidney & Future Clients

- **Faster connections** - No more waiting for public server availability
- **Consistent performance** - Dedicated server resources
- **Better support** - Full control over server configuration
- **Enhanced privacy** - Private encryption keys

## 🔍 Monitoring

Check server status:
```bash
./check_rustdesk.sh
```

View logs:
```bash
journalctl -u rustdesk-hbbs -f
journalctl -u rustdesk-hbbr -f
```

## 📞 Support

For deployment assistance or troubleshooting, refer to the comprehensive `DEPLOYMENT_GUIDE.md` or contact SPEAR support.

---

**Ready to deploy?** Start with the `DEPLOYMENT_GUIDE.md` for detailed instructions! 🎉
