{"custom_client_config": {"display_name": "SPEAR Remote Access", "about": "Secure remote access for SPEAR customers", "override_settings": {"id-server": "**************:21116", "relay-server": "**************:21117", "api-server": "**************:21114", "key": "61PhlnkYifqNNk1NgHvgKeCfrsILb8TULTiRtk8ZXho=", "disable-other-servers": true, "hide-network-settings": true, "disable-update": true, "disable-installation": false, "disable-auto-login": false, "disable-ab": true, "disable-group-panel": true, "disable-server-settings": true, "disable-change-id": true, "disable-tcp-tunneling": true, "disable-direct-ip-access": true, "disable-lan-discovery": true}, "ui_customization": {"logo": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==", "brand_name": "SPEAR", "company_name": "SPEAR Remote Access", "hide_about": false, "hide_help": false, "custom_about_text": "SPEAR Remote Access - Secure device management for subscribers only.", "theme": {"primary_color": "#2563eb", "secondary_color": "#1e40af", "background_color": "#f8fafc", "text_color": "#1e293b"}}, "security_settings": {"force_encryption": true, "require_password": true, "disable_clipboard": false, "disable_file_transfer": false, "disable_audio": false, "session_timeout": 7200, "max_sessions": 1}, "restrictions": {"allowed_domains": ["spear-app.com", "**************"], "blocked_domains": ["rustdesk.com", "public.rustdesk.com"], "whitelist_mode": true, "disable_public_servers": true}}, "deployment_instructions": {"android": {"build_command": "flutter build apk --release --build-name=SPEAR-1.0.0 --build-number=1", "signing": "Sign with SPEAR keystore", "distribution": "Upload to SPEAR admin portal for device provisioning"}, "windows": {"build_command": "cargo build --release --features custom", "installer": "Create MSI with custom branding", "distribution": "Download link in customer portal"}, "macos": {"build_command": "cargo build --release --target x86_64-apple-darwin", "signing": "Sign with Apple Developer certificate", "distribution": "DMG file in customer portal"}}, "server_strategy": {"name": "SPEAR_LOCKDOWN", "description": "Locks down RustDesk clients to SPEAR infrastructure only", "settings": {"disable_settings_ui": true, "disable_server_change": true, "disable_lan_discovery": true, "force_relay": true, "audit_connections": true, "require_approval": false, "session_recording": false}, "apply_to": "all_devices"}}