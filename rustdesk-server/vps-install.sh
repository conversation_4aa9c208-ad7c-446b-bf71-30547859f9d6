#!/bin/bash

# SPEAR RustDesk Pro Server Production Installation Script
# Optimized for DigitalOcean VPS deployment with Pro license
# Tested on Ubuntu 20.04/22.04, CentOS 7/8, Debian

set -e  # Exit on any error

echo "🚀 Installing SPEAR RustDesk Pro Server for Production..."
echo "📋 License: +iOXAUS4gnKPPzWl6YUqGQ=="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root (use sudo)"
   exit 1
fi

# Set variables
SPEAR_LICENSE="+iOXAUS4gnKPPzWl6YUqGQ=="
PUBLIC_IP=$(curl -s ifconfig.me)
DOMAIN_NAME="${DOMAIN_NAME:-$PUBLIC_IP}"  # Use IP if no domain provided
INSTALL_DIR="/opt/rustdesk"
LOG_FILE="/var/log/spear-rustdesk-install.log"

echo "🌐 Server will be accessible at: $DOMAIN_NAME"

# Create log file
touch $LOG_FILE
exec 1> >(tee -a $LOG_FILE)
exec 2> >(tee -a $LOG_FILE >&2)

echo "📝 Installation log: $LOG_FILE"

# Update system
echo "📦 Updating system packages..."
if command -v apt &> /dev/null; then
    apt update && apt upgrade -y
    apt install -y curl wget ufw docker.io docker-compose nginx certbot python3-certbot-nginx htop iftop net-tools
    systemctl enable docker
    systemctl start docker
elif command -v yum &> /dev/null; then
    yum update -y
    yum install -y curl wget firewalld docker docker-compose nginx certbot python3-certbot-nginx htop iftop net-tools
    systemctl enable docker
    systemctl start docker
fi

# Configure firewall for production
echo "🔥 Configuring production firewall..."
if command -v ufw &> /dev/null; then
    # UFW (Ubuntu/Debian)
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing
    ufw allow 22/tcp comment 'SSH'
    ufw allow 80/tcp comment 'HTTP'
    ufw allow 443/tcp comment 'HTTPS'
    ufw allow 21114:21119/tcp comment 'RustDesk TCP'
    ufw allow 21116/udp comment 'RustDesk UDP'
    ufw --force enable
elif command -v firewall-cmd &> /dev/null; then
    # FirewallD (CentOS/RHEL)
    firewall-cmd --permanent --add-service=ssh
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --permanent --add-port=21114-21119/tcp
    firewall-cmd --permanent --add-port=21116/udp
    firewall-cmd --reload
fi

# Create installation directory
mkdir -p $INSTALL_DIR
cd $INSTALL_DIR

# Download RustDesk Pro server
echo "⬇️ Downloading RustDesk Pro server..."
wget -O docker-compose.yml https://rustdesk.com/pro.yml

# Configure RustDesk Pro with license
echo "🔧 Configuring RustDesk Pro server..."

# Set up environment variables for Pro license
cat > .env << EOF
RUSTDESK_LICENSE=$SPEAR_LICENSE
DOMAIN=$DOMAIN_NAME
EOF

# Start RustDesk Pro server
echo "🚀 Starting RustDesk Pro server..."
docker-compose up -d

# Wait for services to start
echo "⏳ Waiting for services to initialize..."
sleep 30

# Get the server key
echo "🔑 Extracting server key..."
SERVER_KEY=""
if [ -f "./data/id_ed25519.pub" ]; then
    SERVER_KEY=$(cat ./data/id_ed25519.pub)
elif docker exec rustdesk-hbbs cat /root/id_ed25519.pub 2>/dev/null; then
    SERVER_KEY=$(docker exec rustdesk-hbbs cat /root/id_ed25519.pub)
fi

# Configure Nginx reverse proxy for web console
echo "🌐 Configuring Nginx reverse proxy..."
cat > /etc/nginx/sites-available/rustdesk << EOF
server {
    listen 80;
    server_name $DOMAIN_NAME;

    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN_NAME;

    # SSL configuration (will be configured by certbot)
    ssl_certificate /etc/letsencrypt/live/$DOMAIN_NAME/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN_NAME/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # Web console proxy
    location / {
        proxy_pass http://localhost:21114;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # API endpoints
    location /api/ {
        proxy_pass http://localhost:21114/api/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/rustdesk /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
nginx -t

# Set up SSL certificate (only if domain is provided)
if [[ "$DOMAIN_NAME" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo "⚠️  Using IP address - SSL certificate skipped"
    echo "   Web console will be available at: http://$DOMAIN_NAME:21114"
    # Remove SSL configuration from nginx
    sed -i 's/listen 443 ssl http2;/listen 80;/' /etc/nginx/sites-available/rustdesk
    sed -i '/ssl_certificate/d' /etc/nginx/sites-available/rustdesk
    sed -i '/add_header/d' /etc/nginx/sites-available/rustdesk
    sed -i '/return 301/d' /etc/nginx/sites-available/rustdesk
else
    echo "🔒 Setting up SSL certificate for domain: $DOMAIN_NAME"
    certbot --nginx -d $DOMAIN_NAME --non-interactive --agree-tos --email <EMAIL>
fi

# Restart nginx
systemctl restart nginx

# Create comprehensive status check script
cat > /usr/local/bin/spear-rustdesk-status << 'EOF'
#!/bin/bash
echo "🔍 SPEAR RustDesk Pro Server Status"
echo "=================================="
echo ""

# Docker containers
echo "📦 Docker Containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(rustdesk|hbbs|hbbr)"
echo ""

# Service ports
echo "🔌 Service Ports:"
netstat -tlnp | grep -E ":(21114|21115|21116|21117|21118|21119)" | awk '{print $1 "\t" $4 "\t" $7}'
echo ""

# System resources
echo "📊 System Resources:"
echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)% used"
echo "Memory: $(free -h | awk '/^Mem:/ {print $3 "/" $2 " (" $5 " used)"}')"
echo "Disk: $(df -h / | awk 'NR==2 {print $3 "/" $2 " (" $5 " used)"}')"
echo ""

# SSL certificate
echo "🔒 SSL Certificate:"
if [ -f "/etc/letsencrypt/live/rustdesk.spear-app.com/fullchain.pem" ]; then
    EXPIRY=$(openssl x509 -enddate -noout -in /etc/letsencrypt/live/rustdesk.spear-app.com/fullchain.pem | cut -d= -f2)
    echo "Valid until: $EXPIRY"
else
    echo "❌ SSL certificate not found"
fi
echo ""

# Web console test
echo "🌐 Web Console Test:"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:21114 || echo "000")
if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "302" ]; then
    echo "✅ Web console responding (HTTP $HTTP_STATUS)"
else
    echo "❌ Web console not responding (HTTP $HTTP_STATUS)"
fi

# License status
echo ""
echo "📄 License Status:"
if docker logs rustdesk-hbbs 2>&1 | grep -q "license.*valid\|license.*ok"; then
    echo "✅ Pro license active"
else
    echo "⚠️  License status unknown - check logs"
fi
EOF

chmod +x /usr/local/bin/spear-rustdesk-status

# Create systemd service for auto-restart
cat > /etc/systemd/system/spear-rustdesk.service << 'EOF'
[Unit]
Description=SPEAR RustDesk Pro Server
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/rustdesk
ExecStart=/usr/bin/docker-compose up -d
ExecStop=/usr/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable spear-rustdesk.service

# Get server information
PUBLIC_IP=$(curl -s ifconfig.me)
echo ""
echo "🎉 SPEAR RustDesk Pro Server Installation Complete!"
echo "=================================================="
echo ""
echo "🌐 Server Details:"
echo "   Domain: https://$DOMAIN_NAME"
echo "   Public IP: $PUBLIC_IP"
echo "   License: $SPEAR_LICENSE"
echo ""
echo "🔑 Server Key (save this for SPEAR configuration):"
echo "$SERVER_KEY"
echo ""
echo "🔧 Service Ports:"
echo "   Web Console: https://$DOMAIN_NAME (port 443)"
echo "   ID Server: $DOMAIN_NAME:21116"
echo "   Relay Server: $DOMAIN_NAME:21117"
echo ""
echo "📋 Next Steps:"
echo "   1. Update SPEAR environment variables with the server key above"
echo "   2. Configure DNS: Point $DOMAIN_NAME to $PUBLIC_IP"
echo "   3. Test web console: https://$DOMAIN_NAME"
echo "   4. Run 'spear-rustdesk-status' to check server health"
echo ""
echo "🔍 Management Commands:"
echo "   Status: spear-rustdesk-status"
echo "   Logs: docker logs rustdesk-hbbs"
echo "   Restart: systemctl restart spear-rustdesk"
echo ""
echo "✅ Your SPEAR RustDesk Pro server is now ready for production!"
