#!/bin/bash

# SPEAR RustDesk Pro Deployment - IP Address Only (No Domain Required)
# This script deploys RustDesk Pro server using only IP address

set -e

echo "🚀 SPEAR RustDesk Pro Deployment (IP Address Only)"
echo "================================================"

# Configuration
DROPLET_NAME="spear-rustdesk-pro"
DROPLET_SIZE="s-2vcpu-2gb"
DROPLET_IMAGE="ubuntu-22-04-x64"
DROPLET_REGION="nyc1"
SSH_KEY_NAME="spear-deployment"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if doctl is installed
check_doctl() {
    if ! command -v doctl &> /dev/null; then
        print_error "DigitalOcean CLI (doctl) is not installed"
        echo ""
        echo "Install options:"
        echo "1. macOS: brew install doctl"
        echo "2. Linux: snap install doctl"
        echo "3. Windows: Download from https://github.com/digitalocean/doctl/releases"
        echo ""
        echo "Then authenticate: doctl auth init"
        exit 1
    fi
    
    # Check if authenticated
    if ! doctl account get &> /dev/null; then
        print_error "DigitalOcean CLI is not authenticated"
        echo ""
        echo "To authenticate:"
        echo "1. Go to https://cloud.digitalocean.com/account/api/tokens"
        echo "2. Generate a new token with Read+Write scope"
        echo "3. Run: doctl auth init"
        echo "4. Paste your token when prompted"
        exit 1
    fi
    
    print_success "DigitalOcean CLI is ready"
}

# Create SSH key if it doesn't exist
setup_ssh_key() {
    print_status "Setting up SSH key..."
    
    # Check if SSH key exists locally
    if [ ! -f ~/.ssh/id_rsa ]; then
        print_status "Generating SSH key..."
        ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
    fi
    
    # Check if key exists in DigitalOcean
    if ! doctl compute ssh-key list --format Name --no-header | grep -q "$SSH_KEY_NAME"; then
        print_status "Adding SSH key to DigitalOcean..."
        doctl compute ssh-key import $SSH_KEY_NAME --public-key-file ~/.ssh/id_rsa.pub
    fi
    
    print_success "SSH key is ready"
}

# Create DigitalOcean droplet
create_droplet() {
    print_status "Creating DigitalOcean droplet..."
    
    # Check if droplet already exists
    if doctl compute droplet list --format Name --no-header | grep -q "$DROPLET_NAME"; then
        print_warning "Droplet '$DROPLET_NAME' already exists"
        DROPLET_IP=$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')
        print_status "Using existing droplet IP: $DROPLET_IP"
    else
        print_status "Creating new droplet..."
        doctl compute droplet create $DROPLET_NAME \
            --size $DROPLET_SIZE \
            --image $DROPLET_IMAGE \
            --region $DROPLET_REGION \
            --ssh-keys $(doctl compute ssh-key list --format ID --no-header | grep -v "^$" | head -1) \
            --wait
        
        DROPLET_IP=$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')
        print_success "Droplet created with IP: $DROPLET_IP"
    fi
}

# Deploy RustDesk server
deploy_server() {
    print_status "Deploying RustDesk Pro server..."
    
    # Wait for SSH to be ready
    print_status "Waiting for SSH to be ready..."
    while ! ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no root@$DROPLET_IP "echo 'SSH ready'" &> /dev/null; do
        sleep 5
        echo -n "."
    done
    echo ""
    
    # Copy installation script
    print_status "Copying installation script..."
    scp -o StrictHostKeyChecking=no vps-install.sh root@$DROPLET_IP:/root/
    
    # Run installation without domain (IP only)
    print_status "Running installation (this may take 5-10 minutes)..."
    ssh -o StrictHostKeyChecking=no root@$DROPLET_IP "chmod +x /root/vps-install.sh && DOMAIN_NAME=$DROPLET_IP /root/vps-install.sh"
    
    print_success "Server deployment complete!"
}

# Get server information
get_server_info() {
    print_status "Retrieving server information..."
    
    # Get server key
    SERVER_KEY=$(ssh -o StrictHostKeyChecking=no root@$DROPLET_IP "cat /opt/rustdesk/data/id_ed25519.pub 2>/dev/null || docker exec rustdesk-hbbs cat /root/id_ed25519.pub 2>/dev/null || echo 'Key not found'")
    
    echo ""
    print_success "🎉 SPEAR RustDesk Pro Server Deployed Successfully!"
    echo "=================================================="
    echo ""
    echo "🌐 Server Details:"
    echo "   IP Address: $DROPLET_IP"
    echo "   Web Console: http://$DROPLET_IP:21114"
    echo "   SSH Access: ssh root@$DROPLET_IP"
    echo ""
    echo "🔑 Server Key (copy this for SPEAR configuration):"
    echo "$SERVER_KEY"
    echo ""
    echo "📋 Environment Variables for SPEAR:"
    echo "RUSTDESK_SERVER_IP=$DROPLET_IP"
    echo "RUSTDESK_SERVER_PORT=21116"
    echo "RUSTDESK_RELAY_PORT=21117"
    echo "RUSTDESK_API_PORT=21114"
    echo "RUSTDESK_SERVER_KEY=$SERVER_KEY"
    echo ""
    echo "NEXT_PUBLIC_RUSTDESK_SERVER_IP=$DROPLET_IP"
    echo "NEXT_PUBLIC_RUSTDESK_SERVER_PORT=21116"
    echo "NEXT_PUBLIC_RUSTDESK_RELAY_PORT=21117"
    echo "NEXT_PUBLIC_RUSTDESK_API_PORT=21114"
    echo "NEXT_PUBLIC_RUSTDESK_SERVER_KEY=$SERVER_KEY"
    echo ""
    echo "🔧 Management Commands (run on server):"
    echo "   Status: ssh root@$DROPLET_IP 'spear-rustdesk-status'"
    echo "   Logs: ssh root@$DROPLET_IP 'docker logs rustdesk-hbbs'"
    echo "   Restart: ssh root@$DROPLET_IP 'systemctl restart spear-rustdesk'"
    echo ""
    echo "🌐 Web Console: http://$DROPLET_IP:21114"
    echo "   Default login: admin / test1234"
    echo "   ⚠️  Change the password immediately!"
    echo ""
    echo "⚠️  Note: Using HTTP (not HTTPS) since no domain is configured"
    echo "   This is fine for testing, but consider getting a domain for production"
    echo ""
}

# Main deployment flow
main() {
    echo "This script will deploy RustDesk Pro server using IP address only:"
    echo "✅ No domain name required"
    echo "✅ No DNS configuration needed"
    echo "✅ HTTP access (not HTTPS)"
    echo "💰 Cost: ~$12/month"
    echo ""
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
    
    check_doctl
    setup_ssh_key
    create_droplet
    deploy_server
    get_server_info
    
    print_success "Deployment complete! 🎉"
    echo ""
    echo "Next steps:"
    echo "1. Update SPEAR environment variables with the values above"
    echo "2. Test the web console at http://$DROPLET_IP:21114"
    echo "3. Configure client devices to use the new server"
    echo "4. Consider getting a domain name for HTTPS in production"
}

# Run main function
main "$@"
