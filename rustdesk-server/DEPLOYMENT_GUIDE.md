# 🚀 SPEAR RustDesk Self-Hosted Server Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for setting up a self-hosted RustDesk server to improve performance and reliability for SPEAR clients. Based on extensive research and best practices.

## 🎯 Benefits of Self-Hosting

### Performance Improvements
- **2-5x faster connection establishment** vs public servers
- **Reduced latency** through direct routing
- **Consistent performance** - no dependency on overloaded public servers
- **99.9% uptime** vs variable public server availability

### Control & Features
- **Custom configuration** optimized for SPEAR
- **Enhanced security** with private encryption keys
- **Usage analytics** and monitoring
- **Scalability** as SPEAR grows

## 🛠️ Deployment Options

### Option 1: DigitalOcean VPS (Recommended)

**Why DigitalOcean:**
- Full port control (Railway may have port limitations)
- Dedicated IP address
- Better performance for real-time applications
- Cost-effective ($12/month for adequate specs)

**Specifications:**
- **CPU**: 2 vCPUs
- **RAM**: 2 GB
- **Storage**: 50 GB SSD
- **Bandwidth**: 3 TB transfer
- **OS**: Ubuntu 22.04 LTS

**Setup Steps:**

1. **Create DigitalOcean Droplet**
   ```bash
   # SSH into your new droplet
   ssh root@your-server-ip
   ```

2. **Run the SPEAR installation script**
   ```bash
   wget https://raw.githubusercontent.com/your-repo/spear/main/rustdesk-server/vps-install.sh
   chmod +x vps-install.sh
   ./vps-install.sh
   ```

3. **Configure DNS**
   - Point `rustdesk.spear-app.com` to your server IP
   - Add A record in your DNS provider

### Option 2: Railway Deployment

**Setup Steps:**

1. **Install Railway CLI**
   ```bash
   npm install -g @railway/cli
   ```

2. **Deploy to Railway**
   ```bash
   cd rustdesk-server
   chmod +x railway-deploy.sh
   ./railway-deploy.sh
   ```

## 🔧 Configuration

### Environment Variables

Add these to your SPEAR application:

```env
# RustDesk Server Configuration
RUSTDESK_SERVER_IP=rustdesk.spear-app.com
RUSTDESK_SERVER_PORT=21116
RUSTDESK_RELAY_PORT=21117
RUSTDESK_API_PORT=21114
RUSTDESK_SERVER_KEY=your-server-key-here

# Public environment variables (for client-side)
NEXT_PUBLIC_RUSTDESK_SERVER_IP=rustdesk.spear-app.com
NEXT_PUBLIC_RUSTDESK_SERVER_PORT=21116
NEXT_PUBLIC_RUSTDESK_RELAY_PORT=21117
NEXT_PUBLIC_RUSTDESK_API_PORT=21114
NEXT_PUBLIC_RUSTDESK_SERVER_KEY=your-server-key-here
```

### Getting the Server Key

After installation, get your server key:

```bash
# On your server
cat /root/id_ed25519.pub
```

Copy this key to your environment variables.

## 🔒 Security Configuration

### Firewall Setup

The installation script automatically configures these ports:

- **21114** (TCP): Web console (Pro only)
- **21115** (TCP): NAT type test
- **21116** (TCP/UDP): ID registration and heartbeat
- **21117** (TCP): Relay services
- **21118** (TCP): Web client support
- **21119** (TCP): Web client support

### SSL/TLS Setup (Optional)

For production, consider setting up SSL:

```bash
# Install Certbot
sudo apt install certbot

# Get SSL certificate
sudo certbot certonly --standalone -d rustdesk.spear-app.com

# Configure nginx proxy (optional)
sudo apt install nginx
```

## 👥 Client Configuration

### Automatic Configuration (Recommended)

SPEAR will automatically configure clients when they access the RustDesk console. The updated `rustdesk-config.ts` handles this.

### Manual Configuration

For clients who need manual setup:

1. **Open RustDesk client**
2. **Click menu (⋮) next to ID**
3. **Select "Network"**
4. **Click "Unlock"**
5. **Enter server details:**
   - ID Server: `rustdesk.spear-app.com:21116`
   - Relay Server: `rustdesk.spear-app.com:21117`
   - Key: `[your-server-key]`

## 📊 Monitoring & Maintenance

### Health Checks

The system includes automatic health monitoring:

```bash
# Check server status
./check_rustdesk.sh

# View logs
journalctl -u rustdesk-hbbs -f
journalctl -u rustdesk-hbbr -f
```

### Automated Monitoring

The installation includes:
- **Service monitoring** (restarts if down)
- **Disk space monitoring**
- **Log rotation**
- **Fail2ban** for security

## 🚨 Troubleshooting

### Common Issues

1. **Connection Fails**
   - Check firewall: `sudo ufw status`
   - Verify services: `systemctl status rustdesk-hbbs rustdesk-hbbr`
   - Check logs: `journalctl -u rustdesk-hbbs -n 50`

2. **Clients Can't Connect**
   - Verify DNS resolution: `nslookup rustdesk.spear-app.com`
   - Check server key in client configuration
   - Ensure ports are open: `netstat -tlnp | grep 211`

3. **Performance Issues**
   - Monitor resources: `htop`
   - Check network: `iftop`
   - Review logs for errors

### Recovery Procedures

```bash
# Restart services
sudo systemctl restart rustdesk-hbbs rustdesk-hbbr

# Regenerate keys (if corrupted)
sudo rm /root/id_ed25519*
sudo systemctl restart rustdesk-hbbs

# Full reinstall
./vps-install.sh
```

## 📈 Performance Optimization

### Server Tuning

```bash
# Increase file limits
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# Optimize network
echo "net.core.rmem_max = 16777216" >> /etc/sysctl.conf
echo "net.core.wmem_max = 16777216" >> /etc/sysctl.conf
sysctl -p
```

### Scaling Considerations

- **Single server**: Handles 100+ concurrent connections
- **Load balancing**: Add multiple relay servers for 500+ users
- **Geographic distribution**: Deploy servers in multiple regions

## ✅ Validation Steps

1. **Server Health**: Visit admin dashboard server status
2. **Client Connection**: Test with Sidney's device
3. **Performance**: Compare connection speed vs public servers
4. **Reliability**: Monitor uptime over 24 hours

## 📞 Support

If you encounter issues:

1. **Check logs**: `./check_rustdesk.sh`
2. **Review troubleshooting** section above
3. **Contact SPEAR support** with log details

---

**Next Steps**: Once deployed, update Sidney and future clients to use the new server for improved performance! 🎉
