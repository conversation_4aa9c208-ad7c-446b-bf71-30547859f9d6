version: '3.8'

services:
  hbbs:
    image: rustdesk/rustdesk-server:latest
    container_name: spear-hbbs
    command: hbbs
    volumes:
      - ./data:/root
    ports:
      - "21114:21114"  # Web console (Pro only)
      - "21115:21115"  # NAT type test
      - "21116:21116"  # TCP/UDP - ID registration and heartbeat
      - "21118:21118"  # Web client support
    environment:
      - ALWAYS_USE_RELAY=N
      - RUST_LOG=info
    restart: unless-stopped
    depends_on:
      - hbbr

  hbbr:
    image: rustdesk/rustdesk-server:latest
    container_name: spear-hbbr
    command: hbbr
    volumes:
      - ./data:/root
    ports:
      - "21117:21117"  # Relay services
      - "21119:21119"  # Web client support
    environment:
      - RUST_LOG=info
    restart: unless-stopped

volumes:
  data:
    driver: local
