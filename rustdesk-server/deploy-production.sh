#!/bin/bash

# SPEAR RustDesk Pro Production Deployment Script
# This script helps deploy RustDesk Pro server to DigitalOcean VPS

set -e

# Check if running on macOS (required for this script)
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "❌ This script is designed for macOS. For other systems, use manual deployment."
    exit 1
fi

echo "🚀 SPEAR RustDesk Pro Production Deployment"
echo "==========================================="

# Configuration
DROPLET_NAME="spear-rustdesk-pro"
DROPLET_SIZE="s-2vcpu-2gb"
DROPLET_IMAGE="ubuntu-22-04-x64"
DROPLET_REGION="nyc1"
DOMAIN_NAME="rustdesk.spear-app.com"
SSH_KEY_NAME="spear-deployment"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if doctl is installed
check_doctl() {
    if ! command -v doctl &> /dev/null; then
        print_error "DigitalOcean CLI (doctl) is not installed"
        echo "Install it with: brew install doctl"
        echo "Then authenticate: doctl auth init"
        exit 1
    fi
    
    # Check if authenticated
    if ! doctl account get &> /dev/null; then
        print_error "DigitalOcean CLI is not authenticated"
        echo "Run: doctl auth init"
        exit 1
    fi
    
    print_success "DigitalOcean CLI is ready"
}

# Create SSH key if it doesn't exist
setup_ssh_key() {
    print_status "Setting up SSH key..."
    
    # Check if SSH key exists locally
    if [ ! -f ~/.ssh/id_rsa ]; then
        print_status "Generating SSH key..."
        ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N ""
    fi
    
    # Check if key exists in DigitalOcean
    if ! doctl compute ssh-key list --format Name --no-header | grep -q "$SSH_KEY_NAME"; then
        print_status "Adding SSH key to DigitalOcean..."
        doctl compute ssh-key import $SSH_KEY_NAME --public-key-file ~/.ssh/id_rsa.pub
    fi
    
    print_success "SSH key is ready"
}

# Create DigitalOcean droplet
create_droplet() {
    print_status "Creating DigitalOcean droplet..."
    
    # Check if droplet already exists
    if doctl compute droplet list --format Name --no-header | grep -q "$DROPLET_NAME"; then
        print_warning "Droplet '$DROPLET_NAME' already exists"
        DROPLET_IP=$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')
        print_status "Using existing droplet IP: $DROPLET_IP"
    else
        print_status "Creating new droplet..."
        doctl compute droplet create $DROPLET_NAME \
            --size $DROPLET_SIZE \
            --image $DROPLET_IMAGE \
            --region $DROPLET_REGION \
            --ssh-keys $(doctl compute ssh-key list --format ID --no-header | grep -v "^$" | head -1) \
            --wait
        
        DROPLET_IP=$(doctl compute droplet list --format Name,PublicIPv4 --no-header | grep "$DROPLET_NAME" | awk '{print $2}')
        print_success "Droplet created with IP: $DROPLET_IP"
    fi
}

# Configure DNS
configure_dns() {
    print_status "DNS Configuration Required"
    echo ""
    echo "Please configure your DNS settings:"
    echo "1. Log into your domain registrar (e.g., Namecheap, GoDaddy)"
    echo "2. Add an A record:"
    echo "   Name: rustdesk"
    echo "   Value: $DROPLET_IP"
    echo "   TTL: 300 (5 minutes)"
    echo ""
    echo "This will make $DOMAIN_NAME point to your server."
    echo ""
    read -p "Press Enter when DNS is configured..."
}

# Deploy RustDesk server
deploy_server() {
    print_status "Deploying RustDesk Pro server..."
    
    # Wait for SSH to be ready
    print_status "Waiting for SSH to be ready..."
    while ! ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no root@$DROPLET_IP "echo 'SSH ready'" &> /dev/null; do
        sleep 5
        echo -n "."
    done
    echo ""
    
    # Copy installation script
    print_status "Copying installation script..."
    scp -o StrictHostKeyChecking=no vps-install.sh root@$DROPLET_IP:/root/
    
    # Run installation
    print_status "Running installation (this may take 5-10 minutes)..."
    ssh -o StrictHostKeyChecking=no root@$DROPLET_IP "chmod +x /root/vps-install.sh && /root/vps-install.sh"
    
    print_success "Server deployment complete!"
}

# Get server information
get_server_info() {
    print_status "Retrieving server information..."
    
    # Get server key
    SERVER_KEY=$(ssh -o StrictHostKeyChecking=no root@$DROPLET_IP "cat /opt/rustdesk/data/id_ed25519.pub 2>/dev/null || docker exec rustdesk-hbbs cat /root/id_ed25519.pub 2>/dev/null || echo 'Key not found'")
    
    echo ""
    print_success "🎉 SPEAR RustDesk Pro Server Deployed Successfully!"
    echo "=================================================="
    echo ""
    echo "🌐 Server Details:"
    echo "   Domain: https://$DOMAIN_NAME"
    echo "   IP Address: $DROPLET_IP"
    echo "   SSH Access: ssh root@$DROPLET_IP"
    echo ""
    echo "🔑 Server Key (copy this for SPEAR configuration):"
    echo "$SERVER_KEY"
    echo ""
    echo "📋 Environment Variables for SPEAR:"
    echo "RUSTDESK_SERVER_IP=$DOMAIN_NAME"
    echo "RUSTDESK_SERVER_PORT=21116"
    echo "RUSTDESK_RELAY_PORT=21117"
    echo "RUSTDESK_API_PORT=21114"
    echo "RUSTDESK_SERVER_KEY=$SERVER_KEY"
    echo ""
    echo "NEXT_PUBLIC_RUSTDESK_SERVER_IP=$DOMAIN_NAME"
    echo "NEXT_PUBLIC_RUSTDESK_SERVER_PORT=21116"
    echo "NEXT_PUBLIC_RUSTDESK_RELAY_PORT=21117"
    echo "NEXT_PUBLIC_RUSTDESK_API_PORT=21114"
    echo "NEXT_PUBLIC_RUSTDESK_SERVER_KEY=$SERVER_KEY"
    echo ""
    echo "🔧 Management Commands (run on server):"
    echo "   Status: spear-rustdesk-status"
    echo "   Logs: docker logs rustdesk-hbbs"
    echo "   Restart: systemctl restart spear-rustdesk"
    echo ""
    echo "🌐 Web Console: https://$DOMAIN_NAME"
    echo "   Default login: admin / test1234"
    echo "   ⚠️  Change the password immediately!"
    echo ""
}

# Main deployment flow
main() {
    echo "This script will:"
    echo "1. Create a DigitalOcean droplet"
    echo "2. Install RustDesk Pro server with your license"
    echo "3. Configure SSL/HTTPS"
    echo "4. Set up the web console"
    echo ""
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
    
    check_doctl
    setup_ssh_key
    create_droplet
    configure_dns
    deploy_server
    get_server_info
    
    print_success "Deployment complete! 🎉"
    echo ""
    echo "Next steps:"
    echo "1. Update SPEAR environment variables with the values above"
    echo "2. Test the web console at https://$DOMAIN_NAME"
    echo "3. Configure client devices to use the new server"
}

# Run main function
main "$@"
