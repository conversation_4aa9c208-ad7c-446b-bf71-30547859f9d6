#!/bin/bash

# SPEAR RustDesk Server Deployment Script for Railway
# This script sets up a self-hosted RustDesk server for improved performance

echo "🚀 Setting up SPEAR RustDesk Server on Railway..."

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Please install it first:"
    echo "npm install -g @railway/cli"
    exit 1
fi

# Login to Railway (if not already logged in)
echo "🔐 Checking Railway authentication..."
railway login

# Create new Railway project for RustDesk
echo "📦 Creating Railway project..."
railway init spear-rustdesk-server

# Deploy the services
echo "🚀 Deploying RustDesk servers..."
railway up

# Get the deployment URL
echo "🌐 Getting deployment information..."
RAILWAY_URL=$(railway status --json | jq -r '.deployments[0].url')

echo "✅ RustDesk Server deployed successfully!"
echo "📍 Server URL: $RAILWAY_URL"
echo "🔑 Next steps:"
echo "   1. Get the server key from logs: railway logs"
echo "   2. Configure SPEAR clients to use this server"
echo "   3. Update SPEAR application with server details"

# Display important information
echo ""
echo "🔧 Server Configuration:"
echo "   ID Server: $RAILWAY_URL"
echo "   Relay Server: $RAILWAY_URL"
echo "   Ports: 21115-21119"
echo ""
echo "📝 To get the server key, run:"
echo "   railway logs | grep 'Public key'"
