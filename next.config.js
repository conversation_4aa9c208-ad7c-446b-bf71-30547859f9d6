/** @type {import('next').NextConfig} */
const nextConfig = {
  serverExternalPackages: ["bcrypt", "nodemailer"],
  // Disable CSS optimization to prevent lightningcss usage
  experimental: {
    optimizeCss: false,
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  webpack: (config, { isServer, nextRuntime }) => {
    // If client-side, don't polyfill Node.js modules
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        path: false,
        os: false,
        crypto: false,
        stream: false,
        http: false,
        https: false,
        zlib: false,
        util: false,
        net: false,
        tls: false,
        child_process: false,
      };
    }

    // Avoid bundling bcrypt and crypto in middleware
    if (nextRuntime === 'edge') {
      config.resolve.alias = {
        ...config.resolve.alias,
        bcrypt: false,
        crypto: false,
      };
    }

    return config;
  },
};

module.exports = nextConfig;
