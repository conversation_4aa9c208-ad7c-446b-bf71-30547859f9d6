#!/usr/bin/env python3
import http.server
import socketserver
import socket
import json
import os
import subprocess
from urllib.parse import urlparse, parse_qs

# Get the local IP address
def get_local_ip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # doesn't even have to be reachable
        s.connect(('**************', 1))
        IP = s.getsockname()[0]
    except Exception:
        IP = '127.0.0.1'
    finally:
        s.close()
    return IP

# Custom request handler
class DiagnosticHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)

        # Serve the diagnostic HTML file
        if parsed_path.path == '/' or parsed_path.path == '/index.html':
            self.path = '/diagnostic.html'
            return http.server.SimpleHTTPRequestHandler.do_GET(self)

        # API endpoint to check RustDesk server status
        elif parsed_path.path == '/api/check-rustdesk':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()

            # Check Docker containers
            try:
                docker_ps = subprocess.check_output(['docker', 'ps'], universal_newlines=True)
                hbbs_running = 'hbbs' in docker_ps
                hbbr_running = 'hbbr' in docker_ps
            except Exception as e:
                hbbs_running = False
                hbbr_running = False
                docker_error = str(e)

            # Check ports
            ports_to_check = [21116, 21117, 21118, 21119]
            port_status = {}

            for port in ports_to_check:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', port))
                port_status[port] = result == 0
                sock.close()

            # Prepare response
            response = {
                'server_ip': get_local_ip(),
                'docker': {
                    'hbbs_running': hbbs_running,
                    'hbbr_running': hbbr_running,
                    'error': docker_error if not (hbbs_running and hbbr_running) else None
                },
                'ports': port_status
            }

            self.wfile.write(json.dumps(response).encode())
            return

        # Default: serve the file
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

# Set up the server
PORT = 8080
Handler = DiagnosticHandler
local_ip = get_local_ip()

with socketserver.TCPServer(("", PORT), Handler) as httpd:
    print(f"Server running at http://{local_ip}:{PORT}")
    print(f"Open this URL on your Android device to run diagnostics")
    httpd.serve_forever()
