events { worker_connections 1024; }

# TCP Stream proxying for RustDesk
stream {
    # ID Server (TCP)
    server {
        listen 21114;
        proxy_pass *************:21114;
        proxy_timeout 1s;
        proxy_responses 1;
    }

    # Relay Server (TCP)
    server {
        listen 21117;
        proxy_pass *************:21117;
        proxy_timeout 1s;
        proxy_responses 1;
    }

    # SSL WebSocket ID Server
    server {
        listen 21118 ssl;
        proxy_pass *************:21118;
        proxy_timeout 1s;
        proxy_responses 1;
        ssl_certificate /etc/ssl/certs/fullchain.pem;
        ssl_certificate_key /etc/ssl/private/privkey.pem;
    }

    # SSL WebSocket Relay Server
    server {
        listen 21119 ssl;
        proxy_pass *************:21119;
        proxy_timeout 1s;
        proxy_responses 1;
        ssl_certificate /etc/ssl/certs/fullchain.pem;
        ssl_certificate_key /etc/ssl/private/privkey.pem;
    }
}

http {
    upstream rustdesk_backend { server *************:21114; }
    upstream rustdesk_ws_id { server *************:21118; }
    upstream rustdesk_ws_relay { server *************:21119; }
    
    server {
        listen 80;
        server_name spear-rustdesk.duckdns.org;
        return 301 https://\$server_name\$request_uri;
    }
    
    server {
        listen 443 ssl;
        server_name spear-rustdesk.duckdns.org;
        ssl_certificate /etc/ssl/certs/fullchain.pem;
        ssl_certificate_key /etc/ssl/private/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        
        location /api/ {
            proxy_pass http://rustdesk_backend/api/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            add_header "Access-Control-Allow-Origin" "*" always;
            add_header "Access-Control-Allow-Methods" "GET, POST, PUT, DELETE, PATCH, OPTIONS" always;
            add_header "Access-Control-Allow-Headers" "Origin, Content-Type, Accept, Authorization, X-Requested-With" always;
        }
        
        location / {
            proxy_pass http://rustdesk_backend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            add_header "Access-Control-Allow-Origin" "*" always;
        }
        
        location /ws/id {
            proxy_pass http://*************:21118/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_read_timeout 120s;
        }
        
        location /ws/relay {
            proxy_pass http://*************:21119/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_read_timeout 120s;
        }
    }
}
