const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function verifyAndResetAdminLogin() {
  try {
    console.log('🔍 Checking admin user...');
    
    // Find the admin user
    const admin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!admin) {
      console.log('❌ Admin user not found!');
      
      // Create admin user
      const hashedPassword = await bcrypt.hash('password', 10);
      const newAdmin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: '<PERSON><PERSON>',
          password: hashedPassword,
          role: 'ADMIN'
        }
      });
      
      console.log('✅ Admin user created:', newAdmin.email);
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: password');
      return;
    }
    
    console.log('✅ Admin user found:', admin.email);
    console.log('👤 Name:', admin.name);
    console.log('🎭 Role:', admin.role);
    
    // Test password verification
    const testPassword = 'password';
    const isValidPassword = await bcrypt.compare(testPassword, admin.password);
    
    if (isValidPassword) {
      console.log('✅ Password verification successful!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: password');
      console.log('🌐 Login URL: http://localhost:3000/login');
    } else {
      console.log('❌ Password verification failed. Resetting password...');
      
      // Reset password
      const newHashedPassword = await bcrypt.hash('password', 10);
      const updatedAdmin = await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { password: newHashedPassword }
      });
      
      console.log('✅ Password reset successful!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: password');
      console.log('🌐 Login URL: http://localhost:3000/login');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyAndResetAdminLogin();
