version: '3.8'

services:
  rustdesk-server-pro:
    image: rustdesk/rustdesk-server-pro:latest
    container_name: spear-rustdesk-pro
    ports:
      - "21114:21114"  # Web console (Pro only)
      - "21115:21115"  # NAT type test
      - "21116:21116"  # ID registration and heartbeat (TCP)
      - "21116:21116/udp"  # ID registration and heartbeat (UDP)
      - "21117:21117"  # Relay services
      - "21118:21118"  # Web client WebSocket
      - "21119:21119"  # Web client files/WebSocket
    volumes:
      - ./data:/data
    environment:
      - LICENSE=+iOXAUS4gnKPPzWl6YUqGQ==
      - DOMAIN=localhost
      - HTTPS=false
    restart: unless-stopped
    networks:
      - rustdesk

networks:
  rustdesk:
    driver: bridge
