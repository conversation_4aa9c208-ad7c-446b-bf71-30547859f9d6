const bcrypt = require('bcrypt');

async function generateBcryptHash() {
  const password = 'password';
  const saltRounds = 10;
  
  try {
    const hash = await bcrypt.hash(password, saltRounds);
    console.log('🔧 Bcrypt hash for password "password":');
    console.log(hash);
    console.log('');
    console.log('🎯 SQL Command:');
    console.log(`UPDATE "User" SET password = '${hash}' WHERE email = '<EMAIL>';`);
    return hash;
  } catch (error) {
    console.error('Error generating bcrypt hash:', error);
  }
}

generateBcryptHash();
