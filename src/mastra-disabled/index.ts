// Temporarily disabled <PERSON><PERSON> to fix Firebase build error
// import { <PERSON><PERSON> } from "@mastra/core";
// import { adminAgent } from "./agents/admin-agent";
// import { seoAgent } from "./agents/seo-agent";
// import { adminNetwork } from "./network/admin-network";

// Configure OpenAI API key
import { openai } from "@ai-sdk/openai";
if (process.env.OPENAI_API_KEY) {
  openai.apiKey = process.env.OPENAI_API_KEY;
  console.log("OpenAI API key configured");
} else {
  console.error("OpenAI API key not found in environment variables");
}

// Temporarily disabled to fix Firebase build error
// export const mastra = new Mastra({
//   agents: {
//     adminAgent,
//     seoAgent,
//   },
//   networks: {
//     adminNetwork,
//   },
// });

export const mastra = null; // Placeholder
