'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, AlertCircle } from 'lucide-react';

interface PayPalPaymentFormProps {
  amount: number;
  planType: string;
  couponCode?: string;
  onSuccess: (result: any) => void;
  onError: (error: string) => void;
  processing: boolean;
}

interface BillingInfo {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
}

declare global {
  interface Window {
    paypal: any;
  }
}

export default function PayPalPaymentForm({
  amount,
  planType,
  couponCode,
  onSuccess,
  onError,
  processing
}: PayPalPaymentFormProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [paypalLoaded, setPaypalLoaded] = useState(false);
  const [billingInfo, setBillingInfo] = useState<BillingInfo>({
    fullName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: ''
  });

  const paypalContainerRef = useRef<HTMLDivElement>(null);
  const billingInfoRef = useRef<BillingInfo>(billingInfo);
  const amountRef = useRef<number>(amount);

  // Update refs when props change
  useEffect(() => {
    amountRef.current = amount;
  }, [amount]);

  useEffect(() => {
    const loadPayPalSDK = () => {
      if (window.paypal) {
        initializePayPal();
        return;
      }

      const script = document.createElement('script');
      const clientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID;
      
      if (!clientId) {
        setError('PayPal configuration is missing');
        setIsLoading(false);
        return;
      }

      script.src = `https://www.paypal.com/sdk/js?client-id=${clientId}&currency=USD&intent=capture`;
      script.async = true;
      script.onload = () => {
        console.log('PayPal SDK loaded successfully');
        setPaypalLoaded(true);
        initializePayPal();
      };
      script.onerror = () => {
        console.error('Failed to load PayPal SDK');
        setError('Failed to load PayPal payment system');
        setIsLoading(false);
      };
      document.head.appendChild(script);
    };

    loadPayPalSDK();
  }, []); // Initialize PayPal buttons only once

  const initializePayPal = () => {
    if (!window.paypal || !paypalContainerRef.current) {
      return;
    }

    try {
      console.log('Initializing PayPal buttons with amount:', amount);
      window.paypal.Buttons({
        createOrder: async () => {
          try {
            setIsProcessing(true);
            setError('');

            // Get current billing info and amount from refs (to avoid closure issues)
            const currentBillingInfo = billingInfoRef.current;
            const currentAmount = amountRef.current;
            console.log('Current billing info from ref:', currentBillingInfo);
            console.log('Current amount from ref:', currentAmount);
            console.log('Full name:', currentBillingInfo.fullName);
            console.log('Email:', currentBillingInfo.email);

            // Validate billing information
            if (!currentBillingInfo.fullName || !currentBillingInfo.email) {
              console.error('Validation failed - missing required fields');
              throw new Error('Please fill in all required billing information');
            }

            // Create payment with our API
            const response = await fetch('/api/paypal/create-payment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                planType,
                couponCode: couponCode || null,
                billingInfo: currentBillingInfo,
                amount: currentAmount, // Pass the current frontend-calculated discounted amount
              }),
            });

            const data = await response.json();

            if (!response.ok || !data.success) {
              throw new Error(data.error || 'Failed to create payment');
            }

            return data.payment.id;
          } catch (error) {
            console.error('Error creating PayPal order:', error);
            setError(error instanceof Error ? error.message : 'Failed to create payment');
            setIsProcessing(false);
            throw error;
          }
        },

        onApprove: async (data: any) => {
          try {
            setIsProcessing(true);

            // Capture the payment
            const response = await fetch('/api/paypal/capture-payment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                orderId: data.orderID,
              }),
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
              throw new Error(result.error || 'Payment capture failed');
            }

            onSuccess(result);
          } catch (error) {
            console.error('Error capturing PayPal payment:', error);
            onError(error instanceof Error ? error.message : 'Payment capture failed');
          } finally {
            setIsProcessing(false);
          }
        },

        onError: (err: any) => {
          console.error('PayPal error:', err);
          setError('PayPal payment failed. Please try again.');
          setIsProcessing(false);
        },

        onCancel: () => {
          console.log('PayPal payment cancelled by user');
          setIsProcessing(false);
        },

        style: {
          layout: 'vertical',
          color: 'blue',
          shape: 'rect',
          label: 'paypal',
          height: 45
        }
      }).render(paypalContainerRef.current);

      setIsLoading(false);
    } catch (error) {
      console.error('Error initializing PayPal:', error);
      setError('Failed to initialize PayPal payment system');
      setIsLoading(false);
    }
  };

  const handleBillingInfoChange = (field: keyof BillingInfo, value: string) => {
    console.log(`Updating ${field} to:`, value);
    setBillingInfo(prev => {
      const updated = {
        ...prev,
        [field]: value
      };
      console.log('Updated billing info:', updated);
      // Update the ref with the latest state
      billingInfoRef.current = updated;
      return updated;
    });
  };

  const isFormValid = billingInfo.fullName && billingInfo.email;

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Billing Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Billing Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={billingInfo.fullName}
              onChange={(e) => handleBillingInfoChange('fullName', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
              placeholder="John Doe"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              value={billingInfo.email}
              onChange={(e) => handleBillingInfoChange('email', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              value={billingInfo.phone}
              onChange={(e) => handleBillingInfoChange('phone', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
              placeholder="(*************"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Address
            </label>
            <input
              type="text"
              value={billingInfo.address}
              onChange={(e) => handleBillingInfoChange('address', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
              placeholder="123 Main St"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              City
            </label>
            <input
              type="text"
              value={billingInfo.city}
              onChange={(e) => handleBillingInfoChange('city', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
              placeholder="New York"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              State
            </label>
            <input
              type="text"
              value={billingInfo.state}
              onChange={(e) => handleBillingInfoChange('state', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
              placeholder="NY"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ZIP Code
            </label>
            <input
              type="text"
              value={billingInfo.zipCode}
              onChange={(e) => handleBillingInfoChange('zipCode', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
              placeholder="10001"
            />
          </div>
        </div>
      </div>

      {/* PayPal Payment */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Payment Method</h3>
        
        <div className="border border-gray-300 rounded-md p-4">
          {!isFormValid && (
            <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                Please fill in all required billing information before proceeding with payment.
              </p>
            </div>
          )}

          <div
            ref={paypalContainerRef}
            className={`min-h-[50px] ${!isFormValid ? 'opacity-50 pointer-events-none' : ''}`}
          >
            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
                <span className="ml-2 text-sm text-gray-500">Loading PayPal...</span>
              </div>
            )}
          </div>

          {(isProcessing || processing) && (
            <div className="mt-4 flex items-center justify-center py-4">
              <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
              <span className="ml-2 text-sm text-gray-600">Processing payment...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
