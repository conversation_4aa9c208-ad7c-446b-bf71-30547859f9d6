"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircleIcon, ExclamationTriangleIcon } from "@heroicons/react/24/outline";

interface TradeInFormProps {
  onSubmissionComplete?: () => void;
}

export default function TradeInForm({ onSubmissionComplete }: TradeInFormProps) {
  const [formData, setFormData] = useState({
    deviceModel: "Samsung A14",
    deviceCondition: "",
    estimatedValue: "",
    additionalNotes: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const deviceConditions = [
    { value: "excellent", label: "Excellent - Like new, no visible wear", estimate: 250 },
    { value: "good", label: "Good - Minor wear, fully functional", estimate: 200 },
    { value: "fair", label: "Fair - Noticeable wear, fully functional", estimate: 150 },
    { value: "poor", label: "Poor - Heavy wear, may have issues", estimate: 100 },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await fetch("/api/trade-in-requests", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          estimatedValue: parseFloat(formData.estimatedValue),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to submit trade-in request");
      }

      setSuccess(true);
      setFormData({
        deviceModel: "Samsung A14",
        deviceCondition: "",
        estimatedValue: "",
        additionalNotes: "",
      });
      
      if (onSubmissionComplete) {
        onSubmissionComplete();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError("");
    setSuccess(false);

    // Auto-update estimated value based on condition
    if (field === "deviceCondition") {
      const condition = deviceConditions.find(c => c.value === value);
      if (condition) {
        setFormData(prev => ({ ...prev, estimatedValue: condition.estimate.toString() }));
      }
    }
  };

  if (success) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <CheckCircleIcon className="w-16 h-16 text-green-500 mx-auto" />
            <h3 className="text-lg font-semibold text-green-700">Trade-In Request Submitted!</h3>
            <p className="text-muted-foreground">
              Your Samsung A14 trade-in request has been submitted. Our team will review it and 
              provide shipping instructions within 24-48 hours.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-semibold text-blue-900 mb-2">What's Next?</h4>
              <ul className="text-sm text-blue-800 space-y-1 text-left">
                <li>• We'll email you shipping instructions and a prepaid label</li>
                <li>• Package your Samsung A14 securely and ship it to us</li>
                <li>• We'll inspect the device and confirm the final value</li>
                <li>• Your subscription rebate will be applied once processed</li>
              </ul>
            </div>
            <Button 
              onClick={() => setSuccess(false)}
              variant="outline"
            >
              Submit Another Request
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <span>Samsung A14 Trade-In Program</span>
        </CardTitle>
        <CardDescription>
          Trade in your Samsung A14 for a subscription rebate. We'll configure it for you and apply 
          the device value as a credit to your account.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <ExclamationTriangleIcon className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Device Model */}
          <div className="space-y-2">
            <Label htmlFor="deviceModel">Device Model</Label>
            <Input
              id="deviceModel"
              value={formData.deviceModel}
              onChange={(e) => handleInputChange("deviceModel", e.target.value)}
              placeholder="Samsung A14"
              required
            />
          </div>

          {/* Device Condition */}
          <div className="space-y-2">
            <Label htmlFor="deviceCondition">Device Condition *</Label>
            <Select 
              value={formData.deviceCondition} 
              onValueChange={(value) => handleInputChange("deviceCondition", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select device condition" />
              </SelectTrigger>
              <SelectContent>
                {deviceConditions.map((condition) => (
                  <SelectItem key={condition.value} value={condition.value}>
                    {condition.label} (Est. ${condition.estimate})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Estimated Value */}
          <div className="space-y-2">
            <Label htmlFor="estimatedValue">Estimated Value (USD) *</Label>
            <Input
              id="estimatedValue"
              type="number"
              value={formData.estimatedValue}
              onChange={(e) => handleInputChange("estimatedValue", e.target.value)}
              placeholder="0"
              min="0"
              step="0.01"
              required
            />
            <p className="text-sm text-muted-foreground">
              This value is automatically set based on condition. Final value will be determined upon inspection.
            </p>
          </div>

          {/* Additional Notes */}
          <div className="space-y-2">
            <Label htmlFor="additionalNotes">Additional Notes (Optional)</Label>
            <Textarea
              id="additionalNotes"
              value={formData.additionalNotes}
              onChange={(e) => handleInputChange("additionalNotes", e.target.value)}
              placeholder="Any additional information about your device..."
              rows={3}
            />
          </div>

          {/* Program Details */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-900 mb-2">Trade-In Program Benefits:</h4>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• Get a subscription rebate equal to your device's value</li>
              <li>• We handle all device configuration and setup</li>
              <li>• Free shipping with prepaid labels</li>
              <li>• Professional device inspection and fair pricing</li>
              <li>• Rebate applied directly to your subscription</li>
            </ul>
          </div>

          {/* Terms */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-2">Important Terms:</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Device must be functional and match the described condition</li>
              <li>• Final value determined after physical inspection</li>
              <li>• Rebate applied as subscription credit, not cash refund</li>
              <li>• Device must be factory reset before shipping</li>
              <li>• Spear reserves the right to adjust value based on actual condition</li>
            </ul>
          </div>

          {/* Submit Button */}
          <Button type="submit" disabled={loading} className="w-full">
            {loading ? "Submitting..." : "Submit Trade-In Request"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
