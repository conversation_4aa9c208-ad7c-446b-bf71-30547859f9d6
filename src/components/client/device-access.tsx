"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  DevicePhoneMobileIcon as SmartphoneIcon,
  WifiIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  LockClosedIcon
} from "@heroicons/react/24/outline";
import { getRustDeskConfig } from "@/lib/rustdesk-config";

interface ClientDevice {
  id: string;
  name: string;
  rustDeskId: string;
  password: string;
  model: string;
  status: 'online' | 'offline';
  lastSeen: string;
  platform: string;
}

interface SubscriptionStatus {
  isActive: boolean;
  planType: string;
  expiresAt?: string;
}

export function ClientDeviceAccess() {
  const { data: session } = useSession();
  const [devices, setDevices] = useState<ClientDevice[]>([]);
  const [subscription, setSubscription] = useState<SubscriptionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (session?.user?.email) {
      loadClientDevices();
      loadSubscriptionStatus();
    }
  }, [session]);

  const loadClientDevices = async () => {
    try {
      const response = await fetch('/api/client/devices');
      if (response.ok) {
        const data = await response.json();
        setDevices(data);
      } else {
        setError('Failed to load your devices');
      }
    } catch (error) {
      console.error('Error loading devices:', error);
      setError('Failed to load your devices');
    }
  };

  const loadSubscriptionStatus = async () => {
    try {
      const response = await fetch('/api/client/subscription');
      if (response.ok) {
        const data = await response.json();
        setSubscription(data);
      }
    } catch (error) {
      console.error('Error loading subscription:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const connectToDevice = async (device: ClientDevice) => {
    if (!subscription?.isActive) {
      alert('Your subscription is not active. Please update your payment method to access your devices.');
      return;
    }

    try {
      // Step 1: Verify subscription with server before allowing connection
      const authResponse = await fetch('/api/client/verify-access', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ deviceId: device.id })
      });

      if (!authResponse.ok) {
        alert('Access denied. Please check your subscription status.');
        return;
      }

      const { accessToken } = await authResponse.json();

      // Step 2: Open SPEAR's protected RustDesk web viewer (not direct RustDesk)
      const spearViewerUrl = `/viewer?device=${device.rustDeskId}&token=${accessToken}`;

      // Open SPEAR's protected viewer in new window
      const viewerWindow = window.open(
        spearViewerUrl,
        'spear-viewer-' + device.rustDeskId,
        'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no'
      );

      if (viewerWindow) {
        viewerWindow.focus();

        // Show connection status
        const statusMessage = `
🔗 Connecting to ${device.name}...

✅ Subscription verified
✅ Device access authorized
✅ Secure connection established

Device: ${device.name} (${device.rustDeskId})
Status: ${device.status}
        `.trim();

        // Show brief status then auto-close
        alert(statusMessage);
      } else {
        alert('Please allow popups for this site to connect to your device.');
      }
    } catch (error) {
      console.error('Error connecting to device:', error);
      alert('Connection failed. Please check your subscription and try again.');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "online":
        return <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
          <CheckCircleIcon className="h-3 w-3" />
          Online
        </Badge>;
      case "offline":
        return <Badge className="bg-red-100 text-red-800 flex items-center gap-1">
          <ExclamationTriangleIcon className="h-3 w-3" />
          Offline
        </Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading your devices...</p>
        </div>
      </div>
    );
  }

  if (!subscription?.isActive) {
    return (
      <div className="space-y-6">
        <Alert>
          <LockClosedIcon className="h-4 w-4" />
          <AlertDescription>
            Your subscription is not active. Please update your payment method to access your devices.
          </AlertDescription>
        </Alert>
        
        <Card>
          <CardHeader>
            <CardTitle>Device Access Restricted</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              To access your remote devices, you need an active subscription.
            </p>
            <Button onClick={() => window.location.href = '/pricing'}>
              View Pricing Plans
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Subscription Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircleIcon className="h-5 w-5 text-green-600" />
            Subscription Active
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">{subscription?.planType}</p>
              {subscription?.expiresAt && (
                <p className="text-sm text-gray-600">
                  Renews: {new Date(subscription.expiresAt).toLocaleDateString()}
                </p>
              )}
            </div>
            <Badge className="bg-green-100 text-green-800">Active</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Device List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Devices</CardTitle>
          <p className="text-gray-600">Connect to your assigned remote devices</p>
        </CardHeader>
        <CardContent>
          {devices.length === 0 ? (
            <div className="text-center py-8">
              <SmartphoneIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No devices assigned to your account.</p>
              <p className="text-sm text-gray-500 mt-2">
                Contact support if you believe this is an error.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {devices.map((device) => (
                <div key={device.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <SmartphoneIcon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-medium">{device.name}</h3>
                        <p className="text-sm text-gray-600">{device.model}</p>
                        <p className="text-xs text-gray-500">
                          ID: {device.rustDeskId} • Last seen: {new Date(device.lastSeen).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {getStatusBadge(device.status)}
                      <Button
                        onClick={() => connectToDevice(device)}
                        disabled={device.status !== 'online'}
                        className="flex items-center gap-2"
                      >
                        <WifiIcon className="h-4 w-4" />
                        Connect
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>Connection Issues:</strong> Ensure your device is powered on and connected to the internet.</p>
            <p><strong>Device Offline:</strong> Check if the device is running and the RustDesk app is active.</p>
            <p><strong>Password Problems:</strong> Contact support if you need your device password reset.</p>
          </div>
          <Button variant="outline" className="mt-4" onClick={() => window.location.href = '/support'}>
            Contact Support
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
