"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircleIcon, ExclamationTriangleIcon } from "@heroicons/react/24/outline";

interface DeviceSubmissionFormProps {
  onSubmissionComplete?: () => void;
}

export default function DeviceSubmissionForm({ onSubmissionComplete }: DeviceSubmissionFormProps) {
  const [formData, setFormData] = useState({
    deviceName: "",
    rustDeskId: "",
    deviceType: "desktop",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const response = await fetch("/api/device-submissions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to submit device");
      }

      setSuccess(true);
      setFormData({ deviceName: "", rustDeskId: "", deviceType: "desktop" });
      
      if (onSubmissionComplete) {
        onSubmissionComplete();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError("");
    setSuccess(false);
  };

  if (success) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <CheckCircleIcon className="w-16 h-16 text-green-500 mx-auto" />
            <h3 className="text-lg font-semibold text-green-700">Device Submitted Successfully!</h3>
            <p className="text-muted-foreground">
              Your device submission has been sent to our admin team for review. 
              You'll receive a notification once it's been processed.
            </p>
            <Button 
              onClick={() => setSuccess(false)}
              variant="outline"
            >
              Submit Another Device
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
          </svg>
          <span>Register Your Desktop Device</span>
        </CardTitle>
        <CardDescription>
          Submit your existing desktop device for admin approval. You can register exactly one desktop device.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <ExclamationTriangleIcon className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Device Name */}
          <div className="space-y-2">
            <Label htmlFor="deviceName">Device Name *</Label>
            <Input
              id="deviceName"
              value={formData.deviceName}
              onChange={(e) => handleInputChange("deviceName", e.target.value)}
              placeholder="e.g., My Work Computer, John's Laptop"
              required
            />
            <p className="text-sm text-muted-foreground">
              Give your device a recognizable name for easy identification
            </p>
          </div>

          {/* RustDesk ID */}
          <div className="space-y-2">
            <Label htmlFor="rustDeskId">RustDesk ID *</Label>
            <Input
              id="rustDeskId"
              value={formData.rustDeskId}
              onChange={(e) => handleInputChange("rustDeskId", e.target.value)}
              placeholder="e.g., 1234567890"
              required
            />
            <p className="text-sm text-muted-foreground">
              Enter the RustDesk ID from your device. You can find this in the RustDesk application.
            </p>
          </div>

          {/* Device Type */}
          <div className="space-y-2">
            <Label htmlFor="deviceType">Device Type</Label>
            <Select 
              value={formData.deviceType} 
              onValueChange={(value) => handleInputChange("deviceType", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desktop">Desktop Computer</SelectItem>
                <SelectItem value="laptop">Laptop</SelectItem>
                <SelectItem value="workstation">Workstation</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-2">Before Submitting:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Ensure RustDesk is installed and running on your device</li>
              <li>• Make sure your device is connected to the internet</li>
              <li>• Verify the RustDesk ID is correct (it should be a numeric ID)</li>
              <li>• Your device will need admin approval before remote access is enabled</li>
            </ul>
          </div>

          {/* Submit Button */}
          <Button type="submit" disabled={loading} className="w-full">
            {loading ? "Submitting..." : "Submit Device for Approval"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
