"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  ComputerDesktopIcon, 
  UserGroupIcon, 
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowTopRightOnSquareIcon
} from "@heroicons/react/24/outline";
import { getRustDeskConfig } from "@/lib/rustdesk-config";

interface RustDeskUser {
  id: string;
  name: string;
  email: string;
  enabled: boolean;
  isAdmin: boolean;
  deviceCount: number;
  lastLogin?: string;
}

interface RustDeskDevice {
  id: string;
  name: string;
  userId?: string;
  online: boolean;
  lastSeen: string;
  platform: string;
}

interface SubscriptionStatus {
  userId: string;
  email: string;
  isActive: boolean;
  planType: string;
  expiresAt?: string;
}

export function RustDeskConsoleIntegration() {
  const [activeTab, setActiveTab] = useState("overview");
  const [isLoading, setIsLoading] = useState(false);
  const [consoleUrl, setConsoleUrl] = useState("");
  const [users, setUsers] = useState<RustDeskUser[]>([]);
  const [devices, setDevices] = useState<RustDeskDevice[]>([]);
  const [subscriptions, setSubscriptions] = useState<SubscriptionStatus[]>([]);
  const [serverStatus, setServerStatus] = useState<"online" | "offline" | "unknown">("unknown");

  useEffect(() => {
    const config = getRustDeskConfig();
    setConsoleUrl(`https://${config.serverIp}:${config.apiPort}`);
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load users, devices, and subscription data
      await Promise.all([
        loadUsers(),
        loadDevices(),
        loadSubscriptions(),
        checkServerStatus()
      ]);
    } catch (error) {
      console.error("Error loading RustDesk data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUsers = async () => {
    // Mock data - replace with actual API calls
    setUsers([
      {
        id: "user1",
        name: "Sidney Client",
        email: "<EMAIL>",
        enabled: true,
        isAdmin: false,
        deviceCount: 1,
        lastLogin: "2024-01-15T10:30:00Z"
      },
      {
        id: "user2", 
        name: "Test Client",
        email: "<EMAIL>",
        enabled: false,
        isAdmin: false,
        deviceCount: 1,
        lastLogin: "2024-01-10T15:45:00Z"
      }
    ]);
  };

  const loadDevices = async () => {
    // Mock data - replace with actual API calls
    setDevices([
      {
        id: "1681512408",
        name: "Samsung A14 - Sidney",
        userId: "user1",
        online: true,
        lastSeen: "2024-01-15T10:30:00Z",
        platform: "Android"
      },
      {
        id: "987654321",
        name: "Samsung A14 - Test",
        userId: "user2",
        online: false,
        lastSeen: "2024-01-10T15:45:00Z",
        platform: "Android"
      }
    ]);
  };

  const loadSubscriptions = async () => {
    // Load subscription data from Spear database
    try {
      const response = await fetch('/api/admin/subscriptions');
      const data = await response.json();
      setSubscriptions(data);
    } catch (error) {
      console.error("Error loading subscriptions:", error);
      // Mock data for development
      setSubscriptions([
        {
          userId: "user1",
          email: "<EMAIL>",
          isActive: true,
          planType: "Single User Plan",
          expiresAt: "2024-02-15T00:00:00Z"
        },
        {
          userId: "user2",
          email: "<EMAIL>", 
          isActive: false,
          planType: "Single User Plan",
          expiresAt: "2024-01-10T00:00:00Z"
        }
      ]);
    }
  };

  const checkServerStatus = async () => {
    try {
      const config = getRustDeskConfig();
      const response = await fetch(`/api/rustdesk/status`);
      setServerStatus(response.ok ? "online" : "offline");
    } catch (error) {
      setServerStatus("offline");
    }
  };

  const toggleUserAccess = async (userId: string, enabled: boolean) => {
    try {
      // Call RustDesk API to enable/disable user
      const response = await fetch(`/api/rustdesk/users/${userId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled })
      });

      if (response.ok) {
        // Update local state
        setUsers(users.map(user => 
          user.id === userId ? { ...user, enabled } : user
        ));
      }
    } catch (error) {
      console.error("Error toggling user access:", error);
    }
  };

  const syncSubscriptionAccess = async () => {
    setIsLoading(true);
    try {
      // Sync subscription status with RustDesk access
      for (const subscription of subscriptions) {
        const user = users.find(u => u.email === subscription.email);
        if (user && user.enabled !== subscription.isActive) {
          await toggleUserAccess(user.id, subscription.isActive);
        }
      }
      await loadUsers();
    } catch (error) {
      console.error("Error syncing subscription access:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const openWebConsole = () => {
    window.open(consoleUrl, '_blank', 'noopener,noreferrer');
  };

  const connectToDevice = async (deviceId: string, deviceName: string) => {
    try {
      // Option 1: Direct RustDesk web client connection
      const config = getRustDeskConfig();
      const rustdeskWebUrl = `http://${config.serverIp}:21114/static/index.html#/devices`;

      // Open RustDesk console in new window focused on devices
      const rustdeskWindow = window.open(
        rustdeskWebUrl,
        'rustdesk-console',
        'width=1200,height=800,scrollbars=yes,resizable=yes'
      );

      if (rustdeskWindow) {
        // Focus the window
        rustdeskWindow.focus();

        // Show connection instructions to admin
        alert(`Connection Instructions:
1. RustDesk console opened in new window
2. Find device ID: ${deviceId} (${deviceName})
3. Click on the device ID to connect
4. Enter device password when prompted
5. Connection will be established`);
      }
    } catch (error) {
      console.error('Error connecting to device:', error);
      alert('Failed to open RustDesk console. Please try again.');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "online":
        return <Badge className="bg-green-100 text-green-800">Online</Badge>;
      case "offline":
        return <Badge className="bg-red-100 text-red-800">Offline</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">RustDesk Pro Console</h2>
          <p className="text-gray-600">Manage client device access and subscriptions</p>
        </div>
        <div className="flex items-center space-x-4">
          {getStatusBadge(serverStatus)}
          <Button onClick={openWebConsole} className="flex items-center">
            <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
            Open Web Console
          </Button>
        </div>
      </div>

      {/* Server Status Alert */}
      {serverStatus === "offline" && (
        <Alert>
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertDescription>
            RustDesk server is offline. Please check server status and connectivity.
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-4 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <UserGroupIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{users.length}</div>
                <p className="text-xs text-muted-foreground">
                  {users.filter(u => u.enabled).length} active
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Devices</CardTitle>
                <ComputerDesktopIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{devices.length}</div>
                <p className="text-xs text-muted-foreground">
                  {devices.filter(d => d.online).length} online
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
                <CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {subscriptions.filter(s => s.isActive).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  of {subscriptions.length} total
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>User Management</CardTitle>
                <Button onClick={syncSubscriptionAccess} disabled={isLoading}>
                  Sync Subscription Access
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">{user.name}</h3>
                      <p className="text-sm text-gray-600">{user.email}</p>
                      <p className="text-xs text-gray-500">
                        {user.deviceCount} device(s) • Last login: {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(user.enabled ? "online" : "offline")}
                      <Button
                        variant={user.enabled ? "destructive" : "default"}
                        size="sm"
                        onClick={() => toggleUserAccess(user.id, !user.enabled)}
                      >
                        {user.enabled ? "Disable" : "Enable"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="devices">
          <Card>
            <CardHeader>
              <CardTitle>Device Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {devices.map((device) => (
                  <div key={device.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">{device.name}</h3>
                      <p className="text-sm text-gray-600">ID: {device.id}</p>
                      <p className="text-xs text-gray-500">
                        {device.platform} • Last seen: {new Date(device.lastSeen).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(device.online ? "online" : "offline")}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => connectToDevice(device.id, device.name)}
                      >
                        Connect
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscriptions">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {subscriptions.map((subscription) => (
                  <div key={subscription.userId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">{subscription.email}</h3>
                      <p className="text-sm text-gray-600">{subscription.planType}</p>
                      {subscription.expiresAt && (
                        <p className="text-xs text-gray-500">
                          Expires: {new Date(subscription.expiresAt).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(subscription.isActive ? "online" : "offline")}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
