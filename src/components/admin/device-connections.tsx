"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  LinkIcon,
  DevicePhoneMobileIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  PlayIcon,
  StopIcon,
  WifiIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";

interface DeviceConnection {
  id: string;
  deviceName: string;
  model: string;
  rustDeskId: string;
  deviceStatus: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  currentOrder?: {
    id: string;
    status: string;
    subscriptionPlan: string;
    createdAt: string;
  };
  connectionStatus: string;
  lastUpdated: string;
  serverType: string;
}

interface ConnectionStats {
  totalConnections: number;
  activeConnections: number;
  pendingConnections: number;
}

export function DeviceConnections() {
  const [connections, setConnections] = useState<DeviceConnection[]>([]);
  const [stats, setStats] = useState<ConnectionStats>({
    totalConnections: 0,
    activeConnections: 0,
    pendingConnections: 0,
  });
  const [loading, setLoading] = useState(true);
  const [selectedConnection, setSelectedConnection] = useState<DeviceConnection | null>(null);
  const [actionDialog, setActionDialog] = useState<{
    open: boolean;
    action: string;
    title: string;
    description: string;
  }>({
    open: false,
    action: '',
    title: '',
    description: '',
  });
  const [notes, setNotes] = useState('');
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchConnections();
  }, []);

  const fetchConnections = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/device-connections');
      if (!response.ok) {
        throw new Error('Failed to fetch device connections');
      }
      
      const data = await response.json();
      setConnections(data.connections);
      setStats(data.stats);
    } catch (error) {
      console.error('Error fetching device connections:', error);
      toast.error('Failed to load device connections');
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async (connection: DeviceConnection, action: string) => {
    setSelectedConnection(connection);
    setNotes('');

    const actionConfig = {
      activate_connection: {
        title: 'Activate Device Connection',
        description: `Activate remote access for ${connection.user.name} to device ${connection.rustDeskId}. This will mark the service as active and send a notification email.`,
      },
      deactivate_connection: {
        title: 'Deactivate Device Connection',
        description: `Deactivate remote access for ${connection.user.name} to device ${connection.rustDeskId}. This will suspend their access.`,
      },
      test_connection: {
        title: 'Test Device Connection',
        description: `Test connectivity to device ${connection.rustDeskId}. This will verify the device is reachable and responsive.`,
      },
    };

    const config = actionConfig[action as keyof typeof actionConfig];
    if (config) {
      setActionDialog({
        open: true,
        action,
        title: config.title,
        description: config.description,
      });
    }
  };

  const executeAction = async () => {
    if (!selectedConnection || !actionDialog.action) return;

    try {
      setProcessing(true);
      const response = await fetch('/api/admin/device-connections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId: selectedConnection.id,
          action: actionDialog.action,
          notes: notes.trim() || undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to execute action');
      }

      const result = await response.json();
      
      if (actionDialog.action === 'test_connection') {
        toast.success(`Connection test completed: ${result.status}`);
      } else {
        toast.success(`Device connection ${actionDialog.action.replace('_', ' ')} successful`);
        await fetchConnections(); // Refresh data
      }

      setActionDialog({ open: false, action: '', title: '', description: '' });
      setSelectedConnection(null);
      setNotes('');
    } catch (error) {
      console.error('Error executing action:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to execute action');
    } finally {
      setProcessing(false);
    }
  };

  const getConnectionStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircleIcon className="h-3 w-3 mr-1" />Connected</Badge>;
      case 'pending':
        return <Badge variant="secondary"><ClockIcon className="h-3 w-3 mr-1" />Pending</Badge>;
      default:
        return <Badge variant="outline"><XCircleIcon className="h-3 w-3 mr-1" />Unknown</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Device Connections</h2>
        <p className="text-muted-foreground">
          Manage user access to their assigned devices
        </p>
      </div>

      {/* Server Status Notice */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-900">Current Server Configuration</h3>
              <p className="text-sm text-blue-700 mt-1">
                Currently using public RustDesk servers. When you upgrade to custom server, 
                this system will automatically manage user access control on your dedicated server.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Connections</CardTitle>
            <LinkIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalConnections}</div>
            <p className="text-xs text-muted-foreground">User-device pairs</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.activeConnections}</div>
            <p className="text-xs text-muted-foreground">Ready for remote access</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <ClockIcon className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pendingConnections}</div>
            <p className="text-xs text-muted-foreground">Awaiting activation</p>
          </CardContent>
        </Card>
      </div>

      {/* Connections Table */}
      <Card>
        <CardHeader>
          <CardTitle>Device Connections</CardTitle>
          <CardDescription>
            Manage user access to their assigned devices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Device</TableHead>
                <TableHead>User</TableHead>
                <TableHead>RustDesk ID</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      <span className="ml-2">Loading connections...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : connections.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    No device connections found. Devices will appear here when assigned to orders.
                  </TableCell>
                </TableRow>
              ) : (
                connections.map((connection) => (
                  <TableRow key={connection.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{connection.deviceName}</div>
                        <div className="text-sm text-muted-foreground">{connection.model}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{connection.user.name}</div>
                        <div className="text-sm text-muted-foreground">{connection.user.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-mono text-sm">{connection.rustDeskId}</div>
                    </TableCell>
                    <TableCell>
                      {getConnectionStatusBadge(connection.connectionStatus)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{formatDate(connection.lastUpdated)}</div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        {connection.connectionStatus === 'pending' ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleAction(connection, 'activate_connection')}
                          >
                            <PlayIcon className="h-4 w-4 mr-1" />
                            Activate
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleAction(connection, 'deactivate_connection')}
                          >
                            <StopIcon className="h-4 w-4 mr-1" />
                            Deactivate
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAction(connection, 'test_connection')}
                        >
                          <WifiIcon className="h-4 w-4 mr-1" />
                          Test
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Action Dialog */}
      <Dialog open={actionDialog.open} onOpenChange={(open) => {
        if (!open) {
          setActionDialog({ open: false, action: '', title: '', description: '' });
          setSelectedConnection(null);
          setNotes('');
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{actionDialog.title}</DialogTitle>
            <DialogDescription>{actionDialog.description}</DialogDescription>
          </DialogHeader>
          
          {actionDialog.action !== 'test_connection' && (
            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any notes about this action..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
              />
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setActionDialog({ open: false, action: '', title: '', description: '' })}
            >
              Cancel
            </Button>
            <Button onClick={executeAction} disabled={processing}>
              {processing ? 'Processing...' : 'Confirm'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
