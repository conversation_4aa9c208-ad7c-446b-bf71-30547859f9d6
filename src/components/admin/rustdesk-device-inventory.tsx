"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";
import {
  DevicePhoneMobileIcon,
  PlusIcon,
  CheckCircleIcon,
  ClockIcon,
  TruckIcon,
  LinkIcon,
} from "@heroicons/react/24/outline";

interface RustDeskDevice {
  id: string;
  name: string;
  model: string;
  rustDeskId: string;
  status: string;
  hasCredentials: boolean;
  createdAt: string;
  assignedTo?: {
    id: string;
    name: string;
    email: string;
  };
  currentOrder?: {
    id: string;
    status: string;
    user: {
      name: string;
      email: string;
    };
  };
}

interface DeviceStats {
  total: number;
  available: number;
  assigned: number;
  shipped: number;
  active: number;
}

export function RustDeskDeviceInventory() {
  const [devices, setDevices] = useState<RustDeskDevice[]>([]);
  const [stats, setStats] = useState<DeviceStats>({
    total: 0,
    available: 0,
    assigned: 0,
    shipped: 0,
    active: 0,
  });
  const [loading, setLoading] = useState(true);
  const [isAddDeviceOpen, setIsAddDeviceOpen] = useState(false);
  const [adding, setAdding] = useState(false);

  // Form state
  const [rustDeskId, setRustDeskId] = useState('');
  const [password, setPassword] = useState('');
  const [deviceName, setDeviceName] = useState('');
  const [model, setModel] = useState('Samsung Galaxy A14');

  useEffect(() => {
    fetchDevices();
  }, []);

  const fetchDevices = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/devices/rustdesk');
      if (!response.ok) {
        throw new Error('Failed to fetch devices');
      }
      
      const data = await response.json();
      setDevices(data.devices);
      setStats(data.stats);
    } catch (error) {
      console.error('Error fetching devices:', error);
      toast.error('Failed to load devices');
    } finally {
      setLoading(false);
    }
  };

  const addDevice = async () => {
    if (!rustDeskId.trim() || !password.trim()) {
      toast.error('RustDesk ID and password are required');
      return;
    }

    try {
      setAdding(true);
      const response = await fetch('/api/admin/devices/rustdesk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rustDeskId: rustDeskId.trim(),
          password: password.trim(),
          deviceName: deviceName.trim() || undefined,
          model: model.trim() || undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to add device');
      }

      toast.success('Device added successfully');
      setIsAddDeviceOpen(false);
      setRustDeskId('');
      setPassword('');
      setDeviceName('');
      setModel('Samsung Galaxy A14');
      await fetchDevices();
    } catch (error) {
      console.error('Error adding device:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to add device');
    } finally {
      setAdding(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      available: { label: 'Available', variant: 'default' as const, icon: CheckCircleIcon, color: 'text-green-600' },
      assigned: { label: 'Assigned', variant: 'secondary' as const, icon: DevicePhoneMobileIcon, color: 'text-blue-600' },
      shipped: { label: 'Shipped', variant: 'default' as const, icon: TruckIcon, color: 'text-orange-600' },
      active: { label: 'Active', variant: 'default' as const, icon: LinkIcon, color: 'text-purple-600' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      variant: 'outline' as const,
      icon: ClockIcon,
      color: 'text-gray-600',
    };

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <config.icon className={`h-3 w-3 ${config.color}`} />
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">RustDesk Device Inventory</h2>
          <p className="text-muted-foreground">
            Manage physically configured devices for order fulfillment
          </p>
        </div>
        <Dialog open={isAddDeviceOpen} onOpenChange={setIsAddDeviceOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Device
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add RustDesk Device</DialogTitle>
              <DialogDescription>
                Add a physically configured device to the inventory for order assignment
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="rustdesk-id">RustDesk ID *</Label>
                <Input
                  id="rustdesk-id"
                  placeholder="123456789"
                  value={rustDeskId}
                  onChange={(e) => setRustDeskId(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  The unique ID from the RustDesk app on the device
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">RustDesk Password *</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter device password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <p className="text-xs text-muted-foreground">
                  The password set for this device in RustDesk
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="device-name">Device Name (Optional)</Label>
                <Input
                  id="device-name"
                  placeholder="SPEAR Device 001"
                  value={deviceName}
                  onChange={(e) => setDeviceName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Input
                  id="model"
                  placeholder="Samsung Galaxy A14"
                  value={model}
                  onChange={(e) => setModel(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDeviceOpen(false)}>
                Cancel
              </Button>
              <Button onClick={addDevice} disabled={adding || !rustDeskId.trim() || !password.trim()}>
                {adding ? 'Adding...' : 'Add Device'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <DevicePhoneMobileIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">Devices in inventory</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.available}</div>
            <p className="text-xs text-muted-foreground">Ready to assign</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned</CardTitle>
            <DevicePhoneMobileIcon className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.assigned}</div>
            <p className="text-xs text-muted-foreground">Assigned to orders</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shipped</CardTitle>
            <TruckIcon className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.shipped}</div>
            <p className="text-xs text-muted-foreground">In transit</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <LinkIcon className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{stats.active}</div>
            <p className="text-xs text-muted-foreground">In use</p>
          </CardContent>
        </Card>
      </div>

      {/* Devices Table */}
      <Card>
        <CardHeader>
          <CardTitle>Device Inventory</CardTitle>
          <CardDescription>
            Physically configured devices ready for order assignment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Device</TableHead>
                <TableHead>RustDesk ID</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Assigned To</TableHead>
                <TableHead>Added</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      <span className="ml-2">Loading devices...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : devices.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                    No devices in inventory. Add your first configured device above.
                  </TableCell>
                </TableRow>
              ) : (
                devices.map((device) => (
                  <TableRow key={device.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{device.name}</div>
                        <div className="text-sm text-muted-foreground">{device.model}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-mono text-sm">{device.rustDeskId}</div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(device.status)}
                    </TableCell>
                    <TableCell>
                      {device.currentOrder ? (
                        <div>
                          <div className="font-medium">{device.currentOrder.user.name}</div>
                          <div className="text-sm text-muted-foreground">{device.currentOrder.user.email}</div>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Unassigned</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{formatDate(device.createdAt)}</div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
