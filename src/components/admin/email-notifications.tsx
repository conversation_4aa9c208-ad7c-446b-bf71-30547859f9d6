"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  EnvelopeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  PaperAirplaneIcon,
} from "@heroicons/react/24/outline";

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  description: string;
  status: 'active' | 'draft';
}

const emailTemplates: EmailTemplate[] = [
  {
    id: 'payment_confirmation',
    name: 'Payment Confirmation',
    subject: '🎉 Payment Confirmed - Your SPEAR Subscription is Active!',
    description: 'Sent immediately after successful payment',
    status: 'active',
  },
  {
    id: 'device_shipped',
    name: 'Device Shipped',
    subject: '🚚 Your SPEAR Device Has Been Shipped!',
    description: 'Sent when admin marks order as shipped with tracking',
    status: 'active',
  },
  {
    id: 'device_delivered',
    name: 'Device Delivered',
    subject: '📦 Your SPEAR Device Has Been Delivered!',
    description: 'Sent when admin marks order as delivered',
    status: 'active',
  },
  {
    id: 'service_activated',
    name: 'Service Activated',
    subject: '🎉 Your SPEAR Service is Now Active!',
    description: 'Sent when admin marks order as active',
    status: 'active',
  },
];

export function EmailNotifications() {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [testEmail, setTestEmail] = useState('');
  const [sending, setSending] = useState(false);

  const sendTestEmail = async () => {
    if (!testEmail || !selectedTemplate) {
      toast.error('Please select a template and enter an email address');
      return;
    }

    setSending(true);
    try {
      const response = await fetch('/api/admin/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template: selectedTemplate,
          email: testEmail,
        }),
      });

      if (response.ok) {
        toast.success(`Test email sent to ${testEmail}`);
        setTestEmail('');
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to send test email');
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      toast.error('Failed to send test email');
    } finally {
      setSending(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Email Notifications</h2>
        <p className="text-muted-foreground">
          Manage automated email notifications for the order fulfillment workflow
        </p>
      </div>

      {/* Email Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Templates</CardTitle>
            <CheckCircleIcon className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {emailTemplates.filter(t => t.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">Email templates enabled</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Email Service</CardTitle>
            <InformationCircleIcon className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {process.env.SMTP_USER ? 'Configured' : 'Not Set'}
            </div>
            <p className="text-xs text-muted-foreground">SMTP configuration status</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivery Rate</CardTitle>
            <EnvelopeIcon className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">99%</div>
            <p className="text-xs text-muted-foreground">Estimated delivery success</p>
          </CardContent>
        </Card>
      </div>

      {/* Email Templates */}
      <Card>
        <CardHeader>
          <CardTitle>Email Templates</CardTitle>
          <CardDescription>
            Automated emails sent during the order fulfillment process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {emailTemplates.map((template) => (
              <div key={template.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h3 className="font-medium">{template.name}</h3>
                    {getStatusBadge(template.status)}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">{template.description}</p>
                  <p className="text-xs text-muted-foreground mt-1 font-mono">{template.subject}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    Preview
                  </Button>
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Test Email */}
      <Card>
        <CardHeader>
          <CardTitle>Test Email Delivery</CardTitle>
          <CardDescription>
            Send test emails to verify your email configuration and templates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="template">Email Template</Label>
              <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a template" />
                </SelectTrigger>
                <SelectContent>
                  {emailTemplates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="test-email">Test Email Address</Label>
              <Input
                id="test-email"
                type="email"
                placeholder="<EMAIL>"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
              />
            </div>
          </div>

          <Button 
            onClick={sendTestEmail} 
            disabled={!selectedTemplate || !testEmail || sending}
            className="w-full md:w-auto"
          >
            <PaperAirplaneIcon className="h-4 w-4 mr-2" />
            {sending ? 'Sending...' : 'Send Test Email'}
          </Button>
        </CardContent>
      </Card>

      {/* Email Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Email Configuration</CardTitle>
          <CardDescription>
            SMTP settings for email delivery (configured via environment variables)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>SMTP Host</Label>
                <div className="mt-1 p-2 bg-muted rounded text-sm">
                  {process.env.SMTP_HOST || 'Not configured'}
                </div>
              </div>
              <div>
                <Label>SMTP Port</Label>
                <div className="mt-1 p-2 bg-muted rounded text-sm">
                  {process.env.SMTP_PORT || 'Not configured'}
                </div>
              </div>
              <div>
                <Label>From Email</Label>
                <div className="mt-1 p-2 bg-muted rounded text-sm">
                  {process.env.FROM_EMAIL || '<EMAIL>'}
                </div>
              </div>
              <div>
                <Label>From Name</Label>
                <div className="mt-1 p-2 bg-muted rounded text-sm">
                  {process.env.FROM_NAME || 'SPEAR Team'}
                </div>
              </div>
            </div>

            {!process.env.SMTP_USER && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex">
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2" />
                  <div>
                    <h3 className="text-sm font-medium text-yellow-800">Email Not Configured</h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      Set SMTP_USER, SMTP_PASS, and other email environment variables to enable email notifications.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
