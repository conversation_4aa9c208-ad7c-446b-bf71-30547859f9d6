"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  DevicePhoneMobileIcon,
  LinkIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon,
  ArrowTopRightOnSquareIcon,
  DocumentTextIcon,
  QuestionMarkCircleIcon,
} from "@heroicons/react/24/outline";

interface DeviceConnectionInfo {
  deviceId: string;
  deviceName: string;
  model: string;
  connectionStatus: 'active' | 'pending' | 'inactive';
  serverType: 'public' | 'custom';
  connectionInstructions?: string;
  supportContact?: string;
}

interface DeviceConnectionStatusProps {
  device?: {
    id: string;
    name: string;
    model?: string;
    rustDeskId?: string;
    status: string;
  };
  orderStatus?: string;
}

export function DeviceConnectionStatus({ device, orderStatus }: DeviceConnectionStatusProps) {
  const [connectionInfo, setConnectionInfo] = useState<DeviceConnectionInfo | null>(null);
  const [showInstructions, setShowInstructions] = useState(false);

  useEffect(() => {
    if (device?.rustDeskId) {
      // Determine connection status based on device and order status
      let connectionStatus: 'active' | 'pending' | 'inactive' = 'pending';
      
      if (orderStatus === 'active' && device.status === 'active') {
        connectionStatus = 'active';
      } else if (device.status === 'assigned' || device.status === 'shipped' || device.status === 'delivered') {
        connectionStatus = 'pending';
      } else {
        connectionStatus = 'inactive';
      }

      setConnectionInfo({
        deviceId: device.rustDeskId,
        deviceName: device.name,
        model: device.model || 'Samsung Galaxy A14',
        connectionStatus,
        serverType: 'public', // Will be 'custom' when you upgrade
        connectionInstructions: getConnectionInstructions(connectionStatus),
        supportContact: '<EMAIL>',
      });
    }
  }, [device, orderStatus]);

  const getConnectionInstructions = (status: 'active' | 'pending' | 'inactive') => {
    switch (status) {
      case 'active':
        return `Your device is ready for remote access! Download the RustDesk app and connect using Device ID: ${device?.rustDeskId}`;
      case 'pending':
        return 'Your device is being prepared for remote access. You\'ll receive an email when it\'s ready to connect.';
      case 'inactive':
        return 'Device connection is currently inactive. Please contact support if you need assistance.';
      default:
        return 'Connection status unknown. Please contact support.';
    }
  };

  const getStatusBadge = (status: 'active' | 'pending' | 'inactive') => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircleIcon className="h-3 w-3 mr-1" />
            Connected
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="secondary">
            <ClockIcon className="h-3 w-3 mr-1" />
            Pending Setup
          </Badge>
        );
      case 'inactive':
        return (
          <Badge variant="destructive">
            <ExclamationCircleIcon className="h-3 w-3 mr-1" />
            Inactive
          </Badge>
        );
    }
  };

  const openRustDeskDownload = () => {
    window.open('https://rustdesk.com/download', '_blank');
  };

  const contactSupport = () => {
    window.open(`mailto:${connectionInfo?.supportContact}?subject=SPEAR Device Connection Support&body=Device ID: ${connectionInfo?.deviceId}%0D%0ADevice Name: ${connectionInfo?.deviceName}%0D%0A%0D%0APlease describe your issue:`, '_blank');
  };

  if (!connectionInfo) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DevicePhoneMobileIcon className="h-5 w-5 mr-2" />
            Device Connection
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Device connection information will appear here once your device is assigned.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <LinkIcon className="h-5 w-5 mr-2" />
            Remote Access Connection
          </div>
          {getStatusBadge(connectionInfo.connectionStatus)}
        </CardTitle>
        <CardDescription>
          Connect to your SPEAR device remotely from anywhere
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Device Info */}
        <div className="bg-muted p-4 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-semibold text-sm">Device Information</h4>
              <div className="mt-2 space-y-1 text-sm">
                <p><span className="text-muted-foreground">Name:</span> {connectionInfo.deviceName}</p>
                <p><span className="text-muted-foreground">Model:</span> {connectionInfo.model}</p>
                <p><span className="text-muted-foreground">Server:</span> {connectionInfo.serverType === 'public' ? 'Public RustDesk' : 'SPEAR Custom Server'}</p>
              </div>
            </div>
            <div>
              <h4 className="font-semibold text-sm">Connection Details</h4>
              <div className="mt-2 space-y-1 text-sm">
                <p><span className="text-muted-foreground">Device ID:</span> 
                  <span className="ml-1 font-mono bg-background px-2 py-1 rounded border">
                    {connectionInfo.deviceId}
                  </span>
                </p>
                <p><span className="text-muted-foreground">Status:</span> {connectionInfo.connectionStatus}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Status-specific content */}
        {connectionInfo.connectionStatus === 'active' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-semibold text-green-800">Ready to Connect!</h4>
                <p className="text-sm text-green-700 mt-1">
                  Your device is active and ready for remote access. Download RustDesk and use your Device ID to connect.
                </p>
                <div className="mt-3 flex flex-wrap gap-2">
                  <Button size="sm" onClick={openRustDeskDownload}>
                    <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-1" />
                    Download RustDesk
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setShowInstructions(!showInstructions)}>
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    {showInstructions ? 'Hide' : 'Show'} Instructions
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {connectionInfo.connectionStatus === 'pending' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <ClockIcon className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-blue-800">Setup in Progress</h4>
                <p className="text-sm text-blue-700 mt-1">
                  Your device is being prepared for remote access. You'll receive an email notification when it's ready to connect.
                </p>
              </div>
            </div>
          </div>
        )}

        {connectionInfo.connectionStatus === 'inactive' && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <ExclamationCircleIcon className="h-5 w-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-red-800">Connection Inactive</h4>
                <p className="text-sm text-red-700 mt-1">
                  Your device connection is currently inactive. Please contact support for assistance.
                </p>
                <Button variant="outline" size="sm" className="mt-2" onClick={contactSupport}>
                  Contact Support
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Connection Instructions */}
        {showInstructions && connectionInfo.connectionStatus === 'active' && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-semibold flex items-center">
                <DocumentTextIcon className="h-4 w-4 mr-2" />
                Connection Instructions
              </h4>
              <ol className="list-decimal list-inside space-y-2 text-sm">
                <li>Download and install RustDesk from <a href="https://rustdesk.com/download" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">rustdesk.com</a></li>
                <li>Open RustDesk on your computer or mobile device</li>
                <li>In the "Remote Desktop" section, enter your Device ID: <span className="font-mono bg-muted px-1 rounded">{connectionInfo.deviceId}</span></li>
                <li>Click "Connect" and enter the password when prompted</li>
                <li>You should now have remote access to your SPEAR device!</li>
              </ol>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded p-3 mt-4">
                <div className="flex items-start space-x-2">
                  <QuestionMarkCircleIcon className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-yellow-800">Need the password?</p>
                    <p className="text-yellow-700">The device password was included with your shipment. If you can't find it, contact support.</p>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Support */}
        <Separator />
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Need help connecting? Our support team is here to assist you.
          </div>
          <Button variant="outline" size="sm" onClick={contactSupport}>
            Contact Support
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
