"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ArrowTopRightOnSquareIcon,
  MapPinIcon,
  CalendarIcon,
} from "@heroicons/react/24/outline";

interface TrackingEvent {
  date: string;
  status: string;
  description: string;
  location?: string;
}

interface TrackingInfo {
  trackingNumber: string;
  carrier: string;
  status: 'shipped' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'exception' | 'unknown';
  statusDescription: string;
  estimatedDelivery?: string;
  lastUpdate: string;
  trackingUrl: string;
  events: TrackingEvent[];
}

interface ShippingTrackerProps {
  orderId?: string;
  trackingNumber?: string;
  orderStatus?: string;
}

export function ShippingTracker({ orderId, trackingNumber, orderStatus }: ShippingTrackerProps) {
  const [trackingInfo, setTrackingInfo] = useState<TrackingInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if ((orderId || trackingNumber) && (orderStatus === 'shipped' || orderStatus === 'delivered')) {
      fetchTrackingInfo();
    }
  }, [orderId, trackingNumber, orderStatus]);

  const fetchTrackingInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (orderId) params.append('orderId', orderId);
      if (trackingNumber) params.append('trackingNumber', trackingNumber);

      const response = await fetch(`/api/shipping/tracking?${params}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch tracking info');
      }

      const data = await response.json();
      setTrackingInfo(data.trackingInfo);
    } catch (err) {
      console.error('Error fetching tracking info:', err);
      setError(err instanceof Error ? err.message : 'Failed to load tracking information');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'out_for_delivery':
        return <TruckIcon className="h-5 w-5 text-blue-600" />;
      case 'in_transit':
        return <TruckIcon className="h-5 w-5 text-orange-600" />;
      case 'shipped':
        return <ClockIcon className="h-5 w-5 text-gray-600" />;
      case 'exception':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'delivered':
        return <Badge variant="default" className="bg-green-100 text-green-800">Delivered</Badge>;
      case 'out_for_delivery':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Out for Delivery</Badge>;
      case 'in_transit':
        return <Badge variant="default" className="bg-orange-100 text-orange-800">In Transit</Badge>;
      case 'shipped':
        return <Badge variant="secondary">Shipped</Badge>;
      case 'exception':
        return <Badge variant="destructive">Exception</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const openTrackingUrl = () => {
    if (trackingInfo?.trackingUrl) {
      window.open(trackingInfo.trackingUrl, '_blank');
    }
  };

  // Don't show if order hasn't shipped yet
  if (!trackingNumber && orderStatus !== 'shipped' && orderStatus !== 'delivered') {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <TruckIcon className="h-5 w-5 mr-2" />
            Package Tracking
          </div>
          {trackingInfo && getStatusBadge(trackingInfo.status)}
        </CardTitle>
        <CardDescription>
          Track your SPEAR device shipment in real-time
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {loading && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2">Loading tracking information...</span>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-red-800">Tracking Error</h4>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                <Button variant="outline" size="sm" className="mt-2" onClick={fetchTrackingInfo}>
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        )}

        {trackingInfo && (
          <>
            {/* Tracking Summary */}
            <div className="bg-muted p-4 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-sm">Tracking Information</h4>
                  <div className="mt-2 space-y-1 text-sm">
                    <p><span className="text-muted-foreground">Tracking Number:</span> 
                      <span className="ml-1 font-mono">{trackingInfo.trackingNumber}</span>
                    </p>
                    <p><span className="text-muted-foreground">Carrier:</span> {trackingInfo.carrier}</p>
                    <p><span className="text-muted-foreground">Status:</span> {trackingInfo.statusDescription}</p>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-sm">Delivery Information</h4>
                  <div className="mt-2 space-y-1 text-sm">
                    {trackingInfo.estimatedDelivery && (
                      <p><span className="text-muted-foreground">Estimated Delivery:</span> {trackingInfo.estimatedDelivery}</p>
                    )}
                    <p><span className="text-muted-foreground">Last Update:</span> {formatDate(trackingInfo.lastUpdate)}</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <Button variant="outline" size="sm" onClick={openTrackingUrl}>
                  <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-1" />
                  Track on {trackingInfo.carrier} Website
                </Button>
              </div>
            </div>

            {/* Status-specific messages */}
            {trackingInfo.status === 'delivered' && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-green-800">Package Delivered!</h4>
                    <p className="text-sm text-green-700 mt-1">
                      Your SPEAR device has been delivered. You should receive an activation email shortly.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {trackingInfo.status === 'out_for_delivery' && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <TruckIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-blue-800">Out for Delivery</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Your package is out for delivery and should arrive today!
                    </p>
                  </div>
                </div>
              </div>
            )}

            {trackingInfo.status === 'exception' && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <h4 className="font-semibold text-red-800">Delivery Exception</h4>
                    <p className="text-sm text-red-700 mt-1">
                      There's an issue with your delivery. Please check the tracking details or contact support.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Tracking Events */}
            {trackingInfo.events && trackingInfo.events.length > 0 && (
              <>
                <Separator />
                <div>
                  <h4 className="font-semibold mb-3">Tracking History</h4>
                  <div className="space-y-3">
                    {trackingInfo.events.map((event, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <p className="font-medium text-sm">{event.description}</p>
                            <div className="flex items-center text-xs text-muted-foreground">
                              <CalendarIcon className="h-3 w-3 mr-1" />
                              {formatDate(event.date)}
                            </div>
                          </div>
                          {event.location && (
                            <div className="flex items-center text-xs text-muted-foreground mt-1">
                              <MapPinIcon className="h-3 w-3 mr-1" />
                              {event.location}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </>
        )}

        {!trackingInfo && !loading && !error && trackingNumber && (
          <div className="text-center py-4">
            <p className="text-muted-foreground mb-4">
              Tracking information will be available once your package is processed by the carrier.
            </p>
            <Button variant="outline" onClick={fetchTrackingInfo}>
              Check for Updates
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
