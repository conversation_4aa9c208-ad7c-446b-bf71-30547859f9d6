"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowPathIcon, ArrowTopRightOnSquareIcon } from "@heroicons/react/24/outline";
import { connectToRustDeskDevice, formatRustDeskDeviceId, getRustDeskWebClientUrl, trackRustDeskSession } from "@/lib/rustdesk-api";

interface RustDeskConnectionProps {
  deviceId: string;
  deviceName: string;
  password?: string;
}

export function RustDeskConnection({ deviceId, deviceName, password }: RustDeskConnectionProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionTime, setConnectionTime] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [connectionUrl, setConnectionUrl] = useState<string | null>(null);

  // Function to connect directly to the RustDesk web client
  const connectDirectly = () => {
    setIsConnecting(true);
    setError(null);

    try {
      // Format the device ID by removing spaces if present
      const formattedDeviceId = deviceId.replace(/\s+/g, '');

      // Get the RustDesk web client URL
      const url = getRustDeskWebClientUrl(formattedDeviceId, password);

      console.log(`Opening direct connection to RustDesk: ${url}`);

      // Open the RustDesk web client in a new window
      window.open(url, '_blank', 'noopener,noreferrer');

      // Update state to show connected
      setConnectionUrl(url);
      setIsConnected(true);
      setConnectionTime(new Date());

      // Track the session
      trackRustDeskSession(deviceId, deviceName);
    } catch (err) {
      console.error("Error connecting to device:", err);
      setError(err instanceof Error ? err.message : "An error occurred during connection");
    } finally {
      setIsConnecting(false);
    }
  };

  const handleConnect = () => {
    // Log connection attempt for debugging
    console.log(`Connecting to device: ${deviceId} (${deviceName})${password ? ' with password' : ''}`);

    // Use the direct connection method
    connectDirectly();
  };

  const handleDisconnect = () => {
    setIsConnected(false);
    setConnectionTime(null);
    setConnectionUrl(null);
    setError(null);
  };

  // Format the connection time for display
  const formattedConnectionTime = connectionTime
    ? connectionTime.toLocaleTimeString()
    : null;

  return (
    <div className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {!isConnected ? (
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-12 h-12 text-primary mb-4">
                <path fillRule="evenodd" d="M2.25 5.25a3 3 0 013-3h13.5a3 3 0 013 3V15a3 3 0 01-3 3h-3v.257c0 .597.237 1.17.659 1.591l.621.622a.75.75 0 01-.53 1.28h-9a.75.75 0 01-.53-1.28l.621-.622a2.25 2.25 0 00.659-1.59V18h-3a3 3 0 01-3-3V5.25zm1.5 0v9.75c0 .83.67 1.5 1.5 1.5h13.5c.83 0 1.5-.67 1.5-1.5V5.25c0-.83-.67-1.5-1.5-1.5H5.25c-.83 0-1.5.67-1.5 1.5z" clipRule="evenodd" />
              </svg>
              <h3 className="text-xl font-medium mb-2">Remote Control</h3>
              <p className="text-slate-500 dark:text-slate-400 text-center mb-6 max-w-md">
                Connect to {deviceName} to start a remote control session. You'll be able to view and control the device remotely.
              </p>
              <Button
                onClick={handleConnect}
                disabled={isConnecting}
                className="w-full md:w-auto md:min-w-[160px]"
              >
                {isConnecting ? (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  "Connect to RustDesk"
                )}
              </Button>

              {error && (
                <div className="mt-4 pt-4 border-t border-slate-200 dark:border-slate-700 w-full">
                  <p className="text-amber-600 dark:text-amber-400 mb-3">
                    {error}
                  </p>
                  <div className="space-y-3">
                    <Button
                      variant="outline"
                      onClick={handleConnect}
                      className="w-full"
                    >
                      <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
                      Try Again
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center">
              <div className="bg-green-100 dark:bg-green-900 p-3 rounded-full mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-8 h-8 text-green-600 dark:text-green-400">
                  <path fillRule="evenodd" d="M2.25 5.25a3 3 0 013-3h13.5a3 3 0 013 3V15a3 3 0 01-3 3h-3v.257c0 .597.237 1.17.659 1.591l.621.622a.75.75 0 01-.53 1.28h-9a.75.75 0 01-.53-1.28l.621-.622a2.25 2.25 0 00.659-1.59V18h-3a3 3 0 01-3-3V5.25zm1.5 0v9.75c0 .83.67 1.5 1.5 1.5h13.5c.83 0 1.5-.67 1.5-1.5V5.25c0-.83-.67-1.5-1.5-1.5H5.25c-.83 0-1.5.67-1.5 1.5z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-medium mb-3">Connected to {deviceName}</h3>
              <p className="text-slate-500 dark:text-slate-400 mb-2">
                Connection established at {formattedConnectionTime}
              </p>
              <p className="text-slate-300 mb-6">
                Due to security restrictions, RustDesk cannot be embedded directly in this page.
                If the remote control window didn't open automatically, click the button below.
              </p>

              <div className="space-y-3">
                <Button
                  onClick={() => {
                    // Format the device ID by removing spaces if present
                    const formattedDeviceId = deviceId.replace(/\s+/g, '');
                    window.open(getRustDeskWebClientUrl(formattedDeviceId, password), '_blank');
                  }}
                  className="w-full"
                >
                  <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
                  Reopen RustDesk Window
                </Button>
              </div>

              <div className="mt-6">
                <Button
                  onClick={handleDisconnect}
                  variant="outline"
                  className="text-red-500 border-red-500 hover:bg-red-50 dark:hover:bg-red-950"
                >
                  End Session
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}


    </div>
  );
}
