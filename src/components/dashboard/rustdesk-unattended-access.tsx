"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { getRustDeskConfig } from "@/lib/rustdesk-config";
import { connectToRustDeskDevice, formatRustDeskDeviceId, trackRustDeskSession } from "@/lib/rustdesk-api";

export function RustDeskUnattendedAccess() {
  const [isLoading, setIsLoading] = useState(false);
  const [manualDeviceId, setManualDeviceId] = useState("");
  const [manualPassword, setManualPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeSessions, setActiveSessions] = useState<any[]>([]);

  // Get the configured Android device ID
  const { androidDeviceId } = getRustDeskConfig();

  useEffect(() => {
    // If we have a configured device ID, use it as the default
    if (androidDeviceId) {
      setManualDeviceId(androidDeviceId);
    }
  }, [androidDeviceId]);

  const handleConnect = async () => {
    if (!manualDeviceId.trim()) {
      setError("Please enter a device ID");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Format the device ID by removing spaces if present
      const formattedDeviceId = manualDeviceId.replace(/\s+/g, '');

      // Track the session
      const session = trackRustDeskSession(formattedDeviceId, "Manual Device");

      // Add the session to the active sessions list
      setActiveSessions([...activeSessions, session]);

      // Show success message
      setSuccess(`Connection to device ${formatRustDeskDeviceId(formattedDeviceId)} initiated`);

      // Launch RustDesk with the connection URL
      const connectionUrl = connectToRustDeskDevice(formattedDeviceId, manualPassword);
      console.log(`Launching RustDesk with URL: ${connectionUrl}`);
      window.location.href = connectionUrl;
    } catch (err) {
      console.error("Error connecting to device:", err);
      setError(err instanceof Error ? err.message : "An unknown error occurred");

      // Fall back to direct connection if there's an error
      const formattedDeviceId = manualDeviceId.replace(/\s+/g, '');
      const connectionUrl = connectToRustDeskDevice(formattedDeviceId, manualPassword);
      window.location.href = connectionUrl;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>RustDesk Unattended Access</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert variant="default" className="bg-green-50 text-green-800 border-green-200 dark:bg-green-950 dark:text-green-200 dark:border-green-800">
                <AlertDescription>{success}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="deviceId">Device ID</Label>
              <Input
                id="deviceId"
                placeholder="Enter device ID (e.g., 123 456 789)"
                value={manualDeviceId}
                onChange={(e) => setManualDeviceId(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password (optional)</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter password if required"
                value={manualPassword}
                onChange={(e) => setManualPassword(e.target.value)}
              />
            </div>

            <Button
              onClick={handleConnect}
              disabled={isLoading || !manualDeviceId.trim()}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Connecting...
                </>
              ) : (
                "Connect to Device"
              )}
            </Button>
          </div>

          {activeSessions.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-2">Active Sessions</h3>
              <div className="space-y-2">
                {activeSessions.map((session) => (
                  <div
                    key={session.id}
                    className="p-3 bg-slate-50 dark:bg-slate-800 rounded-md flex justify-between items-center"
                  >
                    <div>
                      <p className="font-medium">{formatRustDeskDeviceId(session.device_id)}</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Started: {new Date(session.created).toLocaleTimeString()}
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-red-500 border-red-500 hover:bg-red-50 dark:hover:bg-red-950"
                      onClick={() => {
                        // Remove the session from the active sessions list
                        setActiveSessions(activeSessions.filter((s) => s.id !== session.id));
                      }}
                    >
                      End
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>RustDesk Server Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {getRustDeskConfig().isUsingCustomServer ? (
              <>
                <div className="flex justify-between">
                  <span className="text-slate-500 dark:text-slate-400">Server Address:</span>
                  <span className="font-medium">{getRustDeskConfig().serverAddress}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500 dark:text-slate-400">API Address:</span>
                  <span className="font-medium">{getRustDeskConfig().apiAddress}</span>
                </div>
              </>
            ) : (
              <div className="flex justify-between">
                <span className="text-slate-500 dark:text-slate-400">Server:</span>
                <span className="font-medium">Using public RustDesk servers</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-slate-500 dark:text-slate-400">Android Device ID:</span>
              <span className="font-medium">
                {androidDeviceId ? formatRustDeskDeviceId(androidDeviceId) : "Not configured"}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
