"use client";

import { useState, useEffect, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface ConnectionOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  children?: ReactNode;
}

export function ConnectionOverlay({ isOpen, onClose, children }: ConnectionOverlayProps) {

  // If not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-slate-900/95 backdrop-blur-sm z-50 flex flex-col items-center justify-center text-white">
      {/* Close button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 text-slate-400 hover:text-white p-2"
        aria-label="Close"
      >
        <X className="h-6 w-6" />
      </button>

      <div className="w-full max-w-4xl p-6">
        {/* Content */}
        {children}
      </div>
    </div>
  );
}
