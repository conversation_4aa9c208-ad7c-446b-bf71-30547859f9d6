import { useEffect, useState, useCallback, useRef } from 'react';

interface OrderStatusUpdate {
  type: 'connected' | 'order_update' | 'heartbeat' | 'error';
  orderId?: string;
  status?: string;
  trackingNumber?: string;
  updatedAt?: string;
  timestamp: string;
  error?: string;
}

interface UseOrderStatusStreamOptions {
  orderId: string;
  enabled?: boolean;
  onUpdate?: (update: OrderStatusUpdate) => void;
  onError?: (error: string) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
}

export function useOrderStatusStream({
  orderId,
  enabled = true,
  onUpdate,
  onError,
  onConnect,
  onDisconnect,
}: UseOrderStatusStreamOptions) {
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<OrderStatusUpdate | null>(null);
  const [error, setError] = useState<string | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (!enabled || !orderId) return;

    // Close existing connection
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    try {
      const eventSource = new EventSource(`/api/orders/${orderId}/status-stream`);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('Order status stream connected');
        setIsConnected(true);
        setError(null);
        reconnectAttempts.current = 0;
        onConnect?.();
      };

      eventSource.onmessage = (event) => {
        try {
          const update: OrderStatusUpdate = JSON.parse(event.data);
          setLastUpdate(update);

          // Handle different update types
          switch (update.type) {
            case 'connected':
              console.log('Connected to order status stream:', update.orderId);
              break;
            case 'order_update':
              console.log('Order status update:', update);
              onUpdate?.(update);
              break;
            case 'heartbeat':
              // Keep connection alive
              break;
            case 'error':
              console.error('Server error:', update.error);
              setError(update.error || 'Server error');
              onError?.(update.error || 'Server error');
              break;
          }
        } catch (parseError) {
          console.error('Error parsing SSE message:', parseError);
          setError('Error parsing server message');
          onError?.('Error parsing server message');
        }
      };

      eventSource.onerror = (event) => {
        console.error('EventSource error:', event);
        setIsConnected(false);
        onDisconnect?.();

        // Attempt to reconnect with exponential backoff
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.pow(2, reconnectAttempts.current) * 1000; // 1s, 2s, 4s, 8s, 16s
          reconnectAttempts.current++;
          
          console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts.current})`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, delay);
        } else {
          setError('Connection lost. Please refresh the page.');
          onError?.('Connection lost. Please refresh the page.');
        }
      };

    } catch (connectionError) {
      console.error('Error creating EventSource:', connectionError);
      setError('Failed to connect to status updates');
      onError?.('Failed to connect to status updates');
    }
  }, [orderId, enabled, onUpdate, onError, onConnect, onDisconnect]);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setIsConnected(false);
    setError(null);
    reconnectAttempts.current = 0;
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => {
      connect();
    }, 1000);
  }, [disconnect, connect]);

  // Connect on mount and when dependencies change
  useEffect(() => {
    if (enabled && orderId) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [orderId, enabled, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    lastUpdate,
    error,
    reconnect,
    disconnect,
  };
}
