import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getPaymentService } from "@/lib/payment/payment-factory";
import { PayPalPaymentService } from "@/lib/payment/providers/paypal-service";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { orderId } = body;

    if (!orderId) {
      return NextResponse.json(
        { error: "Order ID is required" },
        { status: 400 }
      );
    }

    // Get PayPal payment service
    const paymentService = getPaymentService('paypal') as PayPalPaymentService;

    // Capture the payment
    const captureResult = await paymentService.capturePayment(orderId);

    if (captureResult.success) {
      // Update order in database
      try {
        const order = await prisma.order.findFirst({
          where: {
            OR: [
              { paymentId: orderId },
              { paymentId: captureResult.paymentId }
            ],
            userId: session.user.id
          },
          include: {
            user: true
          }
        });

        if (order) {
          await prisma.order.update({
            where: { id: order.id },
            data: {
              status: 'completed',
              paymentId: captureResult.paymentId
            }
          });

          console.log(`✅ Order ${order.id} captured successfully`);

          // Create or update subscription
          await createOrUpdateSubscription(order, captureResult.paymentId);

          return NextResponse.json({
            success: true,
            payment: {
              id: captureResult.paymentId,
              status: captureResult.status,
              amount: captureResult.amount,
              fees: captureResult.fees
            },
            order: {
              id: order.id,
              status: 'completed'
            }
          });

        } else {
          return NextResponse.json(
            { error: "Order not found" },
            { status: 404 }
          );
        }

      } catch (dbError) {
        console.error('Database error during capture:', dbError);
        return NextResponse.json(
          { error: "Database error" },
          { status: 500 }
        );
      }

    } else {
      console.error('PayPal capture failed:', captureResult.error);
      return NextResponse.json(
        { 
          success: false,
          error: captureResult.error || "Payment capture failed" 
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error capturing PayPal payment:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to create or update subscription
async function createOrUpdateSubscription(order: any, paymentId: string) {
  try {
    // Check if subscription already exists
    const existingSubscription = await prisma.subscription.findFirst({
      where: {
        userId: order.userId,
        status: { in: ['active', 'past_due'] }
      }
    });

    if (existingSubscription) {
      // Update existing subscription
      await prisma.subscription.update({
        where: { id: existingSubscription.id },
        data: {
          status: 'active',
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          planType: order.subscriptionPlan || 'single-user',
          paymentMethod: 'paypal',
          paypalCaptureId: paymentId
        }
      });

      console.log(`✅ Updated subscription ${existingSubscription.id}`);
    } else {
      // Create new subscription
      const newSubscription = await prisma.subscription.create({
        data: {
          userId: order.userId,
          status: 'active',
          planType: order.subscriptionPlan || 'single-user',
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          paymentMethod: 'paypal',
          paypalCaptureId: paymentId
        }
      });

      console.log(`✅ Created new subscription ${newSubscription.id}`);
    }
  } catch (error) {
    console.error('Error creating/updating subscription:', error);
  }
}
