import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import crypto from 'crypto';
import { prisma } from '@/lib/prisma';

// PayPal webhook event types we care about
const WEBHOOK_EVENTS = {
  PAYMENT_CAPTURE_COMPLETED: 'PAYMENT.CAPTURE.COMPLETED',
  PAYMENT_CAPTURE_DENIED: 'PAYMENT.CAPTURE.DENIED',
  PAYMENT_CAPTURE_PENDING: 'PAYMENT.CAPTURE.PENDING',
  PAYMENT_ORDER_CANCELLED: 'CHECKOUT.ORDER.CANCELLED',
} as const;

interface PayPalWebhookEvent {
  id: string;
  event_type: string;
  create_time: string;
  resource_type: string;
  resource_version: string;
  event_version: string;
  summary: string;
  resource: {
    id: string;
    status: string;
    amount?: {
      currency_code: string;
      value: string;
    };
    custom_id?: string;
    invoice_id?: string;
    final_capture?: boolean;
    seller_protection?: {
      status: string;
      dispute_categories: string[];
    };
    links?: Array<{
      href: string;
      rel: string;
      method: string;
    }>;
    create_time?: string;
    update_time?: string;
  };
  links: Array<{
    href: string;
    rel: string;
    method: string;
    encType?: string;
  }>;
}

/**
 * Verify PayPal webhook signature
 * This ensures the webhook is actually from PayPal and not a malicious actor
 */
async function verifyWebhookSignature(
  body: string,
  headers: Headers,
  webhookId: string
): Promise<boolean> {
  try {
    const authAlgo = headers.get('paypal-auth-algo');
    const transmission = headers.get('paypal-transmission-id');
    const certId = headers.get('paypal-cert-id');
    const transmissionSig = headers.get('paypal-transmission-sig');
    const transmissionTime = headers.get('paypal-transmission-time');

    if (!authAlgo || !transmission || !certId || !transmissionSig || !transmissionTime) {
      console.error('Missing required PayPal webhook headers');
      return false;
    }

    // For sandbox testing, we'll implement basic verification
    // In production, you should implement full certificate verification
    // See: https://developer.paypal.com/docs/api/notifications/webhooks/verify/
    
    console.log('PayPal webhook signature verification:', {
      authAlgo,
      transmission,
      certId,
      transmissionTime,
      webhookId
    });

    // For now, return true for sandbox testing
    // TODO: Implement full signature verification for production
    return true;
  } catch (error) {
    console.error('Error verifying PayPal webhook signature:', error);
    return false;
  }
}

/**
 * Handle payment capture completed event
 */
async function handlePaymentCaptureCompleted(event: PayPalWebhookEvent) {
  try {
    const { resource } = event;
    const paymentId = resource.id;
    const customId = resource.custom_id; // This should be our user ID
    const invoiceId = resource.invoice_id; // This should be our order invoice ID

    console.log('Processing payment capture completed:', {
      paymentId,
      customId,
      invoiceId,
      amount: resource.amount,
      status: resource.status
    });

    // Find the order in our database
    const order = await prisma.order.findFirst({
      where: {
        paymentId: paymentId,
        status: 'device_prep'
      },
      include: {
        user: true
      }
    });

    if (!order) {
      console.error('Order not found for payment ID:', paymentId);
      return { success: false, error: 'Order not found' };
    }

    // Update order status to completed
    const updatedOrder = await prisma.order.update({
      where: { id: order.id },
      data: {
        status: 'shipped', // Move to next status in the workflow
        notes: JSON.stringify({
          ...JSON.parse(order.notes || '{}'),
          paymentCompletedAt: new Date().toISOString(),
          paypalCaptureId: paymentId,
          paypalEventId: event.id,
          finalCapture: resource.final_capture
        })
      }
    });

    // TODO: Implement subscription activation logic here
    // This is where you would:
    // 1. Create/activate the user's subscription
    // 2. Send confirmation email
    // 3. Trigger device provisioning workflow
    // 4. Update user permissions

    console.log('✅ Payment completed successfully for order:', updatedOrder.id);

    return { success: true, orderId: updatedOrder.id };
  } catch (error) {
    console.error('Error handling payment capture completed:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Handle payment capture denied event
 */
async function handlePaymentCaptureDenied(event: PayPalWebhookEvent) {
  try {
    const { resource } = event;
    const paymentId = resource.id;

    console.log('Processing payment capture denied:', {
      paymentId,
      status: resource.status
    });

    // Find and update the order
    const order = await prisma.order.findFirst({
      where: {
        paymentId: paymentId
      }
    });

    if (order) {
      await prisma.order.update({
        where: { id: order.id },
        data: {
          status: 'cancelled',
          notes: JSON.stringify({
            ...JSON.parse(order.notes || '{}'),
            paymentDeniedAt: new Date().toISOString(),
            paypalEventId: event.id,
            denialReason: 'Payment capture denied by PayPal'
          })
        }
      });

      console.log('❌ Payment denied for order:', order.id);
    }

    return { success: true };
  } catch (error) {
    console.error('Error handling payment capture denied:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Handle payment capture pending event
 */
async function handlePaymentCapturePending(event: PayPalWebhookEvent) {
  try {
    const { resource } = event;
    const paymentId = resource.id;

    console.log('Processing payment capture pending:', {
      paymentId,
      status: resource.status
    });

    // Find and update the order
    const order = await prisma.order.findFirst({
      where: {
        paymentId: paymentId
      }
    });

    if (order) {
      await prisma.order.update({
        where: { id: order.id },
        data: {
          notes: JSON.stringify({
            ...JSON.parse(order.notes || '{}'),
            paymentPendingAt: new Date().toISOString(),
            paypalEventId: event.id,
            pendingReason: 'Payment capture pending review'
          })
        }
      });

      console.log('⏳ Payment pending for order:', order.id);
    }

    return { success: true };
  } catch (error) {
    console.error('Error handling payment capture pending:', error);
    return { success: false, error: error.message };
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the raw body for signature verification
    const body = await request.text();
    const headersList = headers();

    console.log('PayPal webhook received:', {
      headers: Object.fromEntries(headersList.entries()),
      bodyLength: body.length
    });

    // Parse the webhook event
    const event: PayPalWebhookEvent = JSON.parse(body);

    console.log('PayPal webhook event:', {
      id: event.id,
      event_type: event.event_type,
      resource_type: event.resource_type,
      summary: event.summary
    });

    // Verify webhook signature (important for security)
    const webhookId = process.env.PAYPAL_WEBHOOK_ID;
    if (!webhookId) {
      console.error('PAYPAL_WEBHOOK_ID environment variable not set');
      return NextResponse.json(
        { error: 'Webhook configuration error' },
        { status: 500 }
      );
    }

    const isValidSignature = await verifyWebhookSignature(body, headersList, webhookId);
    if (!isValidSignature) {
      console.error('Invalid PayPal webhook signature');
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      );
    }

    // Handle different event types
    let result;
    switch (event.event_type) {
      case WEBHOOK_EVENTS.PAYMENT_CAPTURE_COMPLETED:
        result = await handlePaymentCaptureCompleted(event);
        break;

      case WEBHOOK_EVENTS.PAYMENT_CAPTURE_DENIED:
        result = await handlePaymentCaptureDenied(event);
        break;

      case WEBHOOK_EVENTS.PAYMENT_CAPTURE_PENDING:
        result = await handlePaymentCapturePending(event);
        break;

      case WEBHOOK_EVENTS.PAYMENT_ORDER_CANCELLED:
        console.log('Payment order cancelled:', event.resource.id);
        result = { success: true };
        break;

      default:
        console.log('Unhandled PayPal webhook event type:', event.event_type);
        result = { success: true, message: 'Event type not handled' };
    }

    if (result.success) {
      return NextResponse.json({ received: true, ...result });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('PayPal webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}
