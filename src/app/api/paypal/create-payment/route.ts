import { NextRequest, NextResponse } from "next/server";
import { getSession } from "next-auth/react";
import { auth } from "@/lib/auth";
import { getPaymentService } from "@/lib/payment/payment-factory";
import { prisma } from "@/lib/prisma";
import { SPEAR_SUBSCRIPTION_PLANS } from "@/lib/payment/types";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { planType, couponCode, billingInfo, amount } = body;

    console.log('PayPal API - Received request body:', {
      planType,
      couponCode,
      amount,
      billingInfo: billingInfo ? 'present' : 'missing'
    });

    // Validate plan type
    const plan = SPEAR_SUBSCRIPTION_PLANS.find(p => p.id === planType);
    if (!plan) {
      return NextResponse.json(
        { error: "Invalid plan type" },
        { status: 400 }
      );
    }

    // Validate billing info
    if (!billingInfo || !billingInfo.fullName || !billingInfo.email) {
      return NextResponse.json(
        { error: "Billing information is required" },
        { status: 400 }
      );
    }

    // Get or create user in database
    let user;
    try {
      user = await prisma.user.findUnique({
        where: { email: session.user.email! }
      });

      if (!user) {
        user = await prisma.user.create({
          data: {
            email: session.user.email!,
            name: session.user.name || billingInfo.fullName,
          }
        });
      }
    } catch (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.json(
        { error: "Database error" },
        { status: 500 }
      );
    }

    // Get PayPal payment service
    const paymentService = getPaymentService('paypal');

    // Create payment request
    const finalAmount = amount ? { amount: amount, currency: 'USD' } : plan.amount;
    console.log('PayPal API - Plan amount:', plan.amount);
    console.log('PayPal API - Frontend amount:', amount);
    console.log('PayPal API - Final amount being sent to PayPal service:', finalAmount);

    const paymentRequest = {
      customer: {
        id: user.id,
        email: user.email,
        name: user.name || billingInfo.fullName
      },
      amount: finalAmount, // Use frontend amount if provided
      planId: plan.id,
      couponCode: couponCode || undefined,
      billingAddress: {
        fullName: billingInfo.fullName,
        email: billingInfo.email,
        phone: billingInfo.phone || '',
        address: billingInfo.address || '',
        city: billingInfo.city || '',
        state: billingInfo.state || '',
        zipCode: billingInfo.zipCode || '',
        country: 'US'
      },
      metadata: {
        userId: user.id,
        planType: plan.id,
        source: 'spear-app'
      }
    };

    // Process payment with PayPal
    const paymentResult = await paymentService.processPayment(paymentRequest);

    if (paymentResult.success) {
      // Create order record in database
      try {
        const order = await prisma.order.create({
          data: {
            userId: user.id,
            amount: paymentResult.amount.amount,
            status: 'device_prep',
            paymentId: paymentResult.paymentId,
            subscriptionPlan: plan.id,
            notes: JSON.stringify({
              ...paymentResult.metadata,
              currency: paymentResult.amount.currency,
              paymentProvider: 'paypal',
              paymentStatus: paymentResult.status,
              billingInfo: billingInfo,
              originalAmount: plan.amount.amount,
              discountApplied: couponCode ? plan.amount.amount - paymentResult.amount.amount : 0,
              couponCode: couponCode || null
            })
          }
        });

        console.log(`✅ Created order ${order.id} for user ${user.email}`);

        return NextResponse.json({
          success: true,
          payment: {
            id: paymentResult.paymentId,
            status: paymentResult.status,
            amount: paymentResult.amount,
            approvalUrl: paymentResult.metadata?.approvalUrl,
            orderId: order.id
          },
          order: {
            id: order.id,
            amount: order.amount,
            currency: order.currency,
            planType: order.planType
          }
        });

      } catch (dbError) {
        console.error('Error creating order:', dbError);
        return NextResponse.json(
          { error: "Failed to create order record" },
          { status: 500 }
        );
      }

    } else {
      console.error('PayPal payment failed:', paymentResult.error);
      return NextResponse.json(
        { 
          success: false,
          error: paymentResult.error || "Payment processing failed" 
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error processing PayPal payment:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve payment status
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const paymentId = searchParams.get('paymentId');

    if (!paymentId) {
      return NextResponse.json(
        { error: "Payment ID is required" },
        { status: 400 }
      );
    }

    // Get PayPal payment service
    const paymentService = getPaymentService('paypal');

    // Get payment status
    const paymentResult = await paymentService.getPaymentStatus(paymentId);

    return NextResponse.json({
      success: paymentResult.success,
      payment: {
        id: paymentResult.paymentId,
        status: paymentResult.status,
        amount: paymentResult.amount,
        metadata: paymentResult.metadata
      },
      error: paymentResult.error
    });

  } catch (error) {
    console.error('Error getting payment status:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
