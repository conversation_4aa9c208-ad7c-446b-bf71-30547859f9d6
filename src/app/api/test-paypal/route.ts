import { NextRequest, NextResponse } from "next/server";
import { getPaymentFactory } from "@/lib/payment/payment-factory";
import { validatePayPalConfig, getPayPalConfigForLogging } from "@/lib/payment/paypal-config";

export async function GET(request: NextRequest) {
  try {
    // Check PayPal configuration
    const configValidation = validatePayPalConfig();
    const configInfo = getPayPalConfigForLogging();
    
    // Test payment factory
    const factory = getPaymentFactory();
    const healthCheck = await factory.getServiceHealth('paypal');
    
    return NextResponse.json({
      message: "PayPal integration test endpoint",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      paypalConfiguration: {
        ...configInfo,
        validation: configValidation
      },
      paymentFactory: {
        currentProvider: factory.getCurrentProvider(),
        availableProviders: factory.getAvailableProviders(),
        isPayPalSupported: factory.isProviderSupported('paypal')
      },
      serviceHealth: healthCheck,
      testResults: {
        configurationValid: configValidation.isValid,
        serviceHealthy: healthCheck.isHealthy,
        readyForPayments: configValidation.isValid && healthCheck.isHealthy
      }
    });
    
  } catch (error) {
    console.error('PayPal test error:', error);
    return NextResponse.json({
      error: "PayPal test failed",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testType = 'basic' } = body;
    
    const factory = getPaymentFactory();
    
    switch (testType) {
      case 'basic':
        // Test basic PayPal service initialization
        const paymentService = factory.getPaymentService('paypal');
        
        return NextResponse.json({
          message: "Basic PayPal service test successful",
          serviceName: paymentService.name,
          supportedFeatures: paymentService.supportedFeatures,
          timestamp: new Date().toISOString()
        });
        
      case 'coupon':
        // Test coupon validation
        const service = factory.getPaymentService('paypal');
        const validCoupon = await service.validateCoupon('SPEARMINT', 'single-user');
        const invalidCoupon = await service.validateCoupon('INVALID', 'single-user');
        
        return NextResponse.json({
          message: "Coupon validation test",
          tests: {
            validCoupon: {
              code: 'SPEARMINT',
              isValid: validCoupon.isValid,
              discountValue: validCoupon.discountValue,
              discountType: validCoupon.discountType
            },
            invalidCoupon: {
              code: 'INVALID',
              isValid: invalidCoupon.isValid
            }
          },
          timestamp: new Date().toISOString()
        });
        
      case 'pricing':
        // Test pricing calculations
        const pricingService = factory.getPaymentService('paypal');
        const singleUserAmount = { amount: 19900, currency: 'USD' };
        const twoUserAmount = { amount: 29800, currency: 'USD' };
        const spearmintCoupon = await pricingService.validateCoupon('SPEARMINT', 'single-user');
        
        const singleUserWithCoupon = pricingService.applyCoupon(singleUserAmount, spearmintCoupon);
        const twoUserWithCoupon = pricingService.applyCoupon(twoUserAmount, spearmintCoupon);
        
        return NextResponse.json({
          message: "Pricing calculation test",
          pricing: {
            singleUser: {
              original: pricingService.formatAmount(singleUserAmount.amount, singleUserAmount.currency),
              withCoupon: pricingService.formatAmount(singleUserWithCoupon.amount, singleUserWithCoupon.currency),
              discount: pricingService.formatAmount(singleUserAmount.amount - singleUserWithCoupon.amount, singleUserAmount.currency)
            },
            twoUser: {
              original: pricingService.formatAmount(twoUserAmount.amount, twoUserAmount.currency),
              withCoupon: pricingService.formatAmount(twoUserWithCoupon.amount, twoUserWithCoupon.currency),
              discount: pricingService.formatAmount(twoUserAmount.amount - twoUserWithCoupon.amount, twoUserAmount.currency)
            }
          },
          timestamp: new Date().toISOString()
        });
        
      default:
        return NextResponse.json({
          error: "Unknown test type",
          availableTests: ['basic', 'coupon', 'pricing']
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('PayPal POST test error:', error);
    return NextResponse.json({
      error: "PayPal test failed",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
