import { NextRequest, NextResponse } from 'next/server';
import { webScrapingAgent } from '@/lib/mastra';

// Content for articles
const gettingStartedContent = `# Getting Started with SPEAR

Welcome to SPEAR Global, your comprehensive remote device management platform. This guide will walk you through the essential steps to get started with SPEAR and begin managing your devices remotely.

## What is SPEAR?

SPEAR Global is a powerful platform designed for secure remote device management. Whether you're managing mobile devices, desktop computers, or IoT devices, SPEAR provides the tools you need to maintain control and ensure security across your device fleet.

## Initial Setup

### 1. Account Creation
- Visit the SPEAR Global website and create your account
- Verify your email address
- Choose your subscription plan based on your needs

### 2. Dashboard Overview
Once logged in, you'll see your main dashboard with:
- Device overview and status
- Quick access to remote connections
- Subscription and billing information
- Support resources

### 3. First Device Registration
To add your first device:
1. Navigate to the "Devices" section
2. Click "Add Device"
3. Follow the device-specific setup instructions
4. Install the SPEAR client on your target device
5. Complete the pairing process

## Key Features

### Remote Access
- Secure remote desktop connections
- File transfer capabilities
- Real-time device monitoring
- Session recording and playback

### Device Management
- Device status monitoring
- Software deployment
- Security policy enforcement
- Automated maintenance tasks

### Security
- End-to-end encryption
- Multi-factor authentication
- Access logging and auditing
- Compliance reporting

## Getting Help

If you need assistance:
- Check our comprehensive FAQ section
- Browse additional guides in the Knowledge Base
- Contact our support <NAME_EMAIL>
- Access live chat during business hours (9am - 8pm EST)

## Next Steps

1. Complete your device setup
2. Explore the security settings
3. Set up automated monitoring
4. Review our Device Provisioning Guide for advanced configuration

Welcome to SPEAR Global - your journey to efficient remote device management starts here!`;

const deviceProvisioningContent = `# Device Provisioning Guide

This comprehensive guide covers everything you need to know about provisioning devices in SPEAR Global, from initial setup to advanced configuration options.

## Overview

Device provisioning is the process of preparing and configuring devices for remote management through SPEAR Global. This includes installing necessary software, configuring security settings, and establishing secure connections.

## Supported Device Types

### Desktop Devices
- Windows 10/11 (Professional and Enterprise editions)
- macOS 10.15 and later
- Linux distributions (Ubuntu, CentOS, RHEL)

### Mobile Devices
- Android 8.0 and later
- iOS 13.0 and later (limited functionality)

## Desktop Device Provisioning

### Windows Devices
1. Download the SPEAR Client from your dashboard
2. Run the installer as Administrator
3. Enter your device registration code
4. Complete the setup wizard
5. Restart the device when prompted

### macOS Devices
1. Download the .dmg installer
2. Grant necessary permissions in System Preferences
3. Complete the guided setup
4. Verify connection in SPEAR dashboard

### Linux Devices
1. Download and install the appropriate package
2. Run the configuration script
3. Enter registration credentials
4. Set up firewall rules if needed

## Mobile Device Provisioning

### Android Devices
1. Install SPEAR Mobile from Google Play Store
2. Scan QR code from your dashboard
3. Grant necessary permissions
4. Complete device enrollment

## Best Practices

1. Plan your deployment strategy
2. Test with pilot devices first
3. Use strong authentication
4. Regularly update software
5. Monitor device health

For additional help with device provisioning, contact <NAME_EMAIL>`;

const securityBestPracticesContent = `# Security Best Practices for SPEAR Global

Security is paramount when managing remote devices. This guide outlines essential security practices to protect your devices, data, and network infrastructure while using SPEAR Global.

## Core Security Principles

### Defense in Depth
Implement multiple layers of security controls:
- Network security (firewalls, VPNs)
- Device security (encryption, access controls)
- Application security (authentication, authorization)
- Data security (encryption at rest and in transit)

### Principle of Least Privilege
- Grant minimum necessary access rights
- Regularly review and update permissions
- Use role-based access control (RBAC)
- Implement time-limited access when possible

## Authentication and Access Control

### Multi-Factor Authentication (MFA)
Enable MFA for all accounts using:
- Authenticator apps (Google Authenticator, Authy)
- SMS-based verification as backup
- Hardware security keys for high-privilege accounts
- Biometric authentication where available

### Strong Password Policies
Implement robust password requirements:
- Minimum 12 characters
- Combination of uppercase, lowercase, numbers, symbols
- No dictionary words or personal information
- Regular password rotation (90-180 days)

## Network Security

### Secure Communications
All SPEAR communications use:
- TLS 1.3 encryption for data in transit
- Certificate pinning for additional security
- Perfect Forward Secrecy (PFS)
- Regular certificate rotation

### Firewall Configuration
Configure appropriate firewall rules for SPEAR traffic and block unnecessary ports by default.

## Device Security

### Endpoint Protection
- Install reputable antivirus/anti-malware
- Enable real-time protection
- Regular security scans
- Automatic threat detection and response

### Device Encryption
- Full disk encryption (BitLocker, FileVault, LUKS)
- Encrypted storage for sensitive data
- Secure key management
- Regular encryption key rotation

### Software Updates
- Enable automatic OS updates
- Regular application updates
- Security patch management
- Vulnerability scanning

## Data Protection

### Data Classification
Classify data by sensitivity:
- Public: No restrictions
- Internal: Company confidential
- Restricted: Limited access required
- Confidential: Highest protection level

### Data Encryption
- Encryption at rest (AES-256)
- Encryption in transit (TLS 1.3)
- End-to-end encryption for sensitive data
- Secure key management practices

## Incident Response

### Preparation
- Develop comprehensive incident response plan
- Define incident types and severity levels
- Establish response team roles
- Create communication procedures

### Detection and Analysis
- 24/7 security monitoring
- Automated threat detection
- Log analysis and correlation
- Threat intelligence integration

## Compliance and Auditing

### Regulatory Compliance
Common frameworks include:
- SOC 2 Type II
- ISO 27001
- GDPR (for EU data)
- HIPAA (for healthcare)
- PCI DSS (for payment data)

### Audit Logging
Comprehensive logging includes:
- User authentication events
- Device access attempts
- Configuration changes
- Data access and transfers
- Administrative actions

## Emergency Procedures

### Device Compromise
Immediate actions:
1. Disconnect device from network
2. Change all associated passwords
3. Scan for malware
4. Review access logs
5. Report to security team

### Data Breach Response
1. Contain the breach
2. Assess the scope
3. Notify stakeholders
4. Preserve evidence
5. Implement remediation
6. Review and improve

## Contact Information

For security-related questions or incidents:
- Security Team: <EMAIL>
- Emergency Hotline: Available 24/7 through support portal
- General Support: <EMAIL>

Remember: Security is everyone's responsibility. Stay vigilant, follow best practices, and report any suspicious activity immediately.`;

// Mock database for knowledge base articles
let knowledgeBaseArticles = [
  {
    id: '1',
    title: 'Getting Started with SPEAR',
    slug: 'getting-started-with-spear',
    content: gettingStartedContent,
    keywords: ['SPEAR', 'getting started', 'setup', 'onboarding', 'remote access', 'device management'],
    category: 'Guides',
    createdAt: '2023-01-15T00:00:00.000Z',
    updatedAt: '2024-12-08T00:00:00.000Z',
  },
  {
    id: '2',
    title: 'Device Provisioning Guide',
    slug: 'device-provisioning-guide',
    content: deviceProvisioningContent,
    keywords: ['device provisioning', 'setup', 'configuration', 'installation', 'mobile', 'desktop'],
    category: 'Guides',
    createdAt: '2023-02-10T00:00:00.000Z',
    updatedAt: '2024-12-08T00:00:00.000Z',
  },
  {
    id: '3',
    title: 'Security Best Practices',
    slug: 'security-best-practices',
    content: securityBestPracticesContent,
    keywords: ['security', 'best practices', 'encryption', 'authentication', 'compliance', 'monitoring'],
    category: 'Security',
    createdAt: '2023-03-15T00:00:00.000Z',
    updatedAt: '2024-12-08T00:00:00.000Z',
  },
];

// GET handler for retrieving knowledge base articles
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');
  const slug = searchParams.get('slug');
  const category = searchParams.get('category');
  const query = searchParams.get('query');

  // Return a specific article by ID
  if (id) {
    const article = knowledgeBaseArticles.find(article => article.id === id);
    if (!article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 });
    }
    return NextResponse.json(article);
  }

  // Return a specific article by slug
  if (slug) {
    const article = knowledgeBaseArticles.find(article => article.slug === slug);
    if (!article) {
      return NextResponse.json({ error: 'Article not found' }, { status: 404 });
    }
    return NextResponse.json(article);
  }

  // Filter by category
  if (category) {
    const filteredArticles = knowledgeBaseArticles.filter(
      article => article.category.toLowerCase() === category.toLowerCase()
    );
    return NextResponse.json(filteredArticles);
  }

  // Search by query
  if (query) {
    const filteredArticles = knowledgeBaseArticles.filter(
      article =>
        article.title.toLowerCase().includes(query.toLowerCase()) ||
        article.content.toLowerCase().includes(query.toLowerCase()) ||
        article.keywords.some(keyword => keyword.toLowerCase().includes(query.toLowerCase()))
    );
    return NextResponse.json(filteredArticles);
  }

  // Return all articles
  return NextResponse.json(knowledgeBaseArticles);
}

// POST handler for creating new knowledge base articles
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.title || !body.content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      );
    }

    // Generate slug if not provided
    const slug = body.slug || body.title.toLowerCase().replace(/[^a-z0-9]+/g, '-');

    // Check if slug already exists
    if (knowledgeBaseArticles.some(article => article.slug === slug)) {
      return NextResponse.json(
        { error: 'An article with this slug already exists' },
        { status: 400 }
      );
    }

    // Create new article
    const newArticle = {
      id: (knowledgeBaseArticles.length + 1).toString(),
      title: body.title,
      slug,
      content: body.content,
      keywords: body.keywords || [],
      category: body.category || 'Uncategorized',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Add to mock database
    knowledgeBaseArticles.push(newArticle);

    return NextResponse.json(newArticle, { status: 201 });
  } catch (error) {
    console.error('Error creating knowledge base article:', error);
    return NextResponse.json(
      { error: 'Failed to create article' },
      { status: 500 }
    );
  }
}

// PUT handler for updating knowledge base articles
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.id) {
      return NextResponse.json(
        { error: 'Article ID is required' },
        { status: 400 }
      );
    }

    // Find the article
    const articleIndex = knowledgeBaseArticles.findIndex(article => article.id === body.id);
    if (articleIndex === -1) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      );
    }

    // Update the article
    const updatedArticle = {
      ...knowledgeBaseArticles[articleIndex],
      ...body,
      updatedAt: new Date().toISOString(),
    };

    knowledgeBaseArticles[articleIndex] = updatedArticle;

    return NextResponse.json(updatedArticle);
  } catch (error) {
    console.error('Error updating knowledge base article:', error);
    return NextResponse.json(
      { error: 'Failed to update article' },
      { status: 500 }
    );
  }
}

// DELETE handler for removing knowledge base articles
export async function DELETE(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json(
      { error: 'Article ID is required' },
      { status: 400 }
    );
  }

  // Find the article
  const articleIndex = knowledgeBaseArticles.findIndex(article => article.id === id);
  if (articleIndex === -1) {
    return NextResponse.json(
      { error: 'Article not found' },
      { status: 404 }
    );
  }

  // Remove the article
  const deletedArticle = knowledgeBaseArticles[articleIndex];
  knowledgeBaseArticles = knowledgeBaseArticles.filter(article => article.id !== id);

  return NextResponse.json(deletedArticle);
}
