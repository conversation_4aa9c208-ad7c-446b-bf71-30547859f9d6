import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

/**
 * API route to update a device
 * This is a temporary endpoint for testing
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || !session.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { rustDeskId, password, status } = body;

    if (!rustDeskId) {
      return NextResponse.json({ success: false, error: 'RustDesk ID is required' }, { status: 400 });
    }

    // Find the device
    const device = await prisma.device.findFirst({
      where: { rustDeskId }
    });

    if (!device) {
      return NextResponse.json({ success: false, error: 'Device not found' }, { status: 404 });
    }

    // Update the device
    const updatedDevice = await prisma.device.update({
      where: { id: device.id },
      data: {
        password: password || device.password,
        status: status || device.status
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Device updated successfully',
      device: updatedDevice
    });
  } catch (error) {
    console.error('Error updating device:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

/**
 * Handle GET requests (for testing)
 */
export async function GET() {
  return NextResponse.json({
    message: 'Update device API is working. Use POST to update a device.',
  });
}
