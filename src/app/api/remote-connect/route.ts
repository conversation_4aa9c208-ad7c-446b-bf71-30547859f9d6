import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

// Configuration
const RUSTDESK_URL = 'https://rustdesk.com/web/';
// More comprehensive selectors to find the device element
const DEVICE_ICON_SELECTOR = [
  '.device-icon',
  '.device-card',
  '[class*="device-card"]',
  '[class*="device-icon"]',
  // Additional selectors that might match the device
  '.device',
  '[class*="device"]',
  // Generic selectors that might contain the device ID
  'div[role="button"]',
  'button',
  'a[role="button"]',
  // Last resort - any clickable element
  '[role="button"]',
  'a',
  'button',
  'div[tabindex="0"]'
].join(', ');
const MAX_WAIT_TIME = 30000; // 30 seconds

/**
 * API route to handle remote connection automation
 * This is a fallback for when client-side automation fails
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { deviceId, password } = body;

    if (!deviceId) {
      return NextResponse.json({ success: false, error: 'Device ID is required' }, { status: 400 });
    }

    // Format the device ID
    const formattedDeviceId = deviceId.replace(/\s+/g, '');

    // Check if the device belongs to the user (for clients)
    if (session.user.role === 'CLIENT') {
      const device = await prisma.device.findFirst({
        where: {
          rustDeskId: formattedDeviceId,
          userId: session.user.id
        }
      });

      if (!device) {
        return NextResponse.json(
          { success: false, error: "You don't have permission to access this device" },
          { status: 403 }
        );
      }
    }

    // Build the connection URL
    let connectionUrl = `${RUSTDESK_URL}?id=${formattedDeviceId}`;
    if (password) {
      connectionUrl += `&password=${encodeURIComponent(password)}`;
    }

    // Log connection attempt with user info
    console.log(`User ${session.user.email} (${session.user.role}) connecting to device: ${formattedDeviceId}`);
    console.log(`Launching Puppeteer to automate connection to: ${connectionUrl}`);

    // Launch a headless browser with more options for stability
    const browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--disable-gpu',
        '--window-size=1280,800'
      ],
      ignoreHTTPSErrors: true,
      timeout: MAX_WAIT_TIME
    });

    try {
      // Create a new page
      const page = await browser.newPage();

      // Set viewport
      await page.setViewport({ width: 1280, height: 800 });

      // Enable console logging from the browser
      page.on('console', msg => console.log('Browser console:', msg.text()));

      // Log all requests for debugging
      await page.setRequestInterception(true);
      page.on('request', request => {
        if (request.resourceType() === 'document' || request.resourceType() === 'xhr') {
          console.log(`Request: ${request.method()} ${request.url()}`);
        }
        request.continue();
      });

      // Navigate to RustDesk web client
      console.log('Navigating to RustDesk web client...');
      await page.goto(connectionUrl, {
        waitUntil: 'networkidle2',
        timeout: MAX_WAIT_TIME
      });

      // Take a screenshot immediately after navigation
      console.log('Taking initial screenshot...');
      const initialScreenshot = await page.screenshot({ encoding: 'base64' });

      // Inject helper script to find elements containing the device ID
      console.log('Injecting helper script to find device elements...');
      const deviceIdFormatted = formattedDeviceId.replace(/\s+/g, '');
      const findDeviceResult = await page.evaluate((deviceId) => {
        // Helper function to find elements containing text
        function findElementsWithText(text) {
          const elements = [];
          const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_ELEMENT,
            {
              acceptNode: function(node) {
                if (node.textContent && node.textContent.includes(text)) {
                  return NodeFilter.FILTER_ACCEPT;
                }
                return NodeFilter.FILTER_SKIP;
              }
            }
          );

          while (walker.nextNode()) {
            elements.push(walker.currentNode);
          }

          return elements;
        }

        // Find elements containing the device ID
        const elementsWithDeviceId = findElementsWithText(deviceId);

        // Get clickable ancestors
        const clickableElements = elementsWithDeviceId.map(el => {
          // Check if the element itself is clickable
          if (el.tagName === 'BUTTON' ||
              el.tagName === 'A' ||
              el.getAttribute('role') === 'button' ||
              el.getAttribute('tabindex') === '0') {
            return {
              path: el.tagName + (el.id ? '#' + el.id : '') + (el.className ? '.' + el.className.replace(/\s+/g, '.') : ''),
              text: el.textContent.trim()
            };
          }

          // Check parents up to 3 levels
          let parent = el.parentElement;
          let level = 0;
          while (parent && level < 3) {
            if (parent.tagName === 'BUTTON' ||
                parent.tagName === 'A' ||
                parent.getAttribute('role') === 'button' ||
                parent.getAttribute('tabindex') === '0') {
              return {
                path: parent.tagName + (parent.id ? '#' + parent.id : '') + (parent.className ? '.' + parent.className.replace(/\s+/g, '.') : ''),
                text: parent.textContent.trim()
              };
            }
            parent = parent.parentElement;
            level++;
          }

          // Return the element itself if no clickable parent found
          return {
            path: el.tagName + (el.id ? '#' + el.id : '') + (el.className ? '.' + el.className.replace(/\s+/g, '.') : ''),
            text: el.textContent.trim()
          };
        });

        return {
          found: elementsWithDeviceId.length > 0,
          count: elementsWithDeviceId.length,
          clickableElements: clickableElements
        };
      }, deviceIdFormatted);

      console.log('Find device result:', findDeviceResult);

      // Try to find and click on the device element
      let deviceClicked = false;

      // First try: Use the device ID to find the element
      if (findDeviceResult.found) {
        console.log(`Found ${findDeviceResult.count} elements containing the device ID`);

        // Try clicking each element that contains the device ID
        for (let i = 0; i < findDeviceResult.clickableElements.length; i++) {
          const elementInfo = findDeviceResult.clickableElements[i];
          console.log(`Trying to click element: ${elementInfo.path} with text: ${elementInfo.text}`);

          try {
            // Try to find and click elements containing the device ID
            await page.evaluate((deviceId) => {
              // Helper function to find elements containing text
              function findElementsWithText(text) {
                const elements = [];
                const walker = document.createTreeWalker(
                  document.body,
                  NodeFilter.SHOW_ELEMENT,
                  {
                    acceptNode: function(node) {
                      if (node.textContent && node.textContent.includes(text)) {
                        return NodeFilter.FILTER_ACCEPT;
                      }
                      return NodeFilter.FILTER_SKIP;
                    }
                  }
                );

                while (walker.nextNode()) {
                  elements.push(walker.currentNode);
                }

                return elements;
              }

              const elements = findElementsWithText(deviceId);
              if (elements.length > 0) {
                // Click the element or its parent if it's more likely to be clickable
                const el = elements[0];
                if (el.tagName === 'BUTTON' || el.tagName === 'A' || el.getAttribute('role') === 'button') {
                  el.click();
                } else if (el.parentElement) {
                  el.parentElement.click();
                } else {
                  el.click();
                }
                return true;
              }
              return false;
            }, deviceIdFormatted);

            // Wait a bit to see if the click worked
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Check if we're now on a different URL or if the UI has changed
            const currentUrl = page.url();
            if (currentUrl !== connectionUrl) {
              console.log(`URL changed to ${currentUrl}, click successful!`);
              deviceClicked = true;
              break;
            }

            // Take a screenshot to see what happened
            const afterClickScreenshot = await page.screenshot({ encoding: 'base64' });

            // Check if the UI has changed significantly
            const hasRemoteScreen = await page.evaluate(() => {
              return document.querySelector('.remote-screen, .remote-view, [class*="remote-screen"], [class*="remote-view"]') !== null;
            });

            if (hasRemoteScreen) {
              console.log('Remote screen detected, click successful!');
              deviceClicked = true;
              break;
            }
          } catch (error) {
            console.log(`Error clicking element ${i}:`, error);
          }
        }
      }

      // Second try: Use the standard selectors if the first approach didn't work
      if (!deviceClicked) {
        console.log('First approach failed, trying standard selectors...');

        try {
          // Wait for any device icon to appear
          console.log('Waiting for device icon to appear...');
          await page.waitForSelector(DEVICE_ICON_SELECTOR, { timeout: MAX_WAIT_TIME / 2 });

          // Take a screenshot before clicking (for debugging)
          const beforeScreenshot = await page.screenshot({ encoding: 'base64' });

          // Find all device elements
          const deviceElements = await page.$$(DEVICE_ICON_SELECTOR);
          console.log(`Found ${deviceElements.length} device elements with standard selectors`);

          if (deviceElements.length > 0) {
            // Try multiple approaches to click the device

            // Approach 1: Try to click using page.click with a specific selector
            try {
              console.log('Approach 1: Trying to click using page.click...');

              // Get the bounding box of the element
              const boundingBox = await deviceElements[0].boundingBox();
              if (boundingBox) {
                // Click in the center of the element
                await page.mouse.click(
                  boundingBox.x + boundingBox.width / 2,
                  boundingBox.y + boundingBox.height / 2
                );
                console.log('Clicked using mouse coordinates');
                deviceClicked = true;
              } else {
                console.log('Could not get bounding box of element');
              }
            } catch (error) {
              console.log('Error with approach 1:', error);
            }

            // Approach 2: Try clicking using evaluate if approach 1 failed
            if (!deviceClicked) {
              try {
                console.log('Approach 2: Trying to click using page.evaluate...');

                // Click the device using JavaScript directly
                await page.evaluate(() => {
                  // Try to find the device element by various means
                  const possibleSelectors = [
                    '.device-icon',
                    '.device-card',
                    '[class*="device-card"]',
                    '[class*="device-icon"]',
                    '.device',
                    '[class*="device"]',
                    'div[role="button"]',
                    'button',
                    'a[role="button"]',
                    '[role="button"]',
                    'a',
                    'button',
                    'div[tabindex="0"]'
                  ];

                  // Try each selector
                  for (const selector of possibleSelectors) {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                      // Click the first element
                      elements[0].click();
                      console.log(`Clicked element with selector: ${selector}`);
                      return true;
                    }
                  }

                  // If no selector worked, try clicking the first image
                  const images = document.querySelectorAll('img');
                  if (images.length > 0) {
                    images[0].click();
                    console.log('Clicked first image');
                    return true;
                  }

                  return false;
                });

                console.log('Clicked using evaluate');
                deviceClicked = true;
              } catch (error) {
                console.log('Error with approach 2:', error);
              }
            }

            // Approach 3: Try clicking the Android icon specifically
            if (!deviceClicked) {
              try {
                console.log('Approach 3: Trying to click Android icon...');

                // Look for an image that might be the Android icon
                const androidIconSelector = 'img[src*="android"], img[alt*="android"], img[src*="phone"], img[alt*="phone"]';
                const androidIcon = await page.$(androidIconSelector);

                if (androidIcon) {
                  await androidIcon.click();
                  console.log('Clicked Android icon');
                  deviceClicked = true;
                } else {
                  console.log('Could not find Android icon');
                }
              } catch (error) {
                console.log('Error with approach 3:', error);
              }
            }

            // Approach 4: Try clicking by simulating keyboard navigation
            if (!deviceClicked) {
              try {
                console.log('Approach 4: Trying keyboard navigation...');

                // Press Tab to focus on elements and then Enter to click
                for (let i = 0; i < 10; i++) {
                  await page.keyboard.press('Tab');
                }
                await page.keyboard.press('Enter');

                console.log('Used keyboard navigation');
                deviceClicked = true;
              } catch (error) {
                console.log('Error with approach 4:', error);
              }
            }

            // Approach 5: Try clicking at specific coordinates where the Android icon is likely to be
            if (!deviceClicked) {
              try {
                console.log('Approach 5: Trying to click at specific coordinates...');

                // Based on the screenshot, the Android icon is in the left panel
                // Try clicking at different positions in the left panel
                const clickPositions = [
                  { x: 65, y: 150 },  // Center of the Android icon from screenshot
                  { x: 65, y: 190 },  // Below the icon, might be the text
                  { x: 40, y: 150 },  // Left side of icon
                  { x: 90, y: 150 },  // Right side of icon
                  { x: 65, y: 120 },  // Above the icon
                  { x: 65, y: 180 }   // Below the icon
                ];

                for (const position of clickPositions) {
                  console.log(`Clicking at position: x=${position.x}, y=${position.y}`);
                  await page.mouse.click(position.x, position.y);

                  // Wait a bit to see if the click worked
                  await new Promise(resolve => setTimeout(resolve, 1000));

                  // Check if we're now on a different URL or if the UI has changed
                  const currentUrl = page.url();
                  if (currentUrl !== connectionUrl) {
                    console.log(`URL changed to ${currentUrl}, click successful!`);
                    deviceClicked = true;
                    break;
                  }

                  // Check if the UI has changed significantly
                  const hasRemoteScreen = await page.evaluate(() => {
                    return document.querySelector('.remote-screen, .remote-view, [class*="remote-screen"], [class*="remote-view"]') !== null;
                  });

                  if (hasRemoteScreen) {
                    console.log('Remote screen detected, click successful!');
                    deviceClicked = true;
                    break;
                  }
                }
              } catch (error) {
                console.log('Error with approach 5:', error);
              }
            }
          } else {
            console.log('No device elements found with standard selectors');
          }
        } catch (error) {
          console.log('Error with standard selectors approach:', error);
        }
      }

      // If we couldn't click the device, return an error
      if (!deviceClicked) {
        console.log('Could not click on device element');
        const finalScreenshot = await page.screenshot({ encoding: 'base64' });
        await browser.close();

        return NextResponse.json({
          success: false,
          error: 'Could not find or click on the device element',
          debugScreenshot: `data:image/png;base64,${finalScreenshot}`,
          initialScreenshot: `data:image/png;base64,${initialScreenshot}`
        }, { status: 500 });
      }

      // Wait for the remote screen to appear or just continue after a timeout
      console.log('Waiting for remote screen to appear...');
      await page.waitForSelector('.remote-screen, .remote-view, [class*="remote-screen"], [class*="remote-view"]', {
        timeout: MAX_WAIT_TIME / 2
      }).catch(e => {
        console.log('Could not find remote screen selector, continuing anyway');
      });

      // Wait a bit for the connection to stabilize
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Get the current URL (which should now be the session URL)
      const sessionUrl = page.url();
      console.log(`Session URL: ${sessionUrl}`);

      // Take a screenshot after clicking
      const afterScreenshot = await page.screenshot({ encoding: 'base64' });

      // Close the browser
      await browser.close();

      // Return the session URL to the client
      return NextResponse.json({
        success: true,
        message: 'Connection established',
        connectionUrl: sessionUrl,
        screenshot: `data:image/png;base64,${afterScreenshot}`,
      });
    } catch (error) {
      // Close the browser on error
      await browser.close();
      throw error;
    }
  } catch (error) {
    console.error('Error in remote-connect API:', error);

    // Return error response with more details
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorDetails: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

/**
 * Handle GET requests (for testing)
 */
export async function GET() {
  return NextResponse.json({
    message: 'Remote connect API is working. Use POST to connect to a device.',
  });
}
