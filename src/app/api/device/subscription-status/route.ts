import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get device ID from headers
    const deviceId = request.headers.get('X-Device-ID');
    const deviceType = request.headers.get('X-Device-Type');
    
    if (!deviceId) {
      return NextResponse.json(
        { 
          isActive: false, 
          message: 'Device ID required',
          error: 'DEVICE_ID_MISSING'
        },
        { status: 400 }
      );
    }

    // Find device in database
    const device = await prisma.device.findFirst({
      where: {
        OR: [
          { id: deviceId },
          { rustDeskId: deviceId },
          { serialNumber: deviceId }
        ]
      },
      include: {
        user: {
          include: {
            subscriptions: {
              where: {
                status: { in: ['active', 'past_due', 'canceled', 'unpaid'] }
              },
              orderBy: { createdAt: 'desc' },
              take: 1
            }
          }
        }
      }
    });

    if (!device) {
      // Device not found - could be unregistered device
      return NextResponse.json(
        { 
          isActive: false, 
          message: 'Device not registered in SPEAR system',
          error: 'DEVICE_NOT_REGISTERED'
        },
        { status: 404 }
      );
    }

    if (!device.user) {
      // Device exists but not assigned to user
      return NextResponse.json(
        { 
          isActive: false, 
          message: 'Device not assigned to any user',
          error: 'DEVICE_UNASSIGNED'
        },
        { status: 403 }
      );
    }

    const subscription = device.user.subscriptions[0];
    
    if (!subscription) {
      // User has no subscription
      return NextResponse.json(
        { 
          isActive: false, 
          message: 'No subscription found for device owner',
          error: 'NO_SUBSCRIPTION'
        },
        { status: 402 } // Payment Required
      );
    }

    // Check subscription status
    const isActive = ['active', 'past_due'].includes(subscription.status);
    
    if (!isActive) {
      // Subscription is inactive
      return NextResponse.json(
        { 
          isActive: false, 
          message: `Subscription is ${subscription.status}`,
          error: 'SUBSCRIPTION_INACTIVE',
          subscriptionStatus: subscription.status,
          planType: subscription.plan
        },
        { status: 402 } // Payment Required
      );
    }

    // Check if subscription is expired
    if (subscription.currentPeriodEnd && subscription.currentPeriodEnd < new Date()) {
      return NextResponse.json(
        { 
          isActive: false, 
          message: 'Subscription has expired',
          error: 'SUBSCRIPTION_EXPIRED',
          expiresAt: subscription.currentPeriodEnd.toISOString()
        },
        { status: 402 } // Payment Required
      );
    }

    // Update device last seen timestamp
    await prisma.device.update({
      where: { id: device.id },
      data: { 
        updatedAt: new Date(),
        status: 'online' // Mark as online since it's checking in
      }
    });

    // Subscription is active
    return NextResponse.json({
      isActive: true,
      message: 'Subscription is active',
      subscriptionStatus: subscription.status,
      planType: subscription.plan,
      expiresAt: subscription.currentPeriodEnd?.toISOString(),
      deviceInfo: {
        id: device.id,
        name: device.name,
        model: device.model,
        status: 'online'
      },
      userInfo: {
        email: device.user.email,
        name: device.user.name
      }
    });

  } catch (error) {
    console.error('Error checking device subscription status:', error);
    
    return NextResponse.json(
      { 
        isActive: true, // Default to active on error to avoid false positives
        message: 'Error checking subscription status',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}

// POST endpoint for device registration/check-in
export async function POST(request: NextRequest) {
  try {
    const { deviceId, deviceInfo, location } = await request.json();
    
    if (!deviceId) {
      return NextResponse.json(
        { error: 'Device ID required' },
        { status: 400 }
      );
    }

    // Find or create device
    const device = await prisma.device.upsert({
      where: { 
        rustDeskId: deviceId 
      },
      update: {
        updatedAt: new Date(),
        status: 'online',
        // Update device info if provided
        ...(deviceInfo && {
          model: deviceInfo.model,
          osVersion: deviceInfo.osVersion,
          appVersion: deviceInfo.appVersion
        })
      },
      create: {
        rustDeskId: deviceId,
        name: `Device ${deviceId}`,
        model: deviceInfo?.model || 'Unknown',
        deviceType: 'mobile',
        status: 'online',
        password: generateDevicePassword(),
        osVersion: deviceInfo?.osVersion,
        appVersion: deviceInfo?.appVersion
      },
      include: {
        user: {
          include: {
            subscriptions: {
              where: {
                status: { in: ['active', 'past_due'] }
              },
              orderBy: { createdAt: 'desc' },
              take: 1
            }
          }
        }
      }
    });

    // Check subscription status
    const hasActiveSubscription = device.user?.subscriptions.length > 0;
    
    return NextResponse.json({
      success: true,
      deviceId: device.id,
      isActive: hasActiveSubscription,
      message: hasActiveSubscription ? 'Device registered and active' : 'Device registered but no active subscription'
    });

  } catch (error) {
    console.error('Error registering device:', error);
    
    return NextResponse.json(
      { error: 'Failed to register device' },
      { status: 500 }
    );
  }
}

function generateDevicePassword(): string {
  // Generate a secure random password for the device
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}
