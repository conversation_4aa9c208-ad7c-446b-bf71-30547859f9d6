import { NextRequest, NextResponse } from 'next/server';
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import puppeteer from 'puppeteer';

// Configuration
const RUSTDESK_URL = 'https://rustdesk.com/web/';
const MAX_WAIT_TIME = 30000; // 30 seconds

/**
 * API route to handle remote connection for clients
 * This ensures clients can only connect to their assigned devices
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { deviceId, password } = body;

    if (!deviceId) {
      return NextResponse.json({ success: false, error: 'Device ID is required' }, { status: 400 });
    }

    // Format the device ID
    const formattedDeviceId = deviceId.replace(/\s+/g, '');

    // Check if the device belongs to the user (for clients)
    if (session.user.role === 'CLIENT') {
      const device = await prisma.device.findFirst({
        where: {
          rustDeskId: formattedDeviceId,
          userId: session.user.id
        }
      });

      if (!device) {
        return NextResponse.json(
          { success: false, error: "You don't have permission to access this device" },
          { status: 403 }
        );
      }
    }

    // Build the connection URL
    let connectionUrl = `${RUSTDESK_URL}?id=${formattedDeviceId}`;
    if (password) {
      connectionUrl += `&password=${encodeURIComponent(password)}`;
    }

    console.log(`Client connecting to device: ${formattedDeviceId}`);

    // Launch a headless browser
    const browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--disable-gpu',
        '--window-size=1280,800'
      ],
      ignoreHTTPSErrors: true,
      timeout: MAX_WAIT_TIME
    });

    try {
      // Create a new page
      const page = await browser.newPage();
      
      // Set viewport
      await page.setViewport({ width: 1280, height: 800 });
      
      // Navigate to RustDesk web client
      console.log('Navigating to RustDesk web client...');
      await page.goto(connectionUrl, {
        waitUntil: 'networkidle2',
        timeout: MAX_WAIT_TIME
      });

      // Take a screenshot for debugging
      const screenshot = await page.screenshot({ encoding: 'base64' });
      
      // Get the current URL (which should be the session URL)
      const sessionUrl = page.url();
      console.log(`Session URL: ${sessionUrl}`);
      
      // Close the browser
      await browser.close();
      
      // Return the session URL to the client
      return NextResponse.json({
        success: true,
        message: 'Connection ready',
        connectionUrl: sessionUrl,
        screenshot: `data:image/png;base64,${screenshot}`,
      });
    } catch (error) {
      // Close the browser on error
      await browser.close();
      throw error;
    }
  } catch (error) {
    console.error('Error in client remote-connect API:', error);
    
    // Return error response
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

/**
 * Handle GET requests (for testing)
 */
export async function GET() {
  return NextResponse.json({
    message: 'Client remote connect API is working. Use POST to connect to a device.',
  });
}
