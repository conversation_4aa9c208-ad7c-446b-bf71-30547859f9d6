import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get the current user session
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find the user in the database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        devices: true,
        subscriptions: {
          where: {
            status: { in: ['active', 'past_due'] }
          },
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has active subscription
    const hasActiveSubscription = user.subscriptions.length > 0 && 
      ['active', 'past_due'].includes(user.subscriptions[0].status);

    if (!hasActiveSubscription) {
      return NextResponse.json(
        { error: 'No active subscription' },
        { status: 403 }
      );
    }

    // Return only the user's assigned devices
    const clientDevices = user.devices.map(device => ({
      id: device.id,
      name: device.name,
      rustDeskId: device.rustDeskId,
      password: device.password, // Only show to device owner
      model: device.model,
      status: device.status,
      lastSeen: device.updatedAt.toISOString(),
      platform: device.deviceType === 'mobile' ? 'Android' : 'Desktop'
    }));

    return NextResponse.json(clientDevices);

  } catch (error) {
    console.error('Error fetching client devices:', error);
    return NextResponse.json(
      { error: 'Failed to fetch devices' },
      { status: 500 }
    );
  }
}
