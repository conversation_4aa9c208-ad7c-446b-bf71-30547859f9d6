import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get the current user session
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Find the user and their subscription
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        subscriptions: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const subscription = user.subscriptions[0];
    
    if (!subscription) {
      return NextResponse.json({
        isActive: false,
        planType: 'No Plan',
        message: 'No subscription found'
      });
    }

    const isActive = ['active', 'past_due'].includes(subscription.status);
    
    return NextResponse.json({
      isActive,
      planType: subscription.plan || 'Basic Plan',
      status: subscription.status,
      expiresAt: subscription.currentPeriodEnd?.toISOString(),
      message: isActive ? 'Subscription is active' : 'Subscription is not active'
    });

  } catch (error) {
    console.error('Error fetching client subscription:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription status' },
      { status: 500 }
    );
  }
}
