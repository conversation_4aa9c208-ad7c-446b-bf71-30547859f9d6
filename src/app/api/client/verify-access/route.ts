import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.NEXTAUTH_SECRET || 'spear-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get the current user session
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', message: 'Please log in to access devices' },
        { status: 401 }
      );
    }

    const { deviceId, token } = await request.json();

    if (!deviceId) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Device ID is required' },
        { status: 400 }
      );
    }

    // Find the user and verify they have access to this device
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        devices: {
          where: {
            OR: [
              { id: deviceId },
              { rustDeskId: deviceId }
            ]
          }
        },
        subscriptions: {
          where: {
            status: { in: ['active', 'past_due'] }
          },
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Not Found', message: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has active subscription
    const hasActiveSubscription = user.subscriptions.length > 0 && 
      ['active', 'past_due'].includes(user.subscriptions[0].status);

    if (!hasActiveSubscription) {
      return NextResponse.json(
        { 
          error: 'Subscription Required', 
          message: 'Active subscription required to access devices' 
        },
        { status: 403 }
      );
    }

    // Check if user has access to this specific device
    const device = user.devices[0];
    if (!device) {
      return NextResponse.json(
        { 
          error: 'Access Denied', 
          message: 'You do not have access to this device' 
        },
        { status: 403 }
      );
    }

    // Verify device is online and accessible
    if (device.status !== 'online') {
      return NextResponse.json(
        { 
          error: 'Device Offline', 
          message: 'Device is currently offline or unavailable' 
        },
        { status: 503 }
      );
    }

    // Check with RustDesk Pro server to ensure device is still enabled
    try {
      const rustdeskResponse = await fetch(
        `http://${process.env.RUSTDESK_SERVER_IP}:${process.env.RUSTDESK_API_PORT}/api/devices/${device.rustDeskId}`,
        {
          headers: {
            'Authorization': `Bearer ${process.env.RUSTDESK_API_TOKEN}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!rustdeskResponse.ok) {
        return NextResponse.json(
          { 
            error: 'Device Unavailable', 
            message: 'Device is not accessible on the server' 
          },
          { status: 503 }
        );
      }
    } catch (error) {
      console.error('Failed to verify device with RustDesk server:', error);
      // Continue anyway - server might be temporarily unreachable
    }

    // Generate access token for this session
    const accessToken = jwt.sign(
      {
        userId: user.id,
        deviceId: device.id,
        rustDeskId: device.rustDeskId,
        email: user.email,
        exp: Math.floor(Date.now() / 1000) + (60 * 60 * 2) // 2 hours
      },
      JWT_SECRET
    );

    // Log the access attempt for audit purposes
    console.log(`Device access granted: ${user.email} → ${device.name} (${device.rustDeskId})`);

    // Update device last accessed timestamp
    await prisma.device.update({
      where: { id: device.id },
      data: { updatedAt: new Date() }
    });

    return NextResponse.json({
      success: true,
      accessToken,
      device: {
        id: device.id,
        name: device.name,
        rustDeskId: device.rustDeskId,
        model: device.model,
        status: device.status
      },
      session: {
        expiresAt: new Date(Date.now() + (60 * 60 * 2 * 1000)).toISOString(),
        serverConfig: {
          serverIp: process.env.RUSTDESK_SERVER_IP,
          serverPort: process.env.RUSTDESK_SERVER_PORT,
          relayPort: process.env.RUSTDESK_RELAY_PORT,
          serverKey: process.env.RUSTDESK_SERVER_KEY
        }
      }
    });

  } catch (error) {
    console.error('Error verifying device access:', error);
    return NextResponse.json(
      { 
        error: 'Internal Server Error', 
        message: 'Failed to verify access. Please try again.' 
      },
      { status: 500 }
    );
  }
}
