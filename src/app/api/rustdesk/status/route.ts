import { NextRequest, NextResponse } from 'next/server';
import { getRustDeskConfig } from '@/lib/rustdesk-config';

export async function GET(request: NextRequest) {
  try {
    const config = getRustDeskConfig();
    
    // Check if we have server configuration
    if (!config.serverIp || !config.apiPort) {
      return NextResponse.json(
        { 
          status: 'error', 
          message: 'RustDesk server not configured',
          online: false 
        },
        { status: 503 }
      );
    }

    // Try to connect to the RustDesk server
    const serverUrl = `https://${config.serverIp}:${config.apiPort}`;
    
    try {
      // Attempt to reach the server with a timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
      
      const response = await fetch(serverUrl, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'User-Agent': 'SPEAR-Health-Check/1.0'
        }
      });
      
      clearTimeout(timeoutId);
      
      return NextResponse.json({
        status: 'online',
        message: 'RustDesk server is responding',
        online: true,
        serverUrl,
        responseTime: Date.now(),
        httpStatus: response.status
      });
      
    } catch (fetchError) {
      // If HTTPS fails, try HTTP (for development)
      const httpUrl = `http://${config.serverIp}:${config.apiPort}`;
      
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const response = await fetch(httpUrl, {
          method: 'HEAD',
          signal: controller.signal,
          headers: {
            'User-Agent': 'SPEAR-Health-Check/1.0'
          }
        });
        
        clearTimeout(timeoutId);
        
        return NextResponse.json({
          status: 'online',
          message: 'RustDesk server is responding (HTTP)',
          online: true,
          serverUrl: httpUrl,
          responseTime: Date.now(),
          httpStatus: response.status,
          warning: 'Server is using HTTP instead of HTTPS'
        });
        
      } catch (httpError) {
        return NextResponse.json(
          {
            status: 'offline',
            message: 'RustDesk server is not responding',
            online: false,
            serverUrl,
            error: fetchError instanceof Error ? fetchError.message : 'Connection failed'
          },
          { status: 503 }
        );
      }
    }
    
  } catch (error) {
    console.error('Error checking RustDesk server status:', error);
    
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to check server status',
        online: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Health check endpoint that returns detailed server information
export async function POST(request: NextRequest) {
  try {
    const config = getRustDeskConfig();
    
    const healthData = {
      timestamp: new Date().toISOString(),
      server: {
        ip: config.serverIp,
        apiPort: config.apiPort,
        serverPort: config.serverPort,
        relayPort: config.relayPort,
        hasKey: !!config.serverKey,
        isCustomServer: config.isUsingCustomServer
      },
      services: {
        webConsole: { status: 'unknown', port: config.apiPort },
        idServer: { status: 'unknown', port: config.serverPort },
        relayServer: { status: 'unknown', port: config.relayPort }
      }
    };

    // Test each service
    const baseUrl = config.serverIp.includes('localhost') || config.serverIp.includes('192.168') 
      ? `http://${config.serverIp}` 
      : `https://${config.serverIp}`;

    // Test web console
    try {
      const consoleResponse = await fetch(`${baseUrl}:${config.apiPort}`, {
        method: 'HEAD',
        signal: AbortSignal.timeout(3000)
      });
      healthData.services.webConsole.status = consoleResponse.ok ? 'online' : 'error';
    } catch {
      healthData.services.webConsole.status = 'offline';
    }

    // For ID and Relay servers, we can't easily test them via HTTP
    // so we'll mark them as 'unknown' unless we implement TCP checks
    
    return NextResponse.json(healthData);
    
  } catch (error) {
    console.error('Error performing detailed health check:', error);
    
    return NextResponse.json(
      {
        status: 'error',
        message: 'Health check failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
