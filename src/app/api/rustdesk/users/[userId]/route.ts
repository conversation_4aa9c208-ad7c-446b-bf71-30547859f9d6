import { NextRequest, NextResponse } from 'next/server';
import { getRustDeskConfig } from '@/lib/rustdesk-config';

interface RustDeskApiResponse {
  success: boolean;
  message?: string;
  data?: any;
}

// Helper function to make authenticated requests to RustDesk Pro API
async function makeRustDeskApiRequest(
  endpoint: string, 
  method: string = 'GET', 
  body?: any
): Promise<RustDeskApiResponse> {
  const config = getRustDeskConfig();
  
  if (!config.serverIp || !config.apiPort) {
    throw new Error('RustDesk server not configured');
  }

  const baseUrl = config.serverIp.includes('localhost') || config.serverIp.includes('192.168')
    ? `http://${config.serverIp}:${config.apiPort}`
    : `https://${config.serverIp}:${config.apiPort}`;

  const url = `${baseUrl}/api${endpoint}`;
  
  try {
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.RUSTDESK_API_TOKEN || 'admin-token'}`,
        'User-Agent': 'SPEAR-Admin/1.0'
      },
      body: body ? JSON.stringify(body) : undefined,
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });

    if (!response.ok) {
      throw new Error(`RustDesk API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return { success: true, data };
    
  } catch (error) {
    console.error('RustDesk API request failed:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'API request failed' 
    };
  }
}

// GET /api/rustdesk/users/[userId] - Get user details
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;
    
    // Make request to RustDesk Pro API to get user details
    const result = await makeRustDeskApiRequest(`/users/${userId}`);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.message || 'Failed to fetch user' },
        { status: 500 }
      );
    }

    return NextResponse.json(result.data);
    
  } catch (error) {
    console.error('Error fetching RustDesk user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user details' },
      { status: 500 }
    );
  }
}

// PATCH /api/rustdesk/users/[userId] - Update user (enable/disable access)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;
    const body = await request.json();
    
    // Validate request body
    if (typeof body.enabled !== 'boolean') {
      return NextResponse.json(
        { error: 'Invalid request body. "enabled" field is required and must be boolean.' },
        { status: 400 }
      );
    }

    // Make request to RustDesk Pro API to update user
    const result = await makeRustDeskApiRequest(`/users/${userId}`, 'PATCH', {
      enabled: body.enabled
    });
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.message || 'Failed to update user' },
        { status: 500 }
      );
    }

    // Log the access change for audit purposes
    console.log(`User ${userId} access ${body.enabled ? 'enabled' : 'disabled'} by admin`);

    return NextResponse.json({
      success: true,
      message: `User access ${body.enabled ? 'enabled' : 'disabled'} successfully`,
      data: result.data
    });
    
  } catch (error) {
    console.error('Error updating RustDesk user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE /api/rustdesk/users/[userId] - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;
    
    // Make request to RustDesk Pro API to delete user
    const result = await makeRustDeskApiRequest(`/users/${userId}`, 'DELETE');
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.message || 'Failed to delete user' },
        { status: 500 }
      );
    }

    // Log the user deletion for audit purposes
    console.log(`User ${userId} deleted by admin`);

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    });
    
  } catch (error) {
    console.error('Error deleting RustDesk user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}

// PUT /api/rustdesk/users/[userId] - Create or update user with full details
export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params;
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Prepare user data for RustDesk API
    const userData = {
      name: body.name,
      email: body.email,
      enabled: body.enabled !== false, // Default to enabled
      isAdmin: body.isAdmin || false,
      password: body.password || generateRandomPassword()
    };

    // Make request to RustDesk Pro API to create/update user
    const result = await makeRustDeskApiRequest(`/users/${userId}`, 'PUT', userData);
    
    if (!result.success) {
      return NextResponse.json(
        { error: result.message || 'Failed to create/update user' },
        { status: 500 }
      );
    }

    // Log the user creation/update for audit purposes
    console.log(`User ${userId} (${body.email}) created/updated by admin`);

    return NextResponse.json({
      success: true,
      message: 'User created/updated successfully',
      data: result.data
    });
    
  } catch (error) {
    console.error('Error creating/updating RustDesk user:', error);
    return NextResponse.json(
      { error: 'Failed to create/update user' },
      { status: 500 }
    );
  }
}

// Helper function to generate a random password
function generateRandomPassword(length: number = 12): string {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}
