import { NextResponse } from "next/server";
import { getRustDeskConfig, isRustDeskConfigured } from "@/lib/rustdesk-config";

/**
 * API route to check RustDesk configuration status
 * 
 * @returns JSON response with configuration status
 */
export async function GET() {
  const config = getRustDeskConfig();
  const isConfigured = isRustDeskConfigured();
  
  return NextResponse.json({
    isConfigured,
    serverIp: config.serverIp,
    serverPort: config.serverPort,
    apiPort: config.apiPort,
    serverAddress: config.serverAddress,
    apiAddress: config.apiAddress,
    // Include raw environment variables for debugging
    envVars: {
      RUSTDESK_SERVER_IP: process.env.RUSTDESK_SERVER_IP ? 'set' : 'not set',
      RUSTDESK_SERVER_PORT: process.env.RUSTDESK_SERVER_PORT ? 'set' : 'not set',
      RUSTDESK_API_PORT: process.env.RUSTDESK_API_PORT ? 'set' : 'not set',
    }
  });
}
