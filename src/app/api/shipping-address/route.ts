import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

/**
 * GET handler for fetching user's shipping addresses
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get user's shipping addresses
    const addresses = await prisma.shippingAddress.findMany({
      where: { userId: session.user.id as string },
      orderBy: [
        { isDefault: 'desc' },
        { createdAt: 'desc' }
      ],
    });

    return NextResponse.json({ addresses });
  } catch (error) {
    console.error("Error fetching shipping addresses:", error);
    return NextResponse.json(
      { error: "Failed to fetch shipping addresses" },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new shipping address
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      name,
      streetAddress,
      city,
      state,
      postalCode,
      country,
      phoneNumber,
      isDefault,
      subscriptionId,
    } = body;

    // Validate required fields
    if (!name || !streetAddress || !city || !state || !postalCode || !country || !phoneNumber) {
      return NextResponse.json(
        { error: "All address fields are required" },
        { status: 400 }
      );
    }

    // If this is being set as default, unset other default addresses
    if (isDefault) {
      await prisma.shippingAddress.updateMany({
        where: { 
          userId: session.user.id as string,
          isDefault: true 
        },
        data: { isDefault: false },
      });
    }

    // Create the shipping address
    const address = await prisma.shippingAddress.create({
      data: {
        userId: session.user.id as string,
        subscriptionId: subscriptionId || null,
        name,
        streetAddress,
        city,
        state,
        postalCode,
        country,
        phoneNumber,
        isDefault: isDefault || false,
      },
    });

    return NextResponse.json(address);
  } catch (error) {
    console.error("Error creating shipping address:", error);
    return NextResponse.json(
      { error: "Failed to create shipping address" },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a shipping address
 */
export async function PUT(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      id,
      name,
      streetAddress,
      city,
      state,
      postalCode,
      country,
      phoneNumber,
      isDefault,
    } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json(
        { error: "Address ID is required" },
        { status: 400 }
      );
    }

    // Check if address belongs to user
    const existingAddress = await prisma.shippingAddress.findFirst({
      where: { 
        id,
        userId: session.user.id as string 
      },
    });

    if (!existingAddress) {
      return NextResponse.json(
        { error: "Address not found" },
        { status: 404 }
      );
    }

    // If this is being set as default, unset other default addresses
    if (isDefault && !existingAddress.isDefault) {
      await prisma.shippingAddress.updateMany({
        where: { 
          userId: session.user.id as string,
          isDefault: true,
          id: { not: id }
        },
        data: { isDefault: false },
      });
    }

    // Update the shipping address
    const updatedAddress = await prisma.shippingAddress.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(streetAddress && { streetAddress }),
        ...(city && { city }),
        ...(state && { state }),
        ...(postalCode && { postalCode }),
        ...(country && { country }),
        ...(phoneNumber && { phoneNumber }),
        ...(isDefault !== undefined && { isDefault }),
      },
    });

    return NextResponse.json(updatedAddress);
  } catch (error) {
    console.error("Error updating shipping address:", error);
    return NextResponse.json(
      { error: "Failed to update shipping address" },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a shipping address
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get address ID from query params
    const { searchParams } = new URL(request.url);
    const addressId = searchParams.get("id");

    if (!addressId) {
      return NextResponse.json(
        { error: "Address ID is required" },
        { status: 400 }
      );
    }

    // Check if address belongs to user
    const existingAddress = await prisma.shippingAddress.findFirst({
      where: { 
        id: addressId,
        userId: session.user.id as string 
      },
    });

    if (!existingAddress) {
      return NextResponse.json(
        { error: "Address not found" },
        { status: 404 }
      );
    }

    // Delete the shipping address
    await prisma.shippingAddress.delete({
      where: { id: addressId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting shipping address:", error);
    return NextResponse.json(
      { error: "Failed to delete shipping address" },
      { status: 500 }
    );
  }
}
