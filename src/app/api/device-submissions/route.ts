import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { createNotification } from "@/lib/notifications";

/**
 * GET handler for fetching device submissions
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");

    let whereClause: any = {};

    // If user is not admin, only show their own submissions
    if (session.user.role !== "ADMIN") {
      whereClause.userId = session.user.id as string;
    }

    // Filter by status if provided
    if (status) {
      whereClause.status = status;
    }

    // Get device submissions
    const submissions = await prisma.deviceSubmission.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({ submissions });
  } catch (error) {
    console.error("Error fetching device submissions:", error);
    return NextResponse.json(
      { error: "Failed to fetch device submissions" },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new device submission
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { deviceName, rustDeskId, deviceType = "desktop" } = body;

    // Validate required fields
    if (!deviceName || !rustDeskId) {
      return NextResponse.json(
        { error: "Device name and RustDesk ID are required" },
        { status: 400 }
      );
    }

    // Check if user already has a pending or approved desktop device submission
    if (deviceType === "desktop") {
      const existingSubmission = await prisma.deviceSubmission.findFirst({
        where: {
          userId: session.user.id as string,
          deviceType: "desktop",
          status: { in: ["pending", "approved"] },
        },
      });

      if (existingSubmission) {
        return NextResponse.json(
          { error: "You can only register one desktop device. Please wait for your current submission to be processed." },
          { status: 400 }
        );
      }
    }

    // Check if RustDesk ID is already in use
    const existingDevice = await prisma.device.findUnique({
      where: { rustDeskId },
    });

    if (existingDevice) {
      return NextResponse.json(
        { error: "This RustDesk ID is already registered" },
        { status: 400 }
      );
    }

    const existingSubmission = await prisma.deviceSubmission.findFirst({
      where: { rustDeskId },
    });

    if (existingSubmission) {
      return NextResponse.json(
        { error: "This RustDesk ID is already submitted for review" },
        { status: 400 }
      );
    }

    // Create the device submission
    const submission = await prisma.deviceSubmission.create({
      data: {
        userId: session.user.id as string,
        deviceName,
        rustDeskId,
        deviceType,
        status: "pending",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Create notification for admins
    try {
      await createNotification({
        title: "New Device Submission",
        message: `${submission.user.name} (${submission.user.email}) has submitted a ${deviceType} device "${deviceName}" for approval.`,
        type: "info",
        // No userId means it goes to all admins
      });
    } catch (notificationError) {
      console.error("Failed to create device submission notification:", notificationError);
    }

    return NextResponse.json(submission);
  } catch (error) {
    console.error("Error creating device submission:", error);
    return NextResponse.json(
      { error: "Failed to create device submission" },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating device submission status (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { id, status, adminNotes } = body;

    // Validate required fields
    if (!id || !status) {
      return NextResponse.json(
        { error: "Submission ID and status are required" },
        { status: 400 }
      );
    }

    // Validate status
    if (!["pending", "approved", "rejected"].includes(status)) {
      return NextResponse.json(
        { error: "Invalid status" },
        { status: 400 }
      );
    }

    // Get the submission
    const submission = await prisma.deviceSubmission.findUnique({
      where: { id },
      include: { user: true },
    });

    if (!submission) {
      return NextResponse.json(
        { error: "Submission not found" },
        { status: 404 }
      );
    }

    // Update the submission
    const updatedSubmission = await prisma.deviceSubmission.update({
      where: { id },
      data: {
        status,
        adminNotes: adminNotes || null,
        reviewedAt: new Date(),
        reviewedBy: session.user.id as string,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // If approved, create the device
    if (status === "approved") {
      await prisma.device.create({
        data: {
          name: submission.deviceName,
          rustDeskId: submission.rustDeskId,
          deviceType: submission.deviceType,
          status: "active",
          userId: submission.userId,
        },
      });

      // Create notification for user
      try {
        await createNotification({
          title: "Device Approved",
          message: `Your ${submission.deviceType} device "${submission.deviceName}" has been approved and is now active.`,
          type: "success",
          userId: submission.userId,
        });
      } catch (notificationError) {
        console.error("Failed to create device approval notification:", notificationError);
      }
    } else if (status === "rejected") {
      // Create notification for user
      try {
        await createNotification({
          title: "Device Rejected",
          message: `Your ${submission.deviceType} device "${submission.deviceName}" submission has been rejected. ${adminNotes ? `Reason: ${adminNotes}` : ""}`,
          type: "warning",
          userId: submission.userId,
        });
      } catch (notificationError) {
        console.error("Failed to create device rejection notification:", notificationError);
      }
    }

    return NextResponse.json(updatedSubmission);
  } catch (error) {
    console.error("Error updating device submission:", error);
    return NextResponse.json(
      { error: "Failed to update device submission" },
      { status: 500 }
    );
  }
}
