import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { 
  getRustDeskDevices, 
  getDeviceByRustDeskId, 
  createDevice, 
  updateDevice, 
  assignDeviceToUser, 
  unassignDevice 
} from "@/lib/rustdesk-api";

/**
 * GET handler for devices
 * 
 * Returns all devices for admin users, or only assigned devices for client users
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get devices based on user role
    let devices;
    if (session.user.role === "ADMIN") {
      // Admin can see all devices
      devices = await prisma.device.findMany({
        include: {
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });
    } else {
      // Client can only see their assigned devices
      devices = await prisma.device.findMany({
        where: {
          userId: session.user.id
        }
      });
    }

    return NextResponse.json({ devices });
  } catch (error) {
    console.error("Error fetching devices:", error);
    return NextResponse.json(
      { error: "Failed to fetch devices" },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new device
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { name, rustDeskId, password, model, userId } = body;

    // Validate required fields
    if (!name || !rustDeskId) {
      return NextResponse.json(
        { error: "Name and RustDesk ID are required" },
        { status: 400 }
      );
    }

    // Check if device already exists
    const existingDevice = await getDeviceByRustDeskId(rustDeskId);
    if (existingDevice) {
      return NextResponse.json(
        { error: "Device with this RustDesk ID already exists" },
        { status: 409 }
      );
    }

    // Create the device
    const device = await createDevice({
      name,
      rustDeskId,
      password,
      model,
      userId
    });

    return NextResponse.json({ device });
  } catch (error) {
    console.error("Error creating device:", error);
    return NextResponse.json(
      { error: "Failed to create device" },
      { status: 500 }
    );
  }
}

/**
 * PATCH handler for updating a device
 */
export async function PATCH(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { id, name, rustDeskId, password, model, userId, status } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json(
        { error: "Device ID is required" },
        { status: 400 }
      );
    }

    // Update the device
    const device = await updateDevice(id, {
      name,
      rustDeskId,
      password,
      model,
      userId,
      status
    });

    return NextResponse.json({ device });
  } catch (error) {
    console.error("Error updating device:", error);
    return NextResponse.json(
      { error: "Failed to update device" },
      { status: 500 }
    );
  }
}
