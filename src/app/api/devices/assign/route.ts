import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { assignDeviceToUser, unassignDevice } from "@/lib/rustdesk-api";

/**
 * POST handler for assigning a device to a user
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { deviceId, userId } = body;

    // Validate required fields
    if (!deviceId || !userId) {
      return NextResponse.json(
        { error: "Device ID and User ID are required" },
        { status: 400 }
      );
    }

    // Assign the device to the user
    const device = await assignDeviceToUser(deviceId, userId);

    return NextResponse.json({ 
      success: true,
      device 
    });
  } catch (error) {
    console.error("Error assigning device:", error);
    return NextResponse.json(
      { error: "Failed to assign device" },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for unassigning a device
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get the device ID from the URL
    const { searchParams } = new URL(request.url);
    const deviceId = searchParams.get("deviceId");

    // Validate required fields
    if (!deviceId) {
      return NextResponse.json(
        { error: "Device ID is required" },
        { status: 400 }
      );
    }

    // Unassign the device
    const device = await unassignDevice(deviceId);

    return NextResponse.json({ 
      success: true,
      device 
    });
  } catch (error) {
    console.error("Error unassigning device:", error);
    return NextResponse.json(
      { error: "Failed to unassign device" },
      { status: 500 }
    );
  }
}
