import { NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

export async function POST() {
  try {
    console.log('Starting database migration...');
    
    // Run Prisma migrations
    const { stdout, stderr } = await execAsync('npx prisma migrate deploy');
    
    console.log('Migration stdout:', stdout);
    if (stderr) {
      console.log('Migration stderr:', stderr);
    }
    
    return NextResponse.json({
      success: true,
      message: "Database migration completed successfully",
      output: stdout,
      errors: stderr || null,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Migration failed:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Database migration failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Use POST method to run database migrations",
    endpoint: "/api/migrate-db",
    method: "POST"
  });
}
