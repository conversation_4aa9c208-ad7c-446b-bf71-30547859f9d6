import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { createNotification } from "@/lib/notifications";

/**
 * GET handler for fetching trade-in requests
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    let whereClause: any = {};

    // If user is not admin, only show their own requests
    if (session.user.role !== "ADMIN") {
      whereClause.userId = session.user.id as string;
    }

    // Get trade-in requests
    const requests = await prisma.tradeInRequest.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return NextResponse.json({ requests });
  } catch (error) {
    console.error("Error fetching trade-in requests:", error);
    return NextResponse.json(
      { error: "Failed to fetch trade-in requests" },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new trade-in request
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { deviceModel = "Samsung A14", deviceCondition, estimatedValue } = body;

    // Validate required fields
    if (!deviceCondition || !estimatedValue) {
      return NextResponse.json(
        { error: "Device condition and estimated value are required" },
        { status: 400 }
      );
    }

    // Check if user already has a pending trade-in request
    const existingRequest = await prisma.tradeInRequest.findFirst({
      where: {
        userId: session.user.id as string,
        status: { in: ["pending", "shipped", "received"] },
      },
    });

    if (existingRequest) {
      return NextResponse.json(
        { error: "You already have a pending trade-in request" },
        { status: 400 }
      );
    }

    // Create the trade-in request
    const tradeInRequest = await prisma.tradeInRequest.create({
      data: {
        userId: session.user.id as string,
        deviceModel,
        deviceCondition,
        estimatedValue: parseFloat(estimatedValue),
        status: "pending",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Create notification for admins
    try {
      await createNotification({
        title: "New Trade-In Request",
        message: `${tradeInRequest.user.name} (${tradeInRequest.user.email}) has submitted a ${deviceModel} trade-in request (${deviceCondition} condition, estimated value: $${estimatedValue}).`,
        type: "info",
        // No userId means it goes to all admins
      });
    } catch (notificationError) {
      console.error("Failed to create trade-in notification:", notificationError);
    }

    return NextResponse.json(tradeInRequest);
  } catch (error) {
    console.error("Error creating trade-in request:", error);
    return NextResponse.json(
      { error: "Failed to create trade-in request" },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating trade-in request (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const {
      id,
      status,
      trackingNumber,
      actualValue,
      rebateAmount,
      rebateApplied,
      adminNotes,
      deviceFunctional,
      conditionMatches,
      returnTrackingNumber
    } = body;

    // Validate required fields
    if (!id) {
      return NextResponse.json(
        { error: "Request ID is required" },
        { status: 400 }
      );
    }

    // Get the request
    const tradeInRequest = await prisma.tradeInRequest.findUnique({
      where: { id },
      include: { user: true },
    });

    if (!tradeInRequest) {
      return NextResponse.json(
        { error: "Trade-in request not found" },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};

    // Handle device processing workflow
    if (status === "processed" && deviceFunctional !== undefined && conditionMatches !== undefined) {
      if (deviceFunctional && conditionMatches) {
        // Device is functional and condition matches - approve trade-in
        updateData.status = "completed";
        updateData.processedAt = new Date();
        updateData.rebateApplied = true;

        // Apply rebate to user's subscription if rebateAmount is provided
        if (rebateAmount && parseFloat(rebateAmount) > 0) {
          try {
            // Here you would integrate with your subscription/billing system
            // For now, we'll just mark it as applied
            updateData.rebateAmount = parseFloat(rebateAmount);

            // TODO: Integrate with Stripe/PayPal to apply credit to subscription
            console.log(`Applying rebate of $${rebateAmount} to user ${tradeInRequest.userId}`);
          } catch (rebateError) {
            console.error("Failed to apply rebate:", rebateError);
            // Continue with the update but note the rebate application failure
            updateData.adminNotes = (adminNotes || "") + " [Note: Rebate application failed - manual intervention required]";
          }
        }
      } else {
        // Device is not functional or condition doesn't match - reject trade-in
        updateData.status = "rejected";
        updateData.processedAt = new Date();
        updateData.rebateApplied = false;
        updateData.rebateAmount = 0;

        // Add rejection reason to admin notes
        const rejectionReason = !deviceFunctional
          ? "Device is not functional"
          : "Device condition does not match description";
        updateData.adminNotes = (adminNotes || "") + ` [Rejection Reason: ${rejectionReason}]`;
      }
    } else {
      // Regular status update without device processing
      if (status) updateData.status = status;
      if (trackingNumber) updateData.trackingNumber = trackingNumber;
      if (actualValue !== undefined) updateData.actualValue = parseFloat(actualValue);
      if (rebateAmount !== undefined) updateData.rebateAmount = parseFloat(rebateAmount);
      if (rebateApplied !== undefined) updateData.rebateApplied = rebateApplied;
      if (adminNotes !== undefined) updateData.adminNotes = adminNotes;

      // Set processedAt if status is completed
      if (status === "completed") {
        updateData.processedAt = new Date();
      }
    }

    // Add return tracking number if provided
    if (returnTrackingNumber) {
      updateData.adminNotes = (updateData.adminNotes || adminNotes || "") + ` [Return Tracking: ${returnTrackingNumber}]`;
    }

    // Update the request
    const updatedRequest = await prisma.tradeInRequest.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Create notifications based on status
    try {
      let notificationMessage = "";
      let notificationType: "info" | "success" | "warning" | "error" = "info";

      switch (updatedRequest.status) {
        case "shipped":
          notificationMessage = `Your ${tradeInRequest.deviceModel} trade-in has been approved. Please ship your device using the provided instructions.`;
          notificationType = "info";
          break;
        case "received":
          notificationMessage = `We have received your ${tradeInRequest.deviceModel} and are processing your trade-in.`;
          notificationType = "info";
          break;
        case "processed":
          notificationMessage = `Your ${tradeInRequest.deviceModel} trade-in has been processed. ${actualValue ? `Actual value: $${actualValue}` : ""}`;
          notificationType = "info";
          break;
        case "completed":
          if (updatedRequest.rebateApplied && updatedRequest.rebateAmount) {
            notificationMessage = `🎉 Your ${tradeInRequest.deviceModel} trade-in is complete! Your device has been configured and shipped back to you. Rebate of $${updatedRequest.rebateAmount} has been applied to your account.`;
            notificationType = "success";
          } else {
            notificationMessage = `Your ${tradeInRequest.deviceModel} trade-in is complete! ${rebateAmount ? `Rebate of $${rebateAmount} has been applied to your account.` : ""}`;
            notificationType = "success";
          }
          break;
        case "rejected":
          notificationMessage = `Your ${tradeInRequest.deviceModel} trade-in has been rejected. Your device will be shipped back to you without configuration. ${updatedRequest.adminNotes ? "Reason: " + updatedRequest.adminNotes : ""}`;
          notificationType = "warning";
          break;
      }

      if (notificationMessage) {
        await createNotification({
          title: "Trade-In Update",
          message: notificationMessage,
          type: notificationType,
          userId: tradeInRequest.userId,
        });
      }
    } catch (notificationError) {
      console.error("Failed to create trade-in update notification:", notificationError);
    }

    return NextResponse.json(updatedRequest);
  } catch (error) {
    console.error("Error updating trade-in request:", error);
    return NextResponse.json(
      { error: "Failed to update trade-in request" },
      { status: 500 }
    );
  }
}
