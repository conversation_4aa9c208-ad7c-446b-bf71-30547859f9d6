import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getPaymentService } from "@/lib/payment/payment-factory";
import { prisma } from "@/lib/prisma";

/**
 * Test Payment API
 * Allows testing PayPal integration with small amounts
 * Only accessible by admin for testing purposes
 */

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await auth();
    if (!session || !session.user || session.user.email !== "<EMAIL>") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { amount, currency = 'USD', description = 'Test Payment', testType = 'payment' } = body;

    // Validate amount (must be between $0.01 and $10.00 for testing)
    if (!amount || amount < 1 || amount > 1000) {
      return NextResponse.json(
        { error: "Test amount must be between $0.01 and $10.00 (1-1000 cents)" },
        { status: 400 }
      );
    }

    // Get PayPal payment service
    const paymentService = getPaymentService('paypal');

    if (testType === 'payment') {
      // Test one-time payment
      const paymentRequest = {
        customer: {
          id: 'test-customer',
          email: session.user.email!,
          name: session.user.name || 'Test User'
        },
        amount: {
          amount: amount,
          currency: currency
        },
        planId: 'test-payment',
        billingAddress: {
          fullName: 'Test User',
          email: session.user.email!,
          phone: '',
          address: '123 Test St',
          city: 'Test City',
          state: 'TS',
          zipCode: '12345',
          country: 'US'
        },
        metadata: {
          testPayment: true,
          testType: 'one-time',
          description: description,
          adminEmail: session.user.email
        }
      };

      const paymentResult = await paymentService.processPayment(paymentRequest);

      if (paymentResult.success) {
        // Create test order record
        const testOrder = await prisma.order.create({
          data: {
            userId: 'test-user',
            amount: paymentResult.amount.amount,
            currency: paymentResult.amount.currency,
            status: 'test_payment',
            paymentId: paymentResult.paymentId,
            subscriptionPlan: 'test-payment',
            notes: JSON.stringify({
              testPayment: true,
              description: description,
              adminEmail: session.user.email,
              paymentStatus: paymentResult.status,
              createdAt: new Date().toISOString()
            })
          }
        });

        return NextResponse.json({
          success: true,
          message: 'Test payment created successfully',
          payment: {
            id: paymentResult.paymentId,
            status: paymentResult.status,
            amount: paymentResult.amount,
            approvalUrl: paymentResult.metadata?.approvalUrl,
            orderId: testOrder.id
          },
          testOrder: {
            id: testOrder.id,
            amount: testOrder.amount,
            currency: testOrder.currency,
            status: testOrder.status
          }
        });
      } else {
        return NextResponse.json(
          { 
            success: false,
            error: paymentResult.error || "Test payment creation failed" 
          },
          { status: 400 }
        );
      }

    } else if (testType === 'subscription') {
      // Test subscription creation
      const subscriptionRequest = {
        customer: {
          id: 'test-customer',
          email: session.user.email!,
          name: session.user.name || 'Test User'
        },
        planId: 'test-subscription',
        billingAddress: {
          fullName: 'Test User',
          email: session.user.email!,
          phone: '',
          address: '123 Test St',
          city: 'Test City',
          state: 'TS',
          zipCode: '12345',
          country: 'US'
        },
        metadata: {
          testSubscription: true,
          testType: 'subscription',
          description: description,
          adminEmail: session.user.email,
          testAmount: amount
        }
      };

      const subscriptionResult = await paymentService.createSubscription(subscriptionRequest);

      if (subscriptionResult.success) {
        // Create test subscription record
        const testOrder = await prisma.order.create({
          data: {
            userId: 'test-user',
            amount: amount,
            currency: currency,
            status: 'test_subscription',
            paymentId: subscriptionResult.subscriptionId,
            subscriptionPlan: 'test-subscription',
            notes: JSON.stringify({
              testSubscription: true,
              description: description,
              adminEmail: session.user.email,
              subscriptionStatus: subscriptionResult.status,
              currentPeriodStart: subscriptionResult.currentPeriodStart,
              currentPeriodEnd: subscriptionResult.currentPeriodEnd,
              createdAt: new Date().toISOString()
            })
          }
        });

        return NextResponse.json({
          success: true,
          message: 'Test subscription created successfully',
          subscription: {
            id: subscriptionResult.subscriptionId,
            status: subscriptionResult.status,
            amount: subscriptionResult.amount,
            currentPeriodStart: subscriptionResult.currentPeriodStart,
            currentPeriodEnd: subscriptionResult.currentPeriodEnd,
            orderId: testOrder.id
          },
          testOrder: {
            id: testOrder.id,
            amount: testOrder.amount,
            currency: testOrder.currency,
            status: testOrder.status
          }
        });
      } else {
        return NextResponse.json(
          { 
            success: false,
            error: subscriptionResult.error || "Test subscription creation failed" 
          },
          { status: 400 }
        );
      }

    } else {
      return NextResponse.json(
        { error: "Invalid test type. Supported types: payment, subscription" },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error processing test payment:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve test payment history
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await auth();
    if (!session || !session.user || session.user.email !== "<EMAIL>") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    // Get test payments from database
    const testOrders = await prisma.order.findMany({
      where: {
        OR: [
          { status: 'test_payment' },
          { status: 'test_subscription' },
          { subscriptionPlan: 'test-payment' },
          { subscriptionPlan: 'test-subscription' }
        ]
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20 // Limit to last 20 test payments
    });

    const testPayments = testOrders.map(order => ({
      id: order.id,
      paymentId: order.paymentId,
      amount: order.amount,
      currency: order.currency,
      status: order.status,
      subscriptionPlan: order.subscriptionPlan,
      createdAt: order.createdAt,
      notes: order.notes ? JSON.parse(order.notes) : null
    }));

    return NextResponse.json({
      success: true,
      testPayments: testPayments,
      total: testPayments.length
    });

  } catch (error) {
    console.error('Error fetching test payments:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
