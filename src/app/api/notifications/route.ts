import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { 
  getNotifications, 
  getUnreadNotificationCount,
  markAllNotificationsAsRead 
} from "@/lib/notifications";

/**
 * GET handler for fetching notifications
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const countOnly = searchParams.get('count') === 'true';

    if (countOnly) {
      // Return only the unread count
      const count = await getUnreadNotificationCount(
        session.user.id as string, 
        session.user.role as string
      );
      return NextResponse.json({ count });
    } else {
      // Return all notifications
      const notifications = await getNotifications(
        session.user.id as string, 
        session.user.role as string
      );
      return NextResponse.json({ notifications });
    }
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return NextResponse.json(
      { error: "Failed to fetch notifications" },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for marking all notifications as read
 */
export async function PUT(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Mark all notifications as read
    const result = await markAllNotificationsAsRead(
      session.user.id as string, 
      session.user.role as string
    );

    return NextResponse.json({ 
      success: true, 
      updatedCount: result.count 
    });
  } catch (error) {
    console.error("Error marking notifications as read:", error);
    return NextResponse.json(
      { error: "Failed to mark notifications as read" },
      { status: 500 }
    );
  }
}
