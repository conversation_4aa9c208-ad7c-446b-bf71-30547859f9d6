import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { paymentId, customerEmail, customerName, billingAddress } = body;

    // Get payment details from database or Square
    let paymentRecord;
    try {
      paymentRecord = await prisma.payment.findFirst({
        where: {
          OR: [
            { squarePaymentId: paymentId },
            { id: paymentId }
          ]
        },
        include: {
          user: true
        }
      });
    } catch (dbError) {
      console.error('Database error, using provided data:', dbError);
    }

    // Get user details
    const user = paymentRecord?.user || {
      email: customerEmail || session.user.email,
      name: customerName || session.user.name || 'Customer'
    };

    // Generate invoice data
    const invoiceData = {
      invoiceNumber: `INV-${Date.now()}`,
      date: new Date().toISOString().split('T')[0],
      dueDate: new Date().toISOString().split('T')[0], // Paid immediately
      
      // Company details
      company: {
        name: "SPEAR Global",
        address: "Remote Access Solutions",
        email: "<EMAIL>",
        website: "https://spear-global.com"
      },
      
      // Customer details
      customer: {
        name: user.name,
        email: user.email,
        address: billingAddress || "Address on file"
      },
      
      // Payment details
      payment: {
        id: paymentRecord?.squarePaymentId || paymentId,
        amount: paymentRecord?.amount || 19900, // Default to $199 if not found
        currency: paymentRecord?.currency || 'USD',
        status: paymentRecord?.status || 'COMPLETED',
        planType: paymentRecord?.planType || 'single-user',
        couponCode: paymentRecord?.couponCode,
        createdAt: paymentRecord?.createdAt || new Date()
      }
    };

    // Calculate line items
    const planNames = {
      'single-user': 'Single User Plan',
      'two-user': 'Two User Bundle'
    };
    
    const planName = planNames[invoiceData.payment.planType as keyof typeof planNames] || 'SPEAR Subscription';
    const amount = invoiceData.payment.amount / 100; // Convert cents to dollars
    
    const lineItems = [
      {
        description: `${planName} - Monthly Subscription`,
        quantity: 1,
        unitPrice: invoiceData.payment.couponCode ? 299.00 : amount,
        total: invoiceData.payment.couponCode ? 299.00 : amount
      }
    ];

    // Add discount if coupon was used
    if (invoiceData.payment.couponCode === 'SPEARMINT') {
      lineItems.push({
        description: 'SPEARMINT Discount',
        quantity: 1,
        unitPrice: -100.00,
        total: -100.00
      });
    }

    const subtotal = lineItems.reduce((sum, item) => sum + item.total, 0);
    const tax = 0; // No tax for now
    const total = subtotal + tax;

    // Create invoice record in database
    let invoice;
    try {
      invoice = await prisma.invoice.create({
        data: {
          userId: paymentRecord?.userId || session.user.id,
          amount: total.toString(),
          status: 'paid',
          description: `${planName} subscription payment`,
          metadata: {
            invoiceNumber: invoiceData.invoiceNumber,
            squarePaymentId: invoiceData.payment.id,
            planType: invoiceData.payment.planType,
            couponCode: invoiceData.payment.couponCode
          }
        }
      });
    } catch (dbError) {
      console.error('Failed to save invoice to database:', dbError);
      // Continue without database record
    }

    // Generate HTML invoice
    const invoiceHTML = generateInvoiceHTML({
      ...invoiceData,
      lineItems,
      subtotal,
      tax,
      total,
      invoiceId: invoice?.id || `temp-${invoiceData.invoiceNumber}`
    });

    return NextResponse.json({
      success: true,
      invoice: {
        id: invoice?.id || `temp-${invoiceData.invoiceNumber}`,
        invoiceNumber: invoiceData.invoiceNumber,
        amount: total,
        status: 'paid',
        date: invoiceData.date,
        html: invoiceHTML,
        downloadUrl: `/api/invoices/${invoice?.id || invoiceData.invoiceNumber}/pdf`
      }
    });

  } catch (error) {
    console.error('Error generating invoice:', error);
    return NextResponse.json(
      { error: 'Failed to generate invoice' },
      { status: 500 }
    );
  }
}

function generateInvoiceHTML(data: any): string {
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice ${data.invoiceNumber}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
        .header { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .company { text-align: left; }
        .company h1 { color: #2563eb; margin: 0; font-size: 28px; }
        .invoice-info { text-align: right; }
        .invoice-info h2 { margin: 0; color: #666; }
        .details { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .bill-to, .payment-info { width: 45%; }
        .bill-to h3, .payment-info h3 { color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 5px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .total-row { font-weight: bold; background-color: #f8f9fa; }
        .payment-status { background-color: #d4edda; color: #155724; padding: 10px; border-radius: 5px; text-align: center; margin-top: 20px; }
        .footer { margin-top: 40px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company">
            <h1>SPEAR Global</h1>
            <p>Remote Access Solutions<br>
            Email: <EMAIL><br>
            Website: spear-global.com</p>
        </div>
        <div class="invoice-info">
            <h2>INVOICE</h2>
            <p><strong>Invoice #:</strong> ${data.invoiceNumber}<br>
            <strong>Date:</strong> ${data.date}<br>
            <strong>Due Date:</strong> ${data.dueDate}</p>
        </div>
    </div>

    <div class="details">
        <div class="bill-to">
            <h3>Bill To:</h3>
            <p><strong>${data.customer.name}</strong><br>
            ${data.customer.email}<br>
            ${data.customer.address}</p>
        </div>
        <div class="payment-info">
            <h3>Payment Information:</h3>
            <p><strong>Payment ID:</strong> ${data.payment.id}<br>
            <strong>Payment Method:</strong> Square<br>
            <strong>Status:</strong> ${data.payment.status}</p>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Description</th>
                <th>Qty</th>
                <th>Unit Price</th>
                <th>Total</th>
            </tr>
        </thead>
        <tbody>
            ${data.lineItems.map((item: any) => `
                <tr>
                    <td>${item.description}</td>
                    <td>${item.quantity}</td>
                    <td>$${item.unitPrice.toFixed(2)}</td>
                    <td>$${item.total.toFixed(2)}</td>
                </tr>
            `).join('')}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="3"><strong>Subtotal:</strong></td>
                <td><strong>$${data.subtotal.toFixed(2)}</strong></td>
            </tr>
            <tr>
                <td colspan="3"><strong>Tax:</strong></td>
                <td><strong>$${data.tax.toFixed(2)}</strong></td>
            </tr>
            <tr class="total-row">
                <td colspan="3"><strong>Total:</strong></td>
                <td><strong>$${data.total.toFixed(2)}</strong></td>
            </tr>
        </tfoot>
    </table>

    <div class="payment-status">
        <strong>✅ PAID IN FULL</strong> - Thank you for your payment!
    </div>

    <div class="footer">
        <p>Thank you for choosing SPEAR Global for your remote access needs.<br>
        For support, contact <NAME_EMAIL></p>
    </div>
</body>
</html>`;
}
