import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { createNotification } from "@/lib/notifications";

/**
 * GET handler for fetching a specific support ticket
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: ticketId } = await params;

    // Build where clause based on user role
    let whereClause: any = { id: ticketId };
    
    if (session.user.role === "CLIENT") {
      // Clients can only see their own tickets
      whereClause.userId = session.user.id;
    }
    // Admins can see all tickets

    const ticket = await prisma.supportTicket.findFirst({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        comments: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json(
        { error: "Ticket not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(ticket);
  } catch (error) {
    console.error("Error fetching support ticket:", error);
    return NextResponse.json(
      { error: "Failed to fetch support ticket" },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a support ticket
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: ticketId } = await params;
    const body = await request.json();

    // Check if ticket exists and user has permission
    let whereClause: any = { id: ticketId };
    
    if (session.user.role === "CLIENT") {
      // Clients can only update their own tickets
      whereClause.userId = session.user.id;
    }

    const existingTicket = await prisma.supportTicket.findFirst({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!existingTicket) {
      return NextResponse.json(
        { error: "Ticket not found" },
        { status: 404 }
      );
    }

    // Prepare update data based on user role
    let updateData: any = {};

    if (session.user.role === "ADMIN") {
      // Admins can update all fields
      const { status, priority, adminNotes, assignedTo } = body;
      
      if (status) updateData.status = status;
      if (priority) updateData.priority = priority;
      if (adminNotes !== undefined) updateData.adminNotes = adminNotes;
      if (assignedTo !== undefined) updateData.assignedTo = assignedTo;
      
      // Set closedAt if status is being changed to closed
      if (status === "closed" && existingTicket.status !== "closed") {
        updateData.closedAt = new Date();
      }
    } else {
      // Clients can only update subject and description, and only if ticket is open
      if (existingTicket.status !== "open") {
        return NextResponse.json(
          { error: "Cannot update a closed or in-progress ticket" },
          { status: 400 }
        );
      }
      
      const { subject, description } = body;
      if (subject) updateData.subject = subject;
      if (description) updateData.description = description;
    }

    // Update the ticket
    const updatedTicket = await prisma.supportTicket.update({
      where: { id: ticketId },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Create notification for status changes (admin updates)
    if (session.user.role === "ADMIN" && updateData.status) {
      try {
        await createNotification({
          title: "Ticket Status Updated",
          message: `Ticket "${existingTicket.subject}" has been ${updateData.status === "closed" ? "closed" : `marked as ${updateData.status}`}.`,
          type: updateData.status === "closed" ? "success" : "info",
          userId: existingTicket.userId, // Notify the ticket creator
        });
      } catch (notificationError) {
        console.error("Failed to create ticket update notification:", notificationError);
      }
    }

    return NextResponse.json(updatedTicket);
  } catch (error) {
    console.error("Error updating support ticket:", error);
    return NextResponse.json(
      { error: "Failed to update support ticket" },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a support ticket
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is an admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: ticketId } = await params;

    // Check if ticket exists
    const existingTicket = await prisma.supportTicket.findUnique({
      where: { id: ticketId },
    });

    if (!existingTicket) {
      return NextResponse.json(
        { error: "Ticket not found" },
        { status: 404 }
      );
    }

    // Delete the ticket
    await prisma.supportTicket.delete({
      where: { id: ticketId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting support ticket:", error);
    return NextResponse.json(
      { error: "Failed to delete support ticket" },
      { status: 500 }
    );
  }
}
