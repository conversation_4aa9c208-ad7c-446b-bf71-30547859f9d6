import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { createNotification } from "@/lib/notifications";

/**
 * GET handler for fetching ticket comments
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: ticketId } = await params;

    // Check if user has access to this ticket
    let whereClause: any = { id: ticketId };
    
    if (session.user.role === "CLIENT") {
      // Clients can only access their own tickets
      whereClause.userId = session.user.id;
    }
    // Ad<PERSON> can access all tickets

    const ticket = await prisma.supportTicket.findFirst({
      where: whereClause,
    });

    if (!ticket) {
      return NextResponse.json(
        { error: "Ticket not found" },
        { status: 404 }
      );
    }

    // Fetch comments for this ticket
    const comments = await prisma.ticketComment.findMany({
      where: {
        ticketId: ticketId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return NextResponse.json({ comments });
  } catch (error) {
    console.error("Error fetching ticket comments:", error);
    return NextResponse.json(
      { error: "Failed to fetch ticket comments" },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new ticket comment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id: ticketId } = await params;
    const body = await request.json();
    const { content } = body;

    // Validate required fields
    if (!content || content.trim().length === 0) {
      return NextResponse.json(
        { error: "Comment content is required" },
        { status: 400 }
      );
    }

    // Check if user has access to this ticket
    let whereClause: any = { id: ticketId };
    
    if (session.user.role === "CLIENT") {
      // Clients can only comment on their own tickets
      whereClause.userId = session.user.id;
    }
    // Admins can comment on all tickets

    const ticket = await prisma.supportTicket.findFirst({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!ticket) {
      return NextResponse.json(
        { error: "Ticket not found" },
        { status: 404 }
      );
    }

    // Create the comment
    const comment = await prisma.ticketComment.create({
      data: {
        ticketId: ticketId,
        userId: session.user.id as string,
        content: content.trim(),
        isAdmin: session.user.role === "ADMIN",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    // Update the ticket's updatedAt timestamp
    await prisma.supportTicket.update({
      where: { id: ticketId },
      data: { updatedAt: new Date() },
    });

    // Create notification based on who commented
    try {
      if (session.user.role === "CLIENT") {
        // Client commented - notify admins
        await createNotification({
          title: "New Ticket Comment",
          message: `${ticket.user.name} (${ticket.user.email}) added a comment to ticket: "${ticket.subject}"`,
          type: "info",
          // No userId means it goes to all admins
        });
      } else {
        // Admin commented - notify the ticket creator
        await createNotification({
          title: "Support Team Response",
          message: `Our support team has responded to your ticket: "${ticket.subject}"`,
          type: "info",
          userId: ticket.userId,
        });
      }
    } catch (notificationError) {
      console.error("Failed to create comment notification:", notificationError);
    }

    return NextResponse.json(comment);
  } catch (error) {
    console.error("Error creating ticket comment:", error);
    return NextResponse.json(
      { error: "Failed to create ticket comment" },
      { status: 500 }
    );
  }
}
