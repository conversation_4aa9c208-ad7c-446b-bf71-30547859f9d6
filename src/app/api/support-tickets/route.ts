import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";
import { createNotification } from "@/lib/notifications";

/**
 * GET handler for fetching support tickets
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const category = searchParams.get('category');

    // Build where clause based on user role
    let whereClause: any = {};
    
    if (session.user.role === "CLIENT") {
      // Clients can only see their own tickets
      whereClause.userId = session.user.id;
    }
    // Admins can see all tickets (no additional filter)

    // Add optional filters
    if (status) {
      whereClause.status = status;
    }
    if (category) {
      whereClause.category = category;
    }

    const tickets = await prisma.supportTicket.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json({ tickets });
  } catch (error) {
    console.error("Error fetching support tickets:", error);
    return NextResponse.json(
      { error: "Failed to fetch support tickets" },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a new support ticket
 */
export async function POST(request: NextRequest) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { subject, description, category = "technical", priority = "medium" } = body;

    // Validate required fields
    if (!subject || !description) {
      return NextResponse.json(
        { error: "Subject and description are required" },
        { status: 400 }
      );
    }

    // Validate category
    const validCategories = ["technical", "billing", "account", "feature", "other"];
    if (!validCategories.includes(category)) {
      return NextResponse.json(
        { error: "Invalid category" },
        { status: 400 }
      );
    }

    // Validate priority
    const validPriorities = ["low", "medium", "high", "urgent"];
    if (!validPriorities.includes(priority)) {
      return NextResponse.json(
        { error: "Invalid priority" },
        { status: 400 }
      );
    }

    // Create the support ticket
    const ticket = await prisma.supportTicket.create({
      data: {
        userId: session.user.id as string,
        subject,
        description,
        category,
        priority,
        status: "open",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Create notification for admins
    try {
      await createNotification({
        title: "New Support Ticket",
        message: `${ticket.user.name} (${ticket.user.email}) has submitted a ${priority} priority ${category} ticket: "${subject}"`,
        type: priority === "urgent" ? "error" : priority === "high" ? "warning" : "info",
        // No userId means it goes to all admins
      });
    } catch (notificationError) {
      console.error("Failed to create ticket notification:", notificationError);
    }

    return NextResponse.json(ticket);
  } catch (error) {
    console.error("Error creating support ticket:", error);
    return NextResponse.json(
      { error: "Failed to create support ticket" },
      { status: 500 }
    );
  }
}
