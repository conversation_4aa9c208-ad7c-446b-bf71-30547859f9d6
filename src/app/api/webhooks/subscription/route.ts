import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
// import { rustDeskSubscriptionManager } from '@/lib/rustdesk-subscription-manager'; // Temporarily disabled to fix Firebase build error
import { prisma } from '@/lib/db';
import Stripe from 'stripe';

// Initialize Stripe only if the secret key is available
let stripe: Stripe | null = null;
let endpointSecret: string | null = null;

if (process.env.STRIPE_SECRET_KEY) {
  stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2023-10-16',
  });
  endpointSecret = process.env.STRIPE_WEBHOOK_SECRET || null;
}

export async function POST(request: NextRequest) {
  // Check if Stripe is properly initialized
  if (!stripe || !endpointSecret) {
    console.log('Stripe webhook: Stripe not configured');
    return NextResponse.json({
      received: true,
      message: 'Stripe not configured'
    });
  }

  try {
    const body = await request.text();
    const headersList = headers();
    const sig = headersList.get('stripe-signature');

    if (!sig) {
      return NextResponse.json(
        { error: 'Missing stripe-signature header' },
        { status: 400 }
      );
    }

    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Webhook signature verification failed' },
        { status: 400 }
      );
    }

    console.log(`Received webhook event: ${event.type}`);

    // For now, just log the event and return success
    // TODO: Re-enable subscription handling once RustDesk integration is fixed
    console.log('Webhook received but subscription handling is temporarily disabled');

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

// Temporarily disabled to fix Firebase build error
/*
async function handleSubscriptionChange(subscription: Stripe.Subscription) {
  try {
    // Get customer details
    const customer = await stripe.customers.retrieve(subscription.customer as string);
    
    if (!customer || customer.deleted) {
      console.error('Customer not found for subscription:', subscription.id);
      return;
    }

    const customerEmail = (customer as Stripe.Customer).email;
    if (!customerEmail) {
      console.error('Customer email not found for subscription:', subscription.id);
      return;
    }

    // Find user in database
    const user = await prisma.user.findUnique({
      where: { email: customerEmail }
    });

    if (!user) {
      console.error('User not found for email:', customerEmail);
      return;
    }

    // Update subscription in database
    await prisma.subscription.upsert({
      where: {
        stripeSubscriptionId: subscription.id
      },
      update: {
        status: subscription.status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        updatedAt: new Date()
      },
      create: {
        userId: user.id,
        stripeCustomerId: subscription.customer as string,
        stripeSubscriptionId: subscription.id,
        status: subscription.status,
        planType: getPlanTypeFromSubscription(subscription),
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000)
      }
    });

    // Update RustDesk access based on subscription status
    const isActive = ['active', 'past_due'].includes(subscription.status);

    if (isActive) {
      // Enable user and devices in RustDesk Pro
      await enableRustDeskAccess(user.id, customerEmail);
      console.log(`✅ Activated RustDesk access for ${customerEmail}`);
    } else {
      // Disable user and devices in RustDesk Pro (instant cutoff)
      await disableRustDeskAccess(user.id, customerEmail);
      console.log(`❌ Deactivated RustDesk access for ${customerEmail}`);
    }

  } catch (error) {
    console.error('Error handling subscription change:', error);
    throw error;
  }
}

async function handleSubscriptionCancellation(subscription: Stripe.Subscription) {
  try {
    // Get customer details
    const customer = await stripe.customers.retrieve(subscription.customer as string);
    
    if (!customer || customer.deleted) {
      console.error('Customer not found for subscription:', subscription.id);
      return;
    }

    const customerEmail = (customer as Stripe.Customer).email;
    if (!customerEmail) {
      console.error('Customer email not found for subscription:', subscription.id);
      return;
    }

    // Find user in database
    const user = await prisma.user.findUnique({
      where: { email: customerEmail }
    });

    if (!user) {
      console.error('User not found for email:', customerEmail);
      return;
    }

    // Update subscription status
    await prisma.subscription.updateMany({
      where: {
        stripeSubscriptionId: subscription.id
      },
      data: {
        status: 'canceled',
        updatedAt: new Date()
      }
    });

    // Deactivate RustDesk access (temporarily disabled)
    // await rustDeskSubscriptionManager.onSubscriptionDeactivated(user.id);
    console.log(`Would deactivate RustDesk access for ${customerEmail} due to cancellation (disabled for build)`);

  } catch (error) {
    console.error('Error handling subscription cancellation:', error);
    throw error;
  }
}

async function handlePaymentSuccess(invoice: Stripe.Invoice) {
  try {
    if (!invoice.subscription) {
      return; // Not a subscription payment
    }

    // Get customer details
    const customer = await stripe.customers.retrieve(invoice.customer as string);
    
    if (!customer || customer.deleted) {
      console.error('Customer not found for invoice:', invoice.id);
      return;
    }

    const customerEmail = (customer as Stripe.Customer).email;
    if (!customerEmail) {
      console.error('Customer email not found for invoice:', invoice.id);
      return;
    }

    // Find user in database
    const user = await prisma.user.findUnique({
      where: { email: customerEmail }
    });

    if (!user) {
      console.error('User not found for email:', customerEmail);
      return;
    }

    // Activate RustDesk access on successful payment (temporarily disabled)
    // await rustDeskSubscriptionManager.onSubscriptionActivated(user.id);
    console.log(`Would activate RustDesk access for ${customerEmail} after successful payment (disabled for build)`);

  } catch (error) {
    console.error('Error handling payment success:', error);
    throw error;
  }
}

async function handlePaymentFailure(invoice: Stripe.Invoice) {
  try {
    if (!invoice.subscription) {
      return; // Not a subscription payment
    }

    // Get customer details
    const customer = await stripe.customers.retrieve(invoice.customer as string);
    
    if (!customer || customer.deleted) {
      console.error('Customer not found for invoice:', invoice.id);
      return;
    }

    const customerEmail = (customer as Stripe.Customer).email;
    if (!customerEmail) {
      console.error('Customer email not found for invoice:', invoice.id);
      return;
    }

    // Find user in database
    const user = await prisma.user.findUnique({
      where: { email: customerEmail }
    });

    if (!user) {
      console.error('User not found for email:', customerEmail);
      return;
    }

    // For now, we'll be lenient and not immediately disable access on first payment failure
    // The subscription status will handle this through the subscription.updated webhook
    console.log(`Payment failed for ${customerEmail}, monitoring subscription status`);

  } catch (error) {
    console.error('Error handling payment failure:', error);
    throw error;
  }
}

function getPlanTypeFromSubscription(subscription: Stripe.Subscription): string {
  // Extract plan type from subscription metadata or price
  if (subscription.metadata?.planType) {
    return subscription.metadata.planType;
  }

  // Default plan type based on price or items
  const item = subscription.items.data[0];
  if (item?.price?.metadata?.planType) {
    return item.price.metadata.planType;
  }

  // Fallback to generic plan name
  return 'Single User Plan';
}

// Instant RustDesk access control functions
async function enableRustDeskAccess(userId: string, email: string) {
  try {
    // Get user's RustDesk user ID
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { devices: true }
    });

    if (!user?.rustDeskUserId) {
      console.log(`Would create RustDesk user for ${email} (disabled for build)`);
      // await rustDeskSubscriptionManager.onSubscriptionActivated(userId);
      return;
    }

    // Enable user in RustDesk Pro via API
    const enableUserResponse = await fetch(
      `http://${process.env.RUSTDESK_SERVER_IP}:${process.env.RUSTDESK_API_PORT}/api/users/${user.rustDeskUserId}`,
      {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${process.env.RUSTDESK_API_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ enabled: true })
      }
    );

    if (enableUserResponse.ok) {
      console.log(`✅ Enabled RustDesk user: ${email}`);
    }

    // Enable all user's devices
    for (const device of user.devices) {
      if (device.rustDeskId) {
        const enableDeviceResponse = await fetch(
          `http://${process.env.RUSTDESK_SERVER_IP}:${process.env.RUSTDESK_API_PORT}/api/devices/${device.rustDeskId}`,
          {
            method: 'PATCH',
            headers: {
              'Authorization': `Bearer ${process.env.RUSTDESK_API_TOKEN}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enabled: true })
          }
        );

        if (enableDeviceResponse.ok) {
          console.log(`✅ Enabled device: ${device.rustDeskId}`);
        }
      }
    }

  } catch (error) {
    console.error(`Failed to enable RustDesk access for ${email}:`, error);
  }
}

async function disableRustDeskAccess(userId: string, email: string) {
  try {
    // Get user's RustDesk user ID and devices
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { devices: true }
    });

    if (!user?.rustDeskUserId) {
      console.log(`No RustDesk user found for ${email}`);
      return;
    }

    // INSTANT CUTOFF: Disable user in RustDesk Pro via API
    const disableUserResponse = await fetch(
      `http://${process.env.RUSTDESK_SERVER_IP}:${process.env.RUSTDESK_API_PORT}/api/users/${user.rustDeskUserId}`,
      {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${process.env.RUSTDESK_API_TOKEN}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ enabled: false })
      }
    );

    if (disableUserResponse.ok) {
      console.log(`❌ Disabled RustDesk user: ${email} (INSTANT CUTOFF)`);
    }

    // Disable all user's devices (belt and suspenders)
    for (const device of user.devices) {
      if (device.rustDeskId) {
        const disableDeviceResponse = await fetch(
          `http://${process.env.RUSTDESK_SERVER_IP}:${process.env.RUSTDESK_API_PORT}/api/devices/${device.rustDeskId}`,
          {
            method: 'PATCH',
            headers: {
              'Authorization': `Bearer ${process.env.RUSTDESK_API_TOKEN}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enabled: false })
          }
        );

        if (disableDeviceResponse.ok) {
          console.log(`❌ Disabled device: ${device.rustDeskId}`);
        }
      }
    }

    // Log the cutoff for audit purposes
    console.log(`🔒 PAYMENT CUTOFF: ${email} - All access disabled due to subscription status`);

  } catch (error) {
    console.error(`Failed to disable RustDesk access for ${email}:`, error);
  }
}
*/
