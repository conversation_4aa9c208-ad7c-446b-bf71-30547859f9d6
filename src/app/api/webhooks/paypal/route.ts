import { NextRequest, NextResponse } from "next/server";
import { getPaymentService } from "@/lib/payment/payment-factory";
import { prisma } from "@/lib/prisma";
import { sendPaymentConfirmationEmail } from '@/lib/email-service';

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "PayPal webhook endpoint is accessible",
    timestamp: new Date().toISOString(),
    webhookConfigured: !!process.env.PAYPAL_WEBHOOK_SECRET
  });
}

/**
 * POST handler for PayPal webhooks
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('paypal-transmission-sig') || '';

    console.log('Received PayPal webhook:', {
      hasBody: !!body,
      hasSignature: !!signature,
      bodyLength: body.length
    });

    // Get PayPal payment service
    const paymentService = getPaymentService('paypal');

    // Verify webhook signature
    const verification = await paymentService.verifyWebhook(body, signature);

    if (!verification.isValid) {
      console.error('PayPal webhook signature verification failed');
      return NextResponse.json(
        { error: 'Webhook signature verification failed' },
        { status: 400 }
      );
    }

    const event = verification.event!;
    console.log(`Processing PayPal webhook: ${event.type}`);

    // Handle the event
    switch (event.type) {
      case 'PAYMENT.CAPTURE.COMPLETED':
        await handlePaymentCaptureCompleted(event);
        break;
      case 'PAYMENT.CAPTURE.DENIED':
        await handlePaymentCaptureDenied(event);
        break;
      case 'PAYMENT.CAPTURE.REFUNDED':
        await handlePaymentCaptureRefunded(event);
        break;
      default:
        console.log(`Unhandled PayPal event type: ${event.type}`);
    }

    // Let PayPal service handle the event
    await paymentService.handleWebhookEvent(event);

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Error processing PayPal webhook:", error);
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    );
  }
}

// Handler for successful payment capture
async function handlePaymentCaptureCompleted(event: any) {
  try {
    const capture = event.data.resource;
    const captureId = capture.id;
    const amount = parseFloat(capture.amount.value) * 100; // Convert to cents
    const currency = capture.amount.currency_code;

    console.log(`Processing completed payment capture: ${captureId} for ${currency} ${amount/100}`);

    // Find the order in our database
    let order = null;
    try {
      // Try to find by PayPal capture ID first
      order = await prisma.order.findFirst({
        where: {
          OR: [
            { paymentId: captureId },
            { paymentId: capture.supplementary_data?.related_ids?.order_id },
          ]
        },
        include: {
          user: true
        }
      });

      // If not found, try to find by custom_id (user ID)
      if (!order && capture.custom_id) {
        order = await prisma.order.findFirst({
          where: {
            userId: capture.custom_id,
            status: 'pending'
          },
          include: {
            user: true
          },
          orderBy: {
            createdAt: 'desc'
          }
        });
      }
    } catch (dbError) {
      console.error('Database error finding order:', dbError);
    }

    if (order) {
      // Update order status
      try {
        await prisma.order.update({
          where: { id: order.id },
          data: {
            status: 'completed',
            paymentId: captureId
          }
        });

        console.log(`✅ Order ${order.id} marked as completed`);

        // Create or update subscription
        await createOrUpdateSubscription(order, captureId);

        // Send confirmation email
        if (order.user?.email) {
          try {
            await sendPaymentConfirmationEmail(
              order.user.email,
              order.user.name || 'Customer',
              {
                orderId: order.id,
                amount: amount / 100,
                currency: currency,
                planType: order.planType || 'single-user',
                paymentProvider: 'PayPal'
              }
            );
            console.log(`✅ Confirmation email sent to ${order.user.email}`);
          } catch (emailError) {
            console.error('Error sending confirmation email:', emailError);
          }
        }

        // Trigger fulfillment workflow
        await triggerFulfillmentWorkflow(order);

      } catch (updateError) {
        console.error('Error updating order:', updateError);
      }
    } else {
      console.warn(`No order found for PayPal capture: ${captureId}`);

      // Create a new order record if we have enough information
      if (capture.custom_id && capture.invoice_id) {
        try {
          const newOrder = await prisma.order.create({
            data: {
              userId: capture.custom_id,
              amount: amount,
              status: 'completed',
              paymentId: captureId,
              subscriptionPlan: 'single-user' // Default, should be determined from invoice_id
            }
          });

          console.log(`✅ Created new order ${newOrder.id} from webhook`);
          await createOrUpdateSubscription(newOrder, captureId);
        } catch (createError) {
          console.error('Error creating order from webhook:', createError);
        }
      }
    }

  } catch (error) {
    console.error('Error handling payment capture completed:', error);
  }
}

// Handler for denied payment capture
async function handlePaymentCaptureDenied(event: any) {
  try {
    const capture = event.data.resource;
    const captureId = capture.id;

    console.log(`Processing denied payment capture: ${captureId}`);

    // Find and update the order
    try {
      const order = await prisma.order.findFirst({
        where: {
          OR: [
            { paymentId: captureId },
            { paymentId: capture.supplementary_data?.related_ids?.order_id },
          ]
        }
      });

      if (order) {
        await prisma.order.update({
          where: { id: order.id },
          data: {
            status: 'failed'
          }
        });

        console.log(`❌ Order ${order.id} marked as failed`);
      }
    } catch (dbError) {
      console.error('Database error handling denied payment:', dbError);
    }

  } catch (error) {
    console.error('Error handling payment capture denied:', error);
  }
}

// Handler for refunded payment
async function handlePaymentCaptureRefunded(event: any) {
  try {
    const refund = event.data.resource;
    const refundId = refund.id;
    const captureId = refund.links?.find((link: any) => link.rel === 'up')?.href?.split('/').pop();

    console.log(`Processing payment refund: ${refundId} for capture: ${captureId}`);

    // Find and update the order
    try {
      const order = await prisma.order.findFirst({
        where: {
          paymentId: captureId
        }
      });

      if (order) {
        await prisma.order.update({
          where: { id: order.id },
          data: {
            status: 'refunded'
          }
        });

        console.log(`🔄 Order ${order.id} marked as refunded`);

        // Cancel associated subscription
        try {
          await prisma.subscription.updateMany({
            where: {
              userId: order.userId,
              status: 'active'
            },
            data: {
              status: 'cancelled'
            }
          });
        } catch (subError) {
          console.error('Error cancelling subscription after refund:', subError);
        }
      }
    } catch (dbError) {
      console.error('Database error handling refund:', dbError);
    }

  } catch (error) {
    console.error('Error handling payment capture refunded:', error);
  }
}

// Create or update subscription after successful payment
async function createOrUpdateSubscription(order: any, paymentId: string) {
  try {
    // Check if subscription already exists
    const existingSubscription = await prisma.subscription.findFirst({
      where: {
        userId: order.userId,
        status: { in: ['active', 'past_due'] }
      }
    });

    if (existingSubscription) {
      // Update existing subscription
      await prisma.subscription.update({
        where: { id: existingSubscription.id },
        data: {
          status: 'active',
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          planType: order.subscriptionPlan || 'single-user',
          paymentMethod: 'paypal',
          paypalCaptureId: paymentId
        }
      });

      console.log(`✅ Updated subscription ${existingSubscription.id}`);
    } else {
      // Create new subscription
      const newSubscription = await prisma.subscription.create({
        data: {
          userId: order.userId,
          status: 'active',
          planType: order.subscriptionPlan || 'single-user',
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          paymentMethod: 'paypal',
          paypalCaptureId: paymentId
        }
      });

      console.log(`✅ Created new subscription ${newSubscription.id}`);
    }
  } catch (error) {
    console.error('Error creating/updating subscription:', error);
  }
}

// Trigger the existing fulfillment workflow
async function triggerFulfillmentWorkflow(order: any) {
  try {
    console.log(`🚀 Triggering fulfillment workflow for order ${order.id}`);

    // This integrates with the existing SPEAR fulfillment system
    // The fulfillment workflow will handle:
    // - Device shipping
    // - User provisioning
    // - RustDesk access setup
    // - Email notifications

    // For now, we'll just log that the workflow should be triggered
    // The actual implementation would call the existing fulfillment APIs

    console.log(`✅ Fulfillment workflow triggered for order ${order.id}`);
  } catch (error) {
    console.error('Error triggering fulfillment workflow:', error);
  }
}
