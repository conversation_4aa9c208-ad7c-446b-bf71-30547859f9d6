import { NextResponse } from "next/server";
import { exec } from "child_process";
import { promisify } from "util";

const execAsync = promisify(exec);

export async function POST() {
  try {
    console.log('Setting up database schema...');
    
    // Use db push to create tables without migrations
    const { stdout, stderr } = await execAsync('npx prisma db push --force-reset');
    
    console.log('DB Push stdout:', stdout);
    if (stderr) {
      console.log('DB Push stderr:', stderr);
    }
    
    return NextResponse.json({
      success: true,
      message: "Database schema setup completed successfully",
      output: stdout,
      errors: stderr || null,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('DB setup failed:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Database setup failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Use POST method to setup database schema",
    endpoint: "/api/setup-db",
    method: "POST",
    description: "This will create all tables in the Supabase database based on the Prisma schema"
  });
}
