import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { getPaymentService } from "@/lib/payment/payment-factory";

/**
 * Admin Subscription Management API
 * GET: List all subscriptions with payment status
 * POST: Update subscription status or cancel subscription
 */

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await auth();
    if (!session || !session.user || session.user.email !== "<EMAIL>") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status'); // Filter by status
    const userId = searchParams.get('userId'); // Filter by user
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build filter conditions
    const whereConditions: any = {};
    if (userId) {
      whereConditions.userId = userId;
    }

    // Get orders (which represent subscriptions in our system)
    const orders = await prisma.order.findMany({
      where: whereConditions,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            createdAt: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    });

    // Get payment service for status checking
    const paymentService = getPaymentService('paypal');

    // Check subscription status for each order
    const subscriptionsWithStatus = await Promise.all(
      orders.map(async (order) => {
        let subscriptionStatus = 'unknown';
        let paymentStatus = 'unknown';
        let nextBillingDate = null;
        let deviceAccess = 'unknown';

        try {
          // Check PayPal payment status
          if (order.paymentId) {
            const paymentResult = await paymentService.getPaymentStatus(order.paymentId);
            paymentStatus = paymentResult.status;
            
            // Determine subscription status
            if (paymentResult.success && paymentResult.status === 'completed') {
              subscriptionStatus = 'active';
              // Calculate next billing date (30 days from order creation)
              nextBillingDate = new Date(order.createdAt.getTime() + 30 * 24 * 60 * 60 * 1000);
              
              // Check if subscription is still valid (within 30 days)
              const now = new Date();
              if (now > nextBillingDate) {
                subscriptionStatus = 'past_due';
                deviceAccess = 'revoked';
              } else {
                deviceAccess = 'active';
              }
            } else {
              subscriptionStatus = 'unpaid';
              deviceAccess = 'revoked';
            }
          }
        } catch (error) {
          console.error(`Error checking subscription status for order ${order.id}:`, error);
        }

        return {
          id: order.id,
          userId: order.userId,
          user: order.user,
          subscriptionPlan: order.subscriptionPlan,
          amount: order.amount,
          currency: order.currency,
          paymentId: order.paymentId,
          status: order.status,
          subscriptionStatus,
          paymentStatus,
          deviceAccess,
          nextBillingDate,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
          notes: order.notes ? JSON.parse(order.notes) : null
        };
      })
    );

    // Filter by status if requested
    const filteredSubscriptions = status 
      ? subscriptionsWithStatus.filter(sub => sub.subscriptionStatus === status)
      : subscriptionsWithStatus;

    // Get summary statistics
    const totalSubscriptions = filteredSubscriptions.length;
    const activeSubscriptions = filteredSubscriptions.filter(sub => sub.subscriptionStatus === 'active').length;
    const pastDueSubscriptions = filteredSubscriptions.filter(sub => sub.subscriptionStatus === 'past_due').length;
    const unpaidSubscriptions = filteredSubscriptions.filter(sub => sub.subscriptionStatus === 'unpaid').length;

    return NextResponse.json({
      success: true,
      subscriptions: filteredSubscriptions,
      summary: {
        total: totalSubscriptions,
        active: activeSubscriptions,
        pastDue: pastDueSubscriptions,
        unpaid: unpaidSubscriptions
      },
      pagination: {
        limit,
        offset,
        total: totalSubscriptions
      }
    });

  } catch (error) {
    console.error('Error fetching admin subscriptions:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await auth();
    if (!session || !session.user || session.user.email !== "<EMAIL>") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, subscriptionId, userId, reason } = body;

    if (!action || !subscriptionId) {
      return NextResponse.json(
        { error: "Action and subscriptionId are required" },
        { status: 400 }
      );
    }

    const paymentService = getPaymentService('paypal');

    switch (action) {
      case 'cancel':
        // Cancel subscription
        const cancelResult = await paymentService.cancelSubscription(subscriptionId);
        
        if (cancelResult.success) {
          // Update order status in database
          await prisma.order.update({
            where: { id: subscriptionId },
            data: {
              status: 'cancelled',
              notes: JSON.stringify({
                cancelledAt: new Date().toISOString(),
                cancelledBy: 'admin',
                reason: reason || 'admin_action'
              })
            }
          });

          return NextResponse.json({
            success: true,
            message: 'Subscription cancelled successfully',
            subscription: cancelResult
          });
        } else {
          return NextResponse.json(
            { error: cancelResult.error || 'Failed to cancel subscription' },
            { status: 400 }
          );
        }

      case 'reactivate':
        // Reactivate subscription (update database status)
        await prisma.order.update({
          where: { id: subscriptionId },
          data: {
            status: 'device_prep',
            notes: JSON.stringify({
              reactivatedAt: new Date().toISOString(),
              reactivatedBy: 'admin',
              reason: reason || 'admin_reactivation'
            })
          }
        });

        return NextResponse.json({
          success: true,
          message: 'Subscription reactivated successfully'
        });

      case 'check_status':
        // Check current subscription status
        const statusResult = await paymentService.getSubscriptionStatus(subscriptionId);
        
        return NextResponse.json({
          success: true,
          subscription: statusResult
        });

      default:
        return NextResponse.json(
          { error: "Invalid action. Supported actions: cancel, reactivate, check_status" },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error managing subscription:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
