import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

/**
 * GET handler for fetching a specific coupon (admin only)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const couponId = params.id;

    // Get the coupon
    const coupon = await prisma.coupon.findUnique({
      where: { id: couponId },
    });

    if (!coupon) {
      return NextResponse.json(
        { error: "Coupon not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(coupon);
  } catch (error) {
    console.error("Error fetching coupon:", error);
    return NextResponse.json(
      { error: "Failed to fetch coupon" },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a coupon (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const couponId = params.id;

    // Parse request body
    const body = await request.json();
    const {
      code,
      discountType,
      discountValue,
      maxUses,
      validFrom,
      validTo,
      applicablePlans,
      isActive,
    } = body;

    // Validate discount type if provided
    if (discountType && !["percentage", "fixed"].includes(discountType)) {
      return NextResponse.json(
        { error: "Discount type must be 'percentage' or 'fixed'" },
        { status: 400 }
      );
    }

    // Validate discount value if provided
    if (discountValue !== undefined) {
      if (discountType === "percentage" && (discountValue < 0 || discountValue > 100)) {
        return NextResponse.json(
          { error: "Percentage discount must be between 0 and 100" },
          { status: 400 }
        );
      }

      if (discountType === "fixed" && discountValue < 0) {
        return NextResponse.json(
          { error: "Fixed discount must be a positive number" },
          { status: 400 }
        );
      }
    }

    // Update the coupon
    const updatedCoupon = await prisma.coupon.update({
      where: { id: couponId },
      data: {
        ...(code && { code: code.toUpperCase() }),
        ...(discountType && { discountType }),
        ...(discountValue !== undefined && { discountValue }),
        ...(maxUses !== undefined && { maxUses: maxUses || null }),
        ...(validFrom && { validFrom: new Date(validFrom) }),
        ...(validTo !== undefined && { validTo: validTo ? new Date(validTo) : null }),
        ...(applicablePlans && { applicablePlans }),
        ...(isActive !== undefined && { isActive }),
      },
    });

    return NextResponse.json(updatedCoupon);
  } catch (error) {
    console.error("Error updating coupon:", error);
    
    // Handle unique constraint violation
    if (error instanceof Error && error.message.includes("Unique constraint")) {
      return NextResponse.json(
        { error: "A coupon with this code already exists" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update coupon" },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a coupon (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the current session
    const session = await auth();

    // Check if user is authenticated and is admin
    if (!session || !session.user || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const couponId = params.id;

    // Delete the coupon
    await prisma.coupon.delete({
      where: { id: couponId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting coupon:", error);
    return NextResponse.json(
      { error: "Failed to delete coupon" },
      { status: 500 }
    );
  }
}
