import { NextResponse } from "next/server";
import { prisma } from "@/lib/db";

export async function GET() {
  try {
    // Test database connection
    const result = await prisma.$queryRaw`SELECT 1 as test`;

    // Test all tables and count records
    const userCount = await prisma.user.count();
    const deviceCount = await prisma.device.count();
    const subscriptionCount = await prisma.subscription.count();
    const paymentMethodCount = await prisma.paymentMethod.count();
    const invoiceCount = await prisma.invoice.count();
    const settingCount = await prisma.setting.count();

    // Get sample data
    const users = await prisma.user.findMany({
      select: { id: true, email: true, name: true, role: true }
    });

    const devices = await prisma.device.findMany({
      select: { id: true, name: true, rustDeskId: true, status: true, userId: true }
    });

    const settings = await prisma.setting.findMany({
      select: { key: true, value: true, type: true }
    });

    return NextResponse.json({
      success: true,
      message: "Database connection successful",
      data: {
        connectionTest: result,
        counts: {
          users: userCount,
          devices: deviceCount,
          subscriptions: subscriptionCount,
          paymentMethods: paymentMethodCount,
          invoices: invoiceCount,
          settings: settingCount
        },
        sampleData: {
          users,
          devices,
          settings
        },
        databaseUrl: process.env.DATABASE_URL ? process.env.DATABASE_URL.substring(0, 80) + '...' : 'missing',
        databaseHost: process.env.DATABASE_URL ? new URL(process.env.DATABASE_URL).hostname : 'missing',
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Database connection test failed:', error);

    return NextResponse.json({
      success: false,
      error: 'Database connection failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      databaseUrl: process.env.DATABASE_URL ? process.env.DATABASE_URL.substring(0, 80) + '...' : 'missing',
      databaseHost: process.env.DATABASE_URL ? new URL(process.env.DATABASE_URL).hostname : 'missing',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
