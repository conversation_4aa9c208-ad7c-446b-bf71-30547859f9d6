import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import jwt from 'jsonwebtoken';

// EMERGENCY ADMIN ACCESS API
// This creates a temporary admin session without database access

const EMERGENCY_PASSWORD = 'SpearEmergencyAccess2024!';
const JWT_SECRET = process.env.NEXTAUTH_SECRET || 'emergency-secret';

export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json();

    // Verify emergency password
    if (password !== EMERGENCY_PASSWORD) {
      return NextResponse.json(
        { error: 'Invalid emergency password' },
        { status: 401 }
      );
    }

    // Create emergency admin session data
    const adminUser = {
      id: 'emergency-admin',
      email: '<EMAIL>',
      name: '<PERSON><PERSON>',
      role: 'ADMIN'
    };

    // Create JWT token for the session
    const token = jwt.sign(
      {
        user: adminUser,
        exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24), // 24 hours
        emergency: true
      },
      JWT_SECRET
    );

    // Set session cookie manually
    const cookieStore = cookies();
    
    // Create NextAuth-compatible session cookie
    const sessionToken = `next-auth.session-token=${token}; Path=/; HttpOnly; SameSite=Lax; ${process.env.NODE_ENV === 'production' ? 'Secure;' : ''} Max-Age=86400`;

    const response = NextResponse.json({ 
      success: true, 
      message: 'Emergency admin access granted',
      user: adminUser
    });

    // Set the session cookie
    response.headers.set('Set-Cookie', sessionToken);

    console.log('🚨 Emergency admin access granted');
    
    return response;

  } catch (error) {
    console.error('Emergency admin access error:', error);
    return NextResponse.json(
      { error: 'Emergency access failed' },
      { status: 500 }
    );
  }
}
