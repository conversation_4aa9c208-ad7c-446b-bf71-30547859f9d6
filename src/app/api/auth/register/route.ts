import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/db";
import bcrypt from "bcrypt";
import { createNewUserSignupNotification } from "@/lib/notifications";

/**
 * Hash a password using bcrypt
 * This provides more secure password storage with salt
 */
async function hashPassword(password: string): Promise<string> {
  // Use bcrypt to hash the password with a salt (10 rounds)
  return await bcrypt.hash(password, 10);
}

/**
 * API route to register a new user
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { name, email, password } = body;

    // Validate required fields
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: "Name, email, and password are required" },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: {
        email,
      },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 409 }
      );
    }

    // Hash the password
    const hashedPassword = await hashPassword(password);

    // Create the user
    const user = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: "CLIENT", // Default role for new registrations
      },
    });

    // Create notification for admins about new user signup
    try {
      await createNewUserSignupNotification({
        name: user.name || "Unknown",
        email: user.email,
        role: user.role,
      });
    } catch (notificationError) {
      // Log the error but don't fail the registration
      console.error("Failed to create signup notification:", notificationError);
    }

    // Return success response (excluding the password)
    return NextResponse.json({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
    });
  } catch (error) {
    console.error("Error registering user:", error);
    return NextResponse.json(
      { error: "Failed to register user" },
      { status: 500 }
    );
  }
}
