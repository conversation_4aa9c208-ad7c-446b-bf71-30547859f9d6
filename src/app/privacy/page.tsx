import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MetaTags } from "@/components/seo/meta-tags";

export default function PrivacyPage() {
  return (
    <>
      <MetaTags
        title="Privacy Policy - SPEAR Platform"
        description="SPEAR's privacy policy outlines how we collect, use, and protect your personal information and data."
        keywords="privacy policy, data protection, GDPR, CCPA, personal information"
        ogType="website"
      />

      <div className="flex flex-col min-h-screen">
        {/* Navigation Header */}
        <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <Link href="/" className="flex items-center space-x-2">
                <Image
                  src="/images/spear-logo.PNG"
                  alt="SPEAR Logo"
                  width={32}
                  height={32}
                  className="object-contain"
                />
                <span className="text-xl font-bold">SPEAR</span>
              </Link>
              <nav className="hidden md:flex items-center space-x-6">
                <Link href="/pricing" className="text-sm font-medium hover:text-primary">
                  Pricing
                </Link>
                <Link href="/faq" className="text-sm font-medium hover:text-primary">
                  FAQ
                </Link>
                <Link href="/blog" className="text-sm font-medium hover:text-primary">
                  Blog
                </Link>
                <Link href="/contact" className="text-sm font-medium hover:text-primary">
                  Contact
                </Link>
              </nav>
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/login">Sign In</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/pricing">Get Started</Link>
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary-foreground/5 z-0"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Privacy Policy
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground mb-8">
                Your privacy is important to us. Learn how we collect, use, and protect your information.
              </p>
              <p className="text-sm text-muted-foreground">
                Last updated: {new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
              </p>
            </div>
          </div>
        </section>

        {/* Privacy Policy Content */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="space-y-8">
                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">1. Information We Collect</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>
                        We collect information you provide directly to us, such as when you create an account, 
                        subscribe to our service, or contact us for support.
                      </p>
                      <h3 className="text-lg font-semibold text-foreground">Personal Information:</h3>
                      <ul className="list-disc list-inside space-y-2 ml-4">
                        <li>Name and contact information (email address, phone number)</li>
                        <li>Billing and payment information</li>
                        <li>Account credentials and preferences</li>
                        <li>Communications with our support team</li>
                      </ul>
                      <h3 className="text-lg font-semibold text-foreground">Usage Information:</h3>
                      <ul className="list-disc list-inside space-y-2 ml-4">
                        <li>Device information and connection logs</li>
                        <li>Service usage patterns and performance data</li>
                        <li>IP addresses and browser information</li>
                        <li>Cookies and similar tracking technologies</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">2. How We Use Your Information</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>We use the information we collect to:</p>
                      <ul className="list-disc list-inside space-y-2 ml-4">
                        <li>Provide, maintain, and improve our services</li>
                        <li>Process transactions and send related information</li>
                        <li>Send technical notices, updates, and support messages</li>
                        <li>Respond to your comments, questions, and customer service requests</li>
                        <li>Monitor and analyze trends, usage, and activities</li>
                        <li>Detect, investigate, and prevent fraudulent transactions</li>
                        <li>Comply with legal obligations and protect our rights</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">3. Information Sharing and Disclosure</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>
                        We do not sell, trade, or otherwise transfer your personal information to third parties 
                        except as described in this policy:
                      </p>
                      <h3 className="text-lg font-semibold text-foreground">Service Providers:</h3>
                      <p>
                        We may share your information with third-party service providers who perform services 
                        on our behalf, such as payment processing, data analysis, and customer support.
                      </p>
                      <h3 className="text-lg font-semibold text-foreground">Legal Requirements:</h3>
                      <p>
                        We may disclose your information if required by law or in response to valid requests 
                        by public authorities.
                      </p>
                      <h3 className="text-lg font-semibold text-foreground">Business Transfers:</h3>
                      <p>
                        In the event of a merger, acquisition, or sale of assets, your information may be 
                        transferred as part of that transaction.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">4. Data Security</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>
                        We implement appropriate technical and organizational measures to protect your personal 
                        information against unauthorized access, alteration, disclosure, or destruction.
                      </p>
                      <ul className="list-disc list-inside space-y-2 ml-4">
                        <li>End-to-end encryption for all data transmissions</li>
                        <li>Secure data centers with physical access controls</li>
                        <li>Regular security audits and penetration testing</li>
                        <li>Employee training on data protection practices</li>
                        <li>Multi-factor authentication for administrative access</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">5. Your Rights and Choices</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>You have the following rights regarding your personal information:</p>
                      <ul className="list-disc list-inside space-y-2 ml-4">
                        <li><strong>Access:</strong> Request a copy of the personal information we hold about you</li>
                        <li><strong>Correction:</strong> Request correction of inaccurate or incomplete information</li>
                        <li><strong>Deletion:</strong> Request deletion of your personal information</li>
                        <li><strong>Portability:</strong> Request transfer of your data to another service</li>
                        <li><strong>Objection:</strong> Object to processing of your personal information</li>
                        <li><strong>Restriction:</strong> Request restriction of processing in certain circumstances</li>
                      </ul>
                      <p className="mt-4">
                        To exercise these rights, please contact us at{' '}
                        <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                          <EMAIL>
                        </a>
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">6. Cookies and Tracking Technologies</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>
                        We use cookies and similar tracking technologies to collect and use personal information 
                        about you. You can control cookies through your browser settings.
                      </p>
                      <h3 className="text-lg font-semibold text-foreground">Types of Cookies:</h3>
                      <ul className="list-disc list-inside space-y-2 ml-4">
                        <li><strong>Essential Cookies:</strong> Required for the service to function properly</li>
                        <li><strong>Analytics Cookies:</strong> Help us understand how you use our service</li>
                        <li><strong>Preference Cookies:</strong> Remember your settings and preferences</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">7. International Data Transfers</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>
                        Your information may be transferred to and processed in countries other than your own. 
                        We ensure appropriate safeguards are in place to protect your information in accordance 
                        with applicable data protection laws.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">8. Children's Privacy</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>
                        Our service is not intended for children under 13 years of age. We do not knowingly 
                        collect personal information from children under 13. If you become aware that a child 
                        has provided us with personal information, please contact us.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">9. Changes to This Privacy Policy</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>
                        We may update this privacy policy from time to time. We will notify you of any changes 
                        by posting the new privacy policy on this page and updating the "Last updated" date.
                      </p>
                      <p>
                        We encourage you to review this privacy policy periodically for any changes. Changes 
                        to this privacy policy are effective when they are posted on this page.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-4">10. Contact Us</h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p>
                        If you have any questions about this privacy policy or our privacy practices, 
                        please contact us:
                      </p>
                      <div className="bg-muted p-4 rounded-lg">
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Support:</strong> <EMAIL></p>
                        <p><strong>Website:</strong> spear-global.com</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* CTA */}
              <div className="mt-12 text-center">
                <h3 className="text-2xl font-bold mb-4">Questions About Our Privacy Policy?</h3>
                <p className="text-muted-foreground mb-6">
                  Our team is here to help you understand how we protect your privacy.
                </p>
                <Button asChild>
                  <Link href="/contact">Contact Us</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-muted py-12 mt-auto">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-6 md:mb-0">
                <div className="flex items-center">
                  <Image
                    src="/images/spear-logo.PNG"
                    alt="SPEAR Logo"
                    width={40}
                    height={40}
                    className="mr-2"
                  />
                  <span className="text-xl font-bold">SPEAR</span>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Secure Platform for Extended Augmented Reality
                </p>
              </div>
              <div className="flex flex-wrap gap-6">
                <Link href="/pricing" className="text-sm hover:text-primary">Pricing</Link>
                <Link href="/about" className="text-sm hover:text-primary">About</Link>
                <Link href="/blog" className="text-sm hover:text-primary">Blog</Link>
                <Link href="/faq" className="text-sm hover:text-primary">FAQ</Link>
                <Link href="/contact" className="text-sm hover:text-primary">Contact</Link>
                <Link href="/privacy" className="text-sm hover:text-primary">Privacy</Link>
                <Link href="/terms" className="text-sm hover:text-primary">Terms</Link>
              </div>
            </div>
            <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} SPEAR Platform. All rights reserved.
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
