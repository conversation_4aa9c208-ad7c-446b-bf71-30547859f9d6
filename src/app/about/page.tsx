import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MetaTags } from "@/components/seo/meta-tags";
import {
  DevicePhoneMobileIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  LightBulbIcon,
  HeartIcon,
  GlobeAltIcon,
} from "@heroicons/react/24/outline";

export default function AboutPage() {
  return (
    <>
      <MetaTags
        title="About SPEAR - Secure Remote Access Solutions"
        description="Learn about SPEAR's mission to provide secure, reliable remote device management solutions for businesses worldwide."
        keywords="about SPEAR, remote access, company mission, team, technology"
        ogType="website"
      />

      <div className="flex flex-col min-h-screen">
        {/* Navigation Header */}
        <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <Link href="/" className="flex items-center space-x-2">
                <Image
                  src="/images/spear-logo.PNG"
                  alt="SPEAR Logo"
                  width={32}
                  height={32}
                  className="object-contain"
                />
                <span className="text-xl font-bold">SPEAR</span>
              </Link>
              <nav className="hidden md:flex items-center space-x-6">
                <Link href="/pricing" className="text-sm font-medium hover:text-primary">
                  Pricing
                </Link>
                <Link href="/faq" className="text-sm font-medium hover:text-primary">
                  FAQ
                </Link>
                <Link href="/blog" className="text-sm font-medium hover:text-primary">
                  Blog
                </Link>
                <Link href="/contact" className="text-sm font-medium hover:text-primary">
                  Contact
                </Link>
              </nav>
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/login">Sign In</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/pricing">Get Started</Link>
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary-foreground/5 z-0"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                About SPEAR
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground mb-8">
                Revolutionizing remote device management with security, simplicity, and reliability at its core.
              </p>
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">Our Mission</h2>
                <p className="text-lg text-muted-foreground mb-6">
                  SPEAR was founded with a simple yet powerful vision: to make remote device access 
                  secure, reliable, and accessible to everyone. We believe that distance should never 
                  be a barrier to productivity, support, or connection.
                </p>
                <p className="text-lg text-muted-foreground mb-6">
                  Our platform combines enterprise-grade security with consumer-friendly simplicity, 
                  ensuring that whether you're a small business owner or managing a large enterprise, 
                  you have the tools you need to succeed in our increasingly connected world.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button asChild>
                    <Link href="/pricing">Start Your Journey</Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/contact">Contact Our Team</Link>
                  </Button>
                </div>
              </div>
              <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl bg-gradient-to-r from-slate-900 via-purple-950 to-slate-900 flex items-center justify-center">
                <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-30"></div>
                <Image
                  src="/images/spear-logo.PNG"
                  alt="SPEAR Mission"
                  width={200}
                  height={200}
                  className="object-contain z-10"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Values</h2>
              <p className="text-xl text-muted-foreground">
                The principles that guide everything we do at SPEAR
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <Card className="border shadow-sm">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                    <ShieldCheckIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Security First</h3>
                  <p className="text-muted-foreground">
                    We prioritize the security and privacy of your data above all else, 
                    implementing industry-leading encryption and security protocols.
                  </p>
                </CardContent>
              </Card>

              <Card className="border shadow-sm">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                    <LightBulbIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Innovation</h3>
                  <p className="text-muted-foreground">
                    We continuously innovate to stay ahead of technological advances 
                    and provide cutting-edge solutions for our customers.
                  </p>
                </CardContent>
              </Card>

              <Card className="border shadow-sm">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                    <HeartIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Customer Focus</h3>
                  <p className="text-muted-foreground">
                    Our customers are at the heart of everything we do. We listen, 
                    adapt, and evolve based on your needs and feedback.
                  </p>
                </CardContent>
              </Card>

              <Card className="border shadow-sm">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                    <UserGroupIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Collaboration</h3>
                  <p className="text-muted-foreground">
                    We believe in the power of teamwork and collaboration, 
                    both within our company and with our valued customers.
                  </p>
                </CardContent>
              </Card>

              <Card className="border shadow-sm">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                    <DevicePhoneMobileIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Reliability</h3>
                  <p className="text-muted-foreground">
                    We build robust, dependable solutions that you can count on 
                    when it matters most, with 99.9% uptime guarantee.
                  </p>
                </CardContent>
              </Card>

              <Card className="border shadow-sm">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                    <GlobeAltIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold mb-2">Global Impact</h3>
                  <p className="text-muted-foreground">
                    We're committed to making a positive impact worldwide by 
                    connecting people and enabling remote collaboration.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Story Section */}
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Story</h2>
                <p className="text-xl text-muted-foreground">
                  From concept to global platform
                </p>
              </div>

              <div className="space-y-8">
                <div className="bg-card rounded-lg p-8 border shadow-sm">
                  <h3 className="text-2xl font-bold mb-4">The Beginning</h3>
                  <p className="text-muted-foreground text-lg">
                    SPEAR was born from the recognition that existing remote access solutions 
                    were either too complex for small businesses or lacked the security features 
                    required by enterprises. We set out to bridge this gap with a platform that 
                    combines simplicity with enterprise-grade security.
                  </p>
                </div>

                <div className="bg-card rounded-lg p-8 border shadow-sm">
                  <h3 className="text-2xl font-bold mb-4">Innovation & Growth</h3>
                  <p className="text-muted-foreground text-lg">
                    Through continuous innovation and customer feedback, we've evolved SPEAR 
                    into a comprehensive remote device management platform. Our focus on 
                    user experience, security, and reliability has earned the trust of 
                    thousands of organizations worldwide.
                  </p>
                </div>

                <div className="bg-card rounded-lg p-8 border shadow-sm">
                  <h3 className="text-2xl font-bold mb-4">The Future</h3>
                  <p className="text-muted-foreground text-lg">
                    We're just getting started. With emerging technologies like AI, IoT, and 
                    edge computing, the future of remote device management is incredibly exciting. 
                    SPEAR is positioned to lead this transformation, continuing to innovate and 
                    adapt to meet the evolving needs of our customers.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Join the SPEAR Community
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                Be part of the future of remote device management. Start your journey with SPEAR today.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button size="lg" asChild>
                  <Link href="/pricing">View Pricing Plans</Link>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <Link href="/register">Create Account</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-muted py-12 mt-auto">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-6 md:mb-0">
                <div className="flex items-center">
                  <Image
                    src="/images/spear-logo.PNG"
                    alt="SPEAR Logo"
                    width={40}
                    height={40}
                    className="mr-2"
                  />
                  <span className="text-xl font-bold">SPEAR</span>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Secure Platform for Extended Augmented Reality
                </p>
              </div>
              <div className="flex flex-wrap gap-6">
                <Link href="/pricing" className="text-sm hover:text-primary">Pricing</Link>
                <Link href="/about" className="text-sm hover:text-primary">About</Link>
                <Link href="/blog" className="text-sm hover:text-primary">Blog</Link>
                <Link href="/faq" className="text-sm hover:text-primary">FAQ</Link>
                <Link href="/contact" className="text-sm hover:text-primary">Contact</Link>
                <Link href="/privacy" className="text-sm hover:text-primary">Privacy</Link>
                <Link href="/terms" className="text-sm hover:text-primary">Terms</Link>
              </div>
            </div>
            <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} SPEAR Platform. All rights reserved.
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
