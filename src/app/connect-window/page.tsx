"use client";

import { useState, useEffect, Suspense } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useSearchParams } from "next/navigation";

function ConnectWindowContent() {
  // Get search params
  const searchParams = useSearchParams();

  // State
  const [deviceId, setDeviceId] = useState(searchParams.get('id') || "1681512408");
  const [password, setPassword] = useState(searchParams.get('password') || "82AirmaN@$");
  const [isConnecting, setIsConnecting] = useState(false);
  const [statusMessage, setStatusMessage] = useState<{ text: string; type: 'default' | 'info' | 'success' | 'error' } | null>(null);
  const [showInstructions, setShowInstructions] = useState(false);

  // Auto-connect if URL parameters are present
  useEffect(() => {
    if (searchParams.get('id')) {
      handleConnect();
    }
  }, []);

  // Connect to device
  const handleConnect = () => {
    const trimmedDeviceId = deviceId.trim();

    if (!trimmedDeviceId) {
      setStatusMessage({ text: 'Please enter a device ID', type: 'error' });
      return;
    }

    // Start connection process
    setIsConnecting(true);
    setStatusMessage({ text: 'Opening RustDesk web client...', type: 'info' });

    // Build the connection URL
    let connectionUrl = `https://rustdesk.com/web/?id=${trimmedDeviceId}`;
    if (password) {
      connectionUrl += `&password=${encodeURIComponent(password)}`;
    }

    // Open in new window
    const rustDeskWindow = window.open(connectionUrl, '_blank');

    // Check if window was blocked
    if (!rustDeskWindow || rustDeskWindow.closed || typeof rustDeskWindow.closed === 'undefined') {
      setStatusMessage({ text: 'Popup blocked. Please allow popups and try again.', type: 'error' });
      setIsConnecting(false);
    } else {
      // Show instructions
      setShowInstructions(true);
      setIsConnecting(false);

      // Focus the new window
      rustDeskWindow.focus();
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Image
              src="/images/spear-logo.PNG"
              alt="Spear Logo"
              width={120}
              height={60}
              className="h-auto"
            />
          </div>
          <CardTitle>Remote Control</CardTitle>
        </CardHeader>
        <CardContent>
          {!showInstructions ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="deviceId">Device ID</Label>
                <Input
                  id="deviceId"
                  placeholder="Enter device ID"
                  value={deviceId}
                  onChange={(e) => setDeviceId(e.target.value)}
                  disabled={isConnecting}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Access Code</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter access code"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isConnecting}
                />
              </div>

              <Button
                onClick={handleConnect}
                disabled={isConnecting || !deviceId.trim()}
                className="w-full"
              >
                {isConnecting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Connecting...
                  </>
                ) : (
                  "Connect to Device"
                )}
              </Button>

              {statusMessage && (
                <Alert className={
                  statusMessage.type === 'error' ? 'bg-red-900/20 text-red-400 border-red-900/50' :
                  statusMessage.type === 'success' ? 'bg-green-900/20 text-green-400 border-green-900/50' :
                  statusMessage.type === 'info' ? 'bg-blue-900/20 text-blue-400 border-blue-900/50' :
                  'bg-slate-900/20 text-slate-400 border-slate-900/50'
                }>
                  <AlertDescription>
                    {statusMessage.text}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <Alert className="bg-blue-900/20 text-blue-400 border-blue-900/50">
                <AlertDescription>
                  <h3 className="font-semibold text-blue-300 mb-2">Connection Instructions</h3>
                  <ol className="list-decimal pl-5 space-y-2">
                    <li>A new window has opened with the RustDesk web client.</li>
                    <li>Your device ID and password have been pre-filled.</li>
                    <li>Click on your device icon (Android phone) to connect.</li>
                    <li>You should now see your device screen and can control it remotely.</li>
                  </ol>
                  <p className="mt-2">If the window was blocked, please allow pop-ups for this site and try again.</p>
                </AlertDescription>
              </Alert>

              <Button
                onClick={handleConnect}
                className="w-full"
              >
                Connect Again
              </Button>

              <Button
                onClick={() => setShowInstructions(false)}
                variant="outline"
                className="w-full"
              >
                Back to Connection Form
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Wrap the component in a Suspense boundary
export default function ConnectWindowPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </CardContent>
        </Card>
      </div>
    }>
      <ConnectWindowContent />
    </Suspense>
  );
}
