"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function DirectAdminAccess() {
  const router = useRouter();

  // Automatically redirect to admin dashboard after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      router.push("/admin");
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold">Direct Admin Access</h1>
            <p className="text-slate-500 dark:text-slate-400">
              Bypassing login system and redirecting to admin dashboard...
            </p>
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
            <div className="pt-4">
              <Button 
                onClick={() => router.push("/admin")}
                className="w-full"
              >
                Go to Admin Dashboard Now
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
