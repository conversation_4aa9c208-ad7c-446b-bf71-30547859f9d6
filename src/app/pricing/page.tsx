"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  originalPrice?: number; // For showing crossed-out pricing
  originalYearlyPrice?: number;
  savings?: number; // Monthly savings amount
  yearlySavings?: number;
  features: string[];
  limitations: string[];
  popular?: boolean;
  promotional?: boolean;
  couponCode?: string;
  bundleRequirement?: string;
  buttonText: string;
  buttonVariant: "default" | "outline";
}

const pricingPlans: PricingPlan[] = [
  {
    id: "single-user",
    name: "Single User",
    description: "Perfect for individual professionals who need reliable remote access",
    monthlyPrice: 199,
    yearlyPrice: 2400, // $200/month when paid annually
    originalPrice: 299,
    originalYearlyPrice: 3588, // $299 × 12
    savings: 100,
    yearlySavings: 1188,
    promotional: true,
    couponCode: "SPEARMINT",
    features: [
      "1 desktop device + 1 mobile device",
      "Mobile device support (Beta) - Pre-configured phones shipped to you",
      "Advanced remote control",
      "Priority email support",
      "Enhanced security features",
      "File transfer capabilities",
      "Session recording",
      "Custom branding",
      "API access",
    ],
    limitations: [
      "Single user license only",
    ],
    popular: true,
    buttonText: "Start Single User Plan",
    buttonVariant: "default",
  },
  {
    id: "two-user-bundle",
    name: "Two User Bundle",
    description: "Special bundle pricing for teams of two - both users must sign up simultaneously",
    monthlyPrice: 298, // $149 per user
    yearlyPrice: 3576, // $149 × 2 × 12
    originalPrice: 598, // $299 × 2
    originalYearlyPrice: 7176, // $299 × 2 × 12
    savings: 300, // $150 savings per user
    yearlySavings: 3600,
    promotional: true,
    bundleRequirement: "Both users must sign up simultaneously to qualify for bundle pricing",
    features: [
      "2 desktop devices + 2 mobile devices (1 per user)",
      "Two user licenses included",
      "Mobile device support (Beta) for both users",
      "Shared team dashboard",
      "Collaborative device management",
      "Team usage analytics",
      "Bulk device operations",
      "Priority team support",
    ],
    limitations: [
      "Requires simultaneous signup",
      "Cannot combine with other discounts",
    ],
    buttonText: "Start Two User Bundle",
    buttonVariant: "default",
  },
];

export default function PricingPage() {
  const [isYearly, setIsYearly] = useState(false);
  const router = useRouter();
  const { data: session, status } = useSession();

  const handlePlanSelect = (planId: string) => {
    if (planId === "enterprise") {
      // Redirect to contact form for enterprise
      router.push("/contact?plan=enterprise");
    } else {
      // Check if user is authenticated
      if (session?.user) {
        // User is logged in, go directly to checkout
        const couponParam = planId === "single-user" ? "&coupon=SPEARMINT" : "";
        const planParam = planId === "two-user-bundle" ? "two-user" : planId;
        router.push(`/checkout?plan=${planParam}${couponParam}`);
      } else {
        // User not logged in, redirect to login with callback to checkout
        const couponParam = planId === "single-user" ? "&coupon=SPEARMINT" : "";
        const planParam = planId === "two-user-bundle" ? "two-user" : planId;
        router.push(`/login?callbackUrl=${encodeURIComponent(`/checkout?plan=${planParam}${couponParam}`)}`);
      }
    }
  };

  const getPrice = (plan: PricingPlan) => {
    return isYearly ? plan.yearlyPrice : plan.monthlyPrice;
  };

  const getOriginalPrice = (plan: PricingPlan) => {
    if (!plan.originalPrice) return null;
    return isYearly ? plan.originalYearlyPrice : plan.originalPrice;
  };

  const getSavings = (plan: PricingPlan) => {
    if (plan.promotional) {
      return isYearly ? plan.yearlySavings : plan.savings;
    }
    if (!isYearly) return 0;
    const monthlyTotal = plan.monthlyPrice * 12;
    return monthlyTotal - plan.yearlyPrice;
  };

  const getPerUserPrice = (plan: PricingPlan) => {
    if (plan.id === "two-user-bundle") {
      return Math.round(getPrice(plan) / 2);
    }
    return getPrice(plan);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/images/spear-logo.PNG"
                alt="SPEAR Logo"
                width={40}
                height={40}
                className="object-contain"
              />
              <span className="text-2xl font-bold text-primary">SPEAR</span>
            </Link>
            <div className="flex items-center space-x-4">
              {session?.user ? (
                <>
                  <Link href="/dashboard" className="text-sm text-muted-foreground hover:text-foreground">
                    Dashboard
                  </Link>
                  <Button asChild size="sm" variant="outline">
                    <Link href="/dashboard">My Account</Link>
                  </Button>
                </>
              ) : (
                <>
                  <Link href="/login" className="text-sm text-muted-foreground hover:text-foreground">
                    Sign In
                  </Link>
                  <Button asChild size="sm">
                    <Link href="/register">Create Account</Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16 text-center">
        <div className="mb-6">
          <Badge className="mb-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 text-sm font-semibold">
            🎉 LIMITED TIME OFFER
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Special Launch Pricing
          </h1>
        </div>
        <p className="text-xl text-muted-foreground mb-4 max-w-2xl mx-auto">
          Get professional remote device management at incredible savings.
          Use coupon <span className="font-bold text-primary">SPEARMINT</span> for $100 off monthly plans.
        </p>
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 max-w-2xl mx-auto mb-6">
          <p className="text-sm text-amber-800">
            <strong>Bundle Special:</strong> Two users for just $298/month (normally $598) -
            both users must sign up simultaneously to qualify.
          </p>
        </div>

        {/* Mobile Support Callout */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 max-w-3xl mx-auto mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-blue-100 rounded-full p-3 mr-4">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-blue-900">Mobile Device Support Included</h3>
            <Badge className="ml-3 bg-blue-600 text-white">BETA</Badge>
          </div>
          <div className="text-center">
            <p className="text-blue-800 mb-3">
              <strong>All plans include mobile device support!</strong> We provide pre-configured phones
              that you can connect to remotely for complete mobile device management.
            </p>
            <p className="text-sm text-blue-700">
              Mobile devices are shipped directly to you and come pre-configured for immediate remote access.
              Perfect for testing mobile apps, managing mobile workflows, or accessing mobile-only services.
            </p>
          </div>
        </div>



        {/* Billing Toggle */}
        <div className="flex items-center justify-center space-x-4 mb-12">
          <span className={`text-sm ${!isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
            Monthly
          </span>
          <Switch
            checked={isYearly}
            onCheckedChange={setIsYearly}
            className="data-[state=checked]:bg-primary"
          />
          <span className={`text-sm ${isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
            Yearly
          </span>
          <Badge variant="secondary" className="ml-2">
            Save 2 months
          </Badge>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="container mx-auto px-4 pb-16">
        <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {pricingPlans.map((plan) => (
            <Card
              key={plan.id}
              className={`relative ${plan.popular ? 'border-primary shadow-lg scale-105' : ''} ${plan.promotional ? 'border-green-500 bg-gradient-to-br from-green-50 to-emerald-50' : ''}`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary">
                  Most Popular
                </Badge>
              )}
              {plan.promotional && (
                <Badge className="absolute -top-3 right-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white">
                  🔥 Special Offer
                </Badge>
              )}

              <CardHeader className="text-center">
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <CardDescription className="text-sm">
                  {plan.description}
                </CardDescription>

                {/* Pricing Display */}
                <div className="mt-4">
                  {plan.promotional && getOriginalPrice(plan) && (
                    <div className="mb-2">
                      <span className="text-lg text-muted-foreground line-through">
                        ${getOriginalPrice(plan)}
                      </span>
                      <span className="text-muted-foreground ml-1">
                        /{isYearly ? 'year' : 'month'}
                      </span>
                    </div>
                  )}

                  <div className="flex items-baseline justify-center">
                    <span className={`text-4xl font-bold ${plan.promotional ? 'text-green-600' : ''}`}>
                      ${getPrice(plan)}
                    </span>
                    <span className="text-muted-foreground ml-1">
                      /{isYearly ? 'year' : 'month'}
                    </span>
                  </div>

                  {plan.id === "two-user-bundle" && (
                    <p className="text-sm text-muted-foreground mt-1">
                      ${getPerUserPrice(plan)} per user
                    </p>
                  )}

                  {getSavings(plan) > 0 && (
                    <div className="mt-2">
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Save ${getSavings(plan)} {isYearly ? 'per year' : 'per month'}
                      </Badge>
                    </div>
                  )}

                  {plan.couponCode && (
                    <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                      <p className="text-xs text-yellow-800">
                        Use coupon: <span className="font-bold">{plan.couponCode}</span>
                      </p>
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {plan.bundleRequirement && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-xs text-blue-800">
                      <strong>Bundle Requirement:</strong> {plan.bundleRequirement}
                    </p>
                  </div>
                )}

                <div>
                  <h4 className="font-semibold mb-2 text-green-600">What's included:</h4>
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <CheckIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {plan.limitations.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-2 text-muted-foreground">Important Notes:</h4>
                    <ul className="space-y-2">
                      {plan.limitations.map((limitation, index) => (
                        <li key={index} className="flex items-center text-sm text-muted-foreground">
                          <XMarkIcon className="h-4 w-4 text-muted-foreground mr-2 flex-shrink-0" />
                          {limitation}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {plan.promotional && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-xs text-red-800">
                      <strong>⚠️ Important:</strong> If any discounted subscription is canceled,
                      promotional pricing is lost and renewal will be at full price.
                    </p>
                  </div>
                )}
              </CardContent>

              <CardFooter>
                <Button
                  className="w-full"
                  variant={plan.buttonVariant}
                  onClick={() => handlePlanSelect(plan.id)}
                >
                  {plan.buttonText}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>

      {/* Getting Started Section */}
      <div className="bg-gradient-to-r from-primary/10 to-purple-500/10 py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Create your account first, then choose your plan and complete payment.
            We'll handle device shipping and setup for you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/register">Create Account</Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/login">Already have an account?</Link>
            </Button>
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            Step 1: Create Account → Step 2: Choose Plan → Step 3: Pay → Step 4: Get Your Devices
          </p>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="bg-muted/50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Frequently Asked Questions
          </h2>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div>
              <h3 className="font-semibold mb-2">How do I use the SPEARMINT coupon?</h3>
              <p className="text-muted-foreground text-sm">
                Enter "SPEARMINT" during checkout to get $100 off your monthly subscription.
                This coupon cannot be combined with bundle pricing.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">How does the Two User Bundle work?</h3>
              <p className="text-muted-foreground text-sm">
                Both users must sign up simultaneously to qualify for the $298/month bundle price.
                Each user gets their own account with full access.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">What happens if I cancel my promotional subscription?</h3>
              <p className="text-muted-foreground text-sm">
                If you cancel and later resubscribe, you'll pay the regular price ($299/month).
                Promotional pricing is only available for new subscriptions.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Can I combine discounts?</h3>
              <p className="text-muted-foreground text-sm">
                No, coupon codes and bundle discounts cannot be combined. Choose the option
                that provides the best value for your needs.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">What payment methods do you accept?</h3>
              <p className="text-muted-foreground text-sm">
                We accept all major credit cards and PayPal. All payments are processed securely.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">How do I get started?</h3>
              <p className="text-muted-foreground text-sm">
                Create an account, choose your plan, and complete payment. We'll then ship your
                pre-configured mobile device and provide access to your remote desktop solution.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">How does mobile device support work?</h3>
              <p className="text-muted-foreground text-sm">
                We ship pre-configured mobile phones directly to you that come ready for
                remote access. Perfect for mobile app testing and mobile workflow management.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Is there a money-back guarantee?</h3>
              <p className="text-muted-foreground text-sm">
                Yes, we offer a 30-day money-back guarantee. If you're not satisfied,
                we'll refund your payment in full.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="border-t py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center space-y-4">
            <div className="flex items-center space-x-3">
              <Image
                src="/images/spear-logo.PNG"
                alt="SPEAR Logo"
                width={32}
                height={32}
                className="object-contain"
              />
              <span className="text-lg font-bold text-primary">SPEAR</span>
            </div>
            <p className="text-sm text-muted-foreground text-center">
              Professional remote device management with mobile support
            </p>
            <p className="text-xs text-muted-foreground">
              &copy; 2024 SPEAR. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
