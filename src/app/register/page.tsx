"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

function RegisterContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [billingCycle, setBillingCycle] = useState<string | null>(null);

  // Get plan and billing info from URL params
  useEffect(() => {
    const plan = searchParams.get("plan");
    const billing = searchParams.get("billing");
    if (plan) setSelectedPlan(plan);
    if (billing) setBillingCycle(billing);
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    // Basic validation
    if (!name || !email || !password || !confirmPassword) {
      setError("All fields are required");
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (password.length < 8) {
      setError("Password must be at least 8 characters long");
      return;
    }

    setIsLoading(true);

    try {
      // Call the API to register the user
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          email,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to register");
      }

      // Registration successful
      if (selectedPlan) {
        setSuccess("Registration successful! Redirecting to checkout...");
        // Redirect to checkout with selected plan
        setTimeout(() => {
          router.push(`/checkout?plan=${selectedPlan}&billing=${billingCycle || "monthly"}`);
        }, 2000);
      } else {
        setSuccess("Registration successful! Redirecting to choose your plan...");
        // Redirect to pricing page to choose a plan
        setTimeout(() => {
          router.push("/pricing");
        }, 2000);
      }
    } catch (err) {
      console.error("Registration error:", err);

      // Provide helpful error messages based on the error
      let errorMessage = "An error occurred during registration";

      if (err instanceof Error) {
        const message = err.message.toLowerCase();

        if (message.includes("email already exists") || message.includes("user with this email")) {
          errorMessage = "An account with this email already exists. Try logging in instead or use a different email address.";
        } else if (message.includes("network") || message.includes("fetch")) {
          errorMessage = "Network error. Please check your internet connection and try again.";
        } else if (message.includes("validation") || message.includes("invalid")) {
          errorMessage = "Please check that all fields are filled out correctly and try again.";
        } else if (message.includes("server") || message.includes("500")) {
          errorMessage = "Server error. Our team has been notified. Please try again in a few minutes or contact support if the issue persists.";
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-slate-50 dark:bg-slate-900 p-4">
      <div className="w-full max-w-md">
        <div className="mb-8 flex flex-col items-center text-center">
          <Image
            src="/images/spear-logo.PNG"
            alt="Spear Logo"
            width={120}
            height={120}
            className="mb-4"
          />
          <h1 className="text-3xl font-bold tracking-tight">Create an Account</h1>
          <p className="text-slate-500 dark:text-slate-400 mt-2">
            Step 1: Create your account • Step 2: Choose your plan • Step 3: Get your devices
          </p>
          <div className="mt-4">
            <Link href="/pricing" className="text-sm text-primary hover:underline">
              View pricing plans →
            </Link>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Register</CardTitle>
            <CardDescription>
              {selectedPlan ? (
                <>
                  Creating account for <strong>{selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1)}</strong> plan
                  {billingCycle && ` (${billingCycle})`}
                </>
              ) : (
                "Fill out the form below to create your account"
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  type="text"
                  placeholder="Enter your full name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Create a password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  disabled={isLoading}
                />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="bg-green-50 text-green-800 border-green-200">
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating Account...
                  </>
                ) : (
                  "Create Account"
                )}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col space-y-3">
            <div className="text-center">
              <p className="text-sm text-slate-500 dark:text-slate-400">
                Already have an account?{" "}
                <Link href="/login" className="text-primary hover:underline">
                  Log in
                </Link>
              </p>
            </div>
            <div className="text-center space-y-2">
              <p className="text-xs text-slate-400">
                After creating your account, you'll choose a plan and we'll ship your pre-configured devices.
              </p>
              <p className="text-xs text-slate-400">
                Questions about pricing?{" "}
                <Link href="/pricing" className="text-primary hover:underline">
                  View our plans
                </Link>
                {" "}or{" "}
                <Link href="/contact" className="text-primary hover:underline">
                  contact sales
                </Link>
              </p>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

export default function RegisterPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-background flex items-center justify-center">Loading...</div>}>
      <RegisterContent />
    </Suspense>
  );
}
