'use server';

import { prisma } from "@/lib/db";

/**
 * Get all blog posts with optional filtering
 * @param options Optional filtering options
 * @returns Array of blog posts
 */
export async function getAllPosts(options?: {
  status?: string;
  category?: string;
  author?: string;
  searchQuery?: string;
}) {
  try {
    const where: any = {};

    // Apply filters if provided
    if (options?.status && options.status !== "all") {
      where.status = options.status;
    }

    if (options?.category) {
      where.category = options.category;
    }

    if (options?.author) {
      where.author = options.author;
    }

    if (options?.searchQuery) {
      where.OR = [
        { title: { contains: options.searchQuery, mode: "insensitive" } },
        { excerpt: { contains: options.searchQuery, mode: "insensitive" } },
        { author: { contains: options.searchQuery, mode: "insensitive" } },
      ];
    }

    const posts = await prisma.blogPost.findMany({
      where,
      orderBy: {
        publishedAt: "desc",
      },
      include: {
        comments: {
          select: {
            id: true,
          },
        },
      },
    });

    // Format the posts data
    return posts.map((post) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      status: post.status,
      author: post.author,
      category: post.category,
      publishedAt: post.publishedAt ? post.publishedAt.toISOString() : null,
      views: post.views,
      comments: post.comments.length,
    }));
  } catch (error) {
    console.error("Error getting blog posts:", error);
    throw error;
  }
}

/**
 * Get a blog post by slug
 * @param slug The slug of the blog post to get
 * @returns Blog post
 */
export async function getPostBySlug(slug: string) {
  try {
    const post = await prisma.blogPost.findUnique({
      where: {
        slug,
      },
      include: {
        comments: {
          select: {
            id: true,
          },
        },
      },
    });

    if (!post) {
      throw new Error(`Blog post with slug ${slug} not found`);
    }

    // Format the post data
    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      status: post.status,
      author: post.author,
      category: post.category,
      publishedAt: post.publishedAt ? post.publishedAt.toISOString() : null,
      views: post.views,
      comments: post.comments.length,
    };
  } catch (error) {
    console.error(`Error getting blog post with slug ${slug}:`, error);
    throw error;
  }
}

/**
 * Create a new blog post
 * @param postData The blog post data to create
 * @returns Created blog post
 */
export async function createPost(postData: {
  title: string;
  slug: string;
  excerpt: string;
  content?: string;
  status?: string;
  author: string;
  category: string;
}) {
  try {
    const newPost = await prisma.blogPost.create({
      data: {
        title: postData.title,
        slug: postData.slug,
        excerpt: postData.excerpt,
        content: postData.content || "",
        status: postData.status || "draft",
        author: postData.author,
        category: postData.category,
        views: 0,
        userId: "admin-1", // Default to admin user for now
      },
      include: {
        comments: {
          select: {
            id: true,
          },
        },
      },
    });

    // Format the post data
    return {
      id: newPost.id,
      title: newPost.title,
      slug: newPost.slug,
      excerpt: newPost.excerpt,
      status: newPost.status,
      author: newPost.author,
      category: newPost.category,
      publishedAt: newPost.publishedAt ? newPost.publishedAt.toISOString() : null,
      views: newPost.views,
      comments: newPost.comments.length,
    };
  } catch (error) {
    console.error("Error creating blog post:", error);
    throw error;
  }
}

/**
 * Get all blog categories with post counts
 * @returns Array of categories with post counts
 */
export async function getAllCategories() {
  try {
    const categories = await prisma.blogPost.groupBy({
      by: ["category"],
      _count: {
        category: true,
      },
    });

    // Format the categories data
    return categories.map((category) => ({
      name: category.category,
      count: category._count.category,
    }));
  } catch (error) {
    console.error("Error getting blog categories:", error);
    throw error;
  }
}

/**
 * Get SEO keywords data
 * @returns Array of SEO keywords with metrics
 */
export async function getSeoKeywords() {
  try {
    // This would typically come from an SEO API or database
    // For now, we'll return mock data
    return [
      { keyword: "remote access", volume: 12500, difficulty: 67, cpc: 4.25 },
      { keyword: "rustdesk enterprise", volume: 8200, difficulty: 42, cpc: 3.80 },
      { keyword: "rustdesk security features", volume: 6700, difficulty: 38, cpc: 3.50 },
      { keyword: "remote device management", volume: 5400, difficulty: 51, cpc: 5.20 },
      { keyword: "secure remote access", volume: 9800, difficulty: 62, cpc: 6.10 }
    ];
  } catch (error) {
    console.error("Error getting SEO keywords:", error);
    throw error;
  }
}
