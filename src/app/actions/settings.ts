'use server';

import { prisma } from "@/lib/db";

// Define the settings structure
export interface Settings {
  general: {
    companyName: string;
    supportEmail: string;
    supportPhone: string;
    defaultDeviceLimit: number;
  };
  branding: {
    primaryColor: string;
    accentColor: string;
    logoUrl: string;
    useDarkMode: boolean;
    useCustomFonts: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    deviceOfflineAlerts: boolean;
    paymentReminders: boolean;
    newClientNotifications: boolean;
    systemUpdates: boolean;
  };
  security: {
    requireMfa: boolean;
    sessionTimeout: number;
    passwordExpiration: number;
    ipRestrictions: boolean;
  };
  email: {
    smtpServer: string;
    smtpPort: number;
    smtpUsername: string;
    emailFromName: string;
  };
}

// Default settings
export const defaultSettings: Settings = {
  general: {
    companyName: "SPEAR Technologies",
    supportEmail: "<EMAIL>",
    supportPhone: "+****************",
    defaultDeviceLimit: 1,
  },
  branding: {
    primaryColor: "#6366f1",
    accentColor: "#8b5cf6",
    logoUrl: "/images/spear-logo.PNG",
    useDarkMode: true,
    useCustomFonts: false,
  },
  notifications: {
    emailNotifications: true,
    deviceOfflineAlerts: true,
    paymentReminders: true,
    newClientNotifications: true,
    systemUpdates: true,
  },
  security: {
    requireMfa: false,
    sessionTimeout: 30,
    passwordExpiration: 90,
    ipRestrictions: false,
  },
  email: {
    smtpServer: "smtp.example.com",
    smtpPort: 587,
    smtpUsername: "<EMAIL>",
    emailFromName: "SPEAR Support",
  }
};

/**
 * Get all settings
 * @returns Settings object
 */
export async function getSettings(): Promise<Settings> {
  try {
    // Get all settings from the database
    const settingsRecords = await prisma.setting.findMany();
    
    // If no settings exist, return default settings
    if (settingsRecords.length === 0) {
      return defaultSettings;
    }
    
    // Convert the flat settings records to the structured settings object
    const settings: any = {
      general: {},
      branding: {},
      notifications: {},
      security: {},
      email: {},
    };
    
    // Process each setting record
    settingsRecords.forEach(record => {
      const [category, key] = record.key.split('.');
      
      if (!settings[category]) {
        settings[category] = {};
      }
      
      // Convert value based on type
      let value: any = record.value;
      if (record.type === 'boolean') {
        value = value === 'true';
      } else if (record.type === 'number') {
        value = parseFloat(value);
      }
      
      settings[category][key] = value;
    });
    
    // Fill in any missing settings with defaults
    for (const category in defaultSettings) {
      if (!settings[category]) {
        settings[category] = {};
      }
      
      for (const key in defaultSettings[category as keyof Settings]) {
        if (settings[category][key] === undefined) {
          settings[category][key] = defaultSettings[category as keyof Settings][key as any];
        }
      }
    }
    
    return settings as Settings;
  } catch (error) {
    console.error("Error getting settings:", error);
    return defaultSettings;
  }
}

/**
 * Update settings
 * @param settings Settings object to update
 * @returns Updated settings
 */
export async function updateSettings(settings: Settings): Promise<Settings> {
  try {
    // Convert the structured settings object to flat settings records
    const settingsRecords = [];
    
    for (const category in settings) {
      for (const key in settings[category as keyof Settings]) {
        const value = settings[category as keyof Settings][key as any];
        const type = typeof value;
        
        settingsRecords.push({
          key: `${category}.${key}`,
          value: String(value),
          type,
        });
      }
    }
    
    // Update settings in the database using a transaction
    await prisma.$transaction(async (tx) => {
      // Delete all existing settings
      await tx.setting.deleteMany({});
      
      // Create new settings
      for (const record of settingsRecords) {
        await tx.setting.create({
          data: record,
        });
      }
    });
    
    return settings;
  } catch (error) {
    console.error("Error updating settings:", error);
    throw error;
  }
}

/**
 * Test email configuration
 * @returns Success message
 */
export async function testEmailConfiguration(): Promise<string> {
  try {
    // In a real implementation, this would send a test email
    // For now, we'll just simulate a successful test
    return "Test email sent successfully";
  } catch (error) {
    console.error("Error testing email configuration:", error);
    throw error;
  }
}
