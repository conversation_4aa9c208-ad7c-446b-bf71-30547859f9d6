'use server';

import { prisma } from "@/lib/db";

/**
 * Get dashboard statistics for the admin dashboard
 * @returns Object containing counts of clients, devices, active subscriptions, and past due subscriptions
 */
export async function getDashboardStats() {
  try {
    // Get total number of clients (users with CLIENT role)
    const clientCount = await prisma.user.count({
      where: {
        role: "CLIENT",
      },
    });

    // Get total number of devices
    const deviceCount = await prisma.device.count();

    // Get count of active subscriptions
    const activeSubscriptionCount = await prisma.subscription.count({
      where: {
        status: "active",
      },
    });

    // Get count of past due subscriptions
    const pastDueSubscriptionCount = await prisma.subscription.count({
      where: {
        status: "past_due",
      },
    });

    return {
      clientCount,
      deviceCount,
      activeSubscriptionCount,
      pastDueSubscriptionCount,
    };
  } catch (error) {
    console.error("Error getting dashboard stats:", error);
    throw error;
  }
}

/**
 * Get recent clients for the admin dashboard
 * @param limit Number of clients to return
 * @returns Array of clients with their subscription and device information
 */
export async function getRecentClients(limit = 5) {
  try {
    const clients = await prisma.user.findMany({
      where: {
        role: "CLIENT",
      },
      take: limit,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
        devices: {
          select: {
            id: true,
          },
        },
      },
    });

    // Format the clients data
    return clients.map((client) => ({
      id: client.id,
      name: client.name || "Unnamed Client",
      email: client.email,
      subscription: client.subscriptions[0] || {
        status: "none",
        currentPeriodEnd: new Date(),
      },
      devices: client.devices.length,
    }));
  } catch (error) {
    console.error("Error getting recent clients:", error);
    throw error;
  }
}

/**
 * Get recent devices for the admin dashboard
 * @param limit Number of devices to return
 * @returns Array of devices with their assigned user information
 */
export async function getRecentDevices(limit = 5) {
  try {
    const devices = await prisma.device.findMany({
      take: limit,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        assignedTo: true,
      },
    });

    // Format the devices data
    return devices.map((device) => ({
      id: device.id,
      name: device.name,
      deviceModel: device.model || "Unknown Model",
      deviceId: device.rustDeskId,
      assignedTo: device.assignedTo?.name || null,
      status: device.status,
      lastActive: device.updatedAt.toISOString(),
    }));
  } catch (error) {
    console.error("Error getting recent devices:", error);
    throw error;
  }
}

/**
 * Get client details by ID
 * @param clientId The ID of the client to get details for
 * @returns Client details including subscription and devices
 */
export async function getClientDetails(clientId: string) {
  try {
    const client = await prisma.user.findUnique({
      where: {
        id: clientId,
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
        devices: true,
      },
    });

    if (!client) {
      throw new Error(`Client with ID ${clientId} not found`);
    }

    return {
      id: client.id,
      name: client.name || "Unnamed Client",
      email: client.email,
      subscription: client.subscriptions[0] || {
        status: "none",
        currentPeriodEnd: new Date(),
      },
      devices: client.devices.length,
    };
  } catch (error) {
    console.error(`Error getting client details for ID ${clientId}:`, error);
    throw error;
  }
}
