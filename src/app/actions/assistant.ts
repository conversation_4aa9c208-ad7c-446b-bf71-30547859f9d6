'use server'

// import { mastra } from '@/mastra'; // Temporarily disabled
import { randomUUID } from 'crypto';

// Store user sessions
const userSessions: Record<string, { threadId: string, resourceId: string }> = {};

export async function processAssistantMessage(message: string, userId: string = 'default-user') {
  try {
    console.log("Processing message:", message);

    // Get or create session for this user
    if (!userSessions[userId]) {
      userSessions[userId] = {
        threadId: randomUUID(),
        resourceId: userId,
      };
      console.log(`Created new session for user ${userId}: ${userSessions[userId].threadId}`);
    }

    const { threadId, resourceId } = userSessions[userId];

    // Temporarily disabled Mastra to fix Firebase build error
    // const network = mastra.getNetwork("adminNetwork");
    // const response = await network.generate(message, { threadId, resourceId, maxSteps: 10 });

    const response = {
      text: "Admin assistant is temporarily disabled due to build issues. Please try again later.",
      toolCalls: []
    };

    console.log("Network response received (mock)");
    console.log("Tool calls:", response.toolCalls?.length || 0);

    // Return the mock response
    return {
      response: response.text,
      toolResults: undefined,
      agentUsed: "Disabled",
    };
  } catch (error) {
    console.error("Error processing assistant message:", error);
    return {
      response: "I'm sorry, I encountered an error while processing your request. Please try again later.",
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
