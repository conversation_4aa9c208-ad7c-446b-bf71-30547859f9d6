"use client";

import { useState, useEffect, Suspense } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useSearchParams } from "next/navigation";

function AutoClickContent() {
  // Get search params
  const searchParams = useSearchParams();

  // State
  const [deviceId, setDeviceId] = useState(searchParams.get('id') || "1681512408");
  const [password, setPassword] = useState(searchParams.get('password') || "82AirmaN@$");
  const [isConnecting, setIsConnecting] = useState(false);
  const [statusMessage, setStatusMessage] = useState<{ text: string; type: 'default' | 'info' | 'success' | 'error' } | null>(null);
  const [progressStep, setProgressStep] = useState(0);
  const [debugImage, setDebugImage] = useState<string | null>(null);
  const [initialImage, setInitialImage] = useState<string | null>(null);

  // Progress steps
  const progressSteps = [
    'Launching browser...',
    'Navigating to RustDesk...',
    'Waiting for device to appear...',
    'Clicking on device...',
    'Establishing connection...'
  ];

  // Auto-connect if URL parameters are present
  useEffect(() => {
    if (searchParams.get('id')) {
      handleConnect();
    }
  }, []);

  // Progress step updater
  useEffect(() => {
    if (isConnecting) {
      const interval = setInterval(() => {
        if (progressStep < progressSteps.length - 1) {
          setProgressStep(prev => prev + 1);
        } else {
          clearInterval(interval);
        }
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [isConnecting, progressStep]);

  // Connect to device
  const handleConnect = async () => {
    const trimmedDeviceId = deviceId.trim();

    if (!trimmedDeviceId) {
      setStatusMessage({ text: 'Please enter a device ID', type: 'error' });
      return;
    }

    // Start connection process
    setIsConnecting(true);
    setProgressStep(0);
    setStatusMessage({ text: 'Initializing connection...', type: 'info' });
    setDebugImage(null);
    setInitialImage(null);

    try {
      // Call the server API to automate the connection
      const response = await fetch('/api/remote-connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ deviceId: trimmedDeviceId, password }),
      });

      if (!response.ok) {
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        // Connection successful
        setStatusMessage({ text: 'Connection successful! Opening remote session...', type: 'success' });

        // Open the connection URL in a new window
        setTimeout(() => {
          const remoteWindow = window.open(data.connectionUrl, '_blank');

          // Check if window was blocked
          if (!remoteWindow || remoteWindow.closed || typeof remoteWindow.closed === 'undefined') {
            setStatusMessage({ text: 'Popup blocked. Please allow popups and try again.', type: 'error' });
          } else {
            setStatusMessage({ text: 'Remote session opened in a new window.', type: 'success' });
          }

          // Reset connecting state
          setIsConnecting(false);
        }, 1000);
      } else {
        // Connection failed
        setStatusMessage({ text: `Connection failed: ${data.error || 'Unknown error'}`, type: 'error' });
        setIsConnecting(false);

        // Show debug screenshots if available
        if (data.debugScreenshot) {
          setDebugImage(data.debugScreenshot);
        }
        if (data.initialScreenshot) {
          setInitialImage(data.initialScreenshot);
        }
      }
    } catch (error) {
      setStatusMessage({ text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`, type: 'error' });
      setIsConnecting(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Image
              src="/images/spear-logo.PNG"
              alt="Spear Logo"
              width={120}
              height={60}
              className="h-auto"
            />
          </div>
          <CardTitle>Auto Connect</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="deviceId">Device ID</Label>
              <Input
                id="deviceId"
                placeholder="Enter device ID"
                value={deviceId}
                onChange={(e) => setDeviceId(e.target.value)}
                disabled={isConnecting}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Access Code</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter access code"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isConnecting}
              />
            </div>

            <Button
              onClick={handleConnect}
              disabled={isConnecting || !deviceId.trim()}
              className="w-full"
            >
              {isConnecting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Connecting...
                </>
              ) : (
                "Connect to Device"
              )}
            </Button>

            {isConnecting && (
              <div className="text-sm text-slate-400 text-center">
                {progressSteps[progressStep]}
              </div>
            )}

            {statusMessage && (
              <Alert className={
                statusMessage.type === 'error' ? 'bg-red-900/20 text-red-400 border-red-900/50' :
                statusMessage.type === 'success' ? 'bg-green-900/20 text-green-400 border-green-900/50' :
                statusMessage.type === 'info' ? 'bg-blue-900/20 text-blue-400 border-blue-900/50' :
                'bg-slate-900/20 text-slate-400 border-slate-900/50'
              }>
                <AlertDescription>
                  {statusMessage.text}
                </AlertDescription>
              </Alert>
            )}

            {(debugImage || initialImage) && (
              <div className="mt-4 space-y-4">
                {debugImage && (
                  <div>
                    <p className="text-sm text-slate-400 mb-2">Final Screenshot:</p>
                    <img
                      src={debugImage}
                      alt="Debug Screenshot"
                      className="w-full rounded border border-slate-700"
                    />
                  </div>
                )}

                {initialImage && (
                  <div>
                    <p className="text-sm text-slate-400 mb-2">Initial Screenshot:</p>
                    <img
                      src={initialImage}
                      alt="Initial Screenshot"
                      className="w-full rounded border border-slate-700"
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {isConnecting && (
        <div className="fixed inset-0 bg-slate-900/90 flex items-center justify-center z-50 flex-col">
          <div className="w-12 h-12 border-4 border-t-transparent border-indigo-600 rounded-full animate-spin mb-4"></div>
          <p className="text-lg font-medium text-white">Connecting to your device...</p>
          <p className="text-sm text-slate-400 mt-2">{progressSteps[progressStep]}</p>
        </div>
      )}
    </div>
  );
}

// Wrap the component in a Suspense boundary
export default function AutoClickPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </CardContent>
        </Card>
      </div>
    }>
      <AutoClickContent />
    </Suspense>
  );
}
