"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DevicePhoneMobileIcon,
  ArrowTopRightOnSquareIcon,
  CheckCircleIcon,
  EnvelopeIcon,
  ExclamationCircleIcon
} from "@heroicons/react/24/outline";
import { useState, useEffect } from "react";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { VRTitleSection } from "@/components/dashboard/vr-title-section";
import { ConnectionOverlay } from "@/components/dashboard/connection-overlay";
import { RustDeskConnection } from "@/components/dashboard/rustdesk-connection";

export default function DashboardPage() {
  const [isRemoteControlOpen, setIsRemoteControlOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<any>(null);
  const [devices, setDevices] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscription, setSubscription] = useState<any>(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(true);

  const { data: session } = useSession();

  // Fetch user's devices and subscription
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const response = await fetch('/api/devices');
        if (!response.ok) {
          throw new Error('Failed to fetch devices');
        }
        const data = await response.json();
        setDevices(data.devices || []);
      } catch (err) {
        console.error('Error fetching devices:', err);
        setError('Failed to load your devices. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    const fetchSubscription = async () => {
      try {
        const response = await fetch('/api/square/subscriptions');
        if (response.ok) {
          const data = await response.json();
          setSubscription(data.subscription);
        }
      } catch (err) {
        console.error('Error fetching subscription:', err);
      } finally {
        setSubscriptionLoading(false);
      }
    };

    if (session) {
      fetchDevices();
      fetchSubscription();
    }
  }, [session]);

  const handleRemoteControl = (device: any) => {
    setSelectedDevice(device);
    setIsRemoteControlOpen(true);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-b from-slate-950 to-slate-900">
      {/* Header */}
      <header className="py-6 px-4 md:px-6 bg-slate-900/50 backdrop-blur-lg border-b border-slate-800">
        <div className="container mx-auto">
          <VRTitleSection />
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 py-8 px-4 md:px-6">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Devices Section */}
            <div className="md:col-span-2 space-y-6">
              <Card className="border-slate-800 bg-slate-900/50 backdrop-blur-sm shadow-lg">
                <CardHeader className="border-b border-slate-800 pb-4">
                  <CardTitle className="text-xl font-bold text-white">My Devices</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  {loading ? (
                    <div className="flex justify-center items-center py-12">
                      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                  ) : error ? (
                    <div className="text-center py-8">
                      <ExclamationCircleIcon className="h-12 w-12 mx-auto text-red-500 mb-4" />
                      <h3 className="text-lg font-medium text-white mb-2">Error Loading Devices</h3>
                      <p className="text-slate-400 mb-4">{error}</p>
                      <Button
                        variant="outline"
                        className="border-slate-700"
                        onClick={() => window.location.reload()}
                      >
                        Retry
                      </Button>
                    </div>
                  ) : devices.length > 0 ? (
                    <div className="space-y-4">
                      {devices.map((device) => {
                        // Add a default password for testing if not set
                        if (!device.password && device.rustDeskId === '1681512408') {
                          device.password = '82AirmaN@$';
                        }

                        return (
                          <div
                            key={device.id}
                            className="flex items-center justify-between p-4 rounded-lg border border-slate-800 bg-slate-900/50 hover:bg-slate-800/50 transition-colors"
                          >
                            <div className="flex items-center space-x-4">
                              <div className="bg-slate-800 p-2 rounded-lg">
                                <DevicePhoneMobileIcon className="h-6 w-6 text-blue-400" />
                              </div>
                              <div>
                                <h3 className="font-medium text-white">{device.name}</h3>
                                <p className="text-sm text-slate-400">
                                  {device.model || "Device"} • ID: {device.rustDeskId}
                                </p>
                                {device.password && (
                                  <p className="text-xs text-slate-500">
                                    Password: {device.password}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center space-x-3">
                              <div className="flex items-center">
                                <span
                                  className={`h-2 w-2 rounded-full mr-2 ${
                                    device.status === "online"
                                      ? "bg-green-500"
                                      : "bg-red-500"
                                  }`}
                                />
                                <span className="text-sm text-slate-300">
                                  {device.status === "online" ? "Online" : "Offline"}
                                </span>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-slate-700 text-slate-200 hover:text-white hover:bg-slate-800"
                                onClick={() => handleRemoteControl(device)}
                              >
                                <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-1" />
                                Connect
                              </Button>
                            </div>
                          </div>
                        );
                      })}

                      {/* Direct Console Access */}
                      <div className="mt-6 pt-6 border-t border-slate-800">
                        <div className="flex items-center justify-between p-4 rounded-lg border border-slate-700 bg-slate-800/30">
                          <div className="flex items-center space-x-4">
                            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-lg">
                              <ArrowTopRightOnSquareIcon className="h-6 w-6 text-white" />
                            </div>
                            <div>
                              <h3 className="font-medium text-white">Direct Console Access</h3>
                              <p className="text-sm text-slate-400">
                                Access the RustDesk web console to connect to any device
                              </p>
                            </div>
                          </div>
                          <Button
                            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                            onClick={() => window.open('/rustdesk-connect', '_blank')}
                          >
                            <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-1" />
                            Open Console
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <DevicePhoneMobileIcon className="h-12 w-12 mx-auto text-slate-600 mb-4" />
                      <h3 className="text-lg font-medium text-white mb-2">No Devices Assigned</h3>
                      <p className="text-slate-400 mb-4">
                        Your account is active, but no devices have been assigned yet. Our team will configure and assign your devices shortly after subscription activation.
                      </p>
                      <p className="text-slate-400 mb-4 text-sm">
                        <strong>Next steps:</strong> Check your email for device setup instructions, or contact support if you need immediate assistance.
                      </p>
                      <div className="space-y-3">
                        <Button
                          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                          onClick={() => window.open('/rustdesk-connect', '_blank')}
                        >
                          <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-2" />
                          Open RustDesk Console
                        </Button>
                        <p className="text-xs text-slate-500">
                          Access the RustDesk web console to connect to your devices directly
                        </p>
                        <Link href="/help">
                          <Button variant="outline" className="border-slate-700">
                            Contact Support
                          </Button>
                        </Link>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Activity Section */}
              <Card className="border-slate-800 bg-slate-900/50 backdrop-blur-sm shadow-lg">
                <CardHeader className="border-b border-slate-800 pb-4">
                  <CardTitle className="text-xl font-bold text-white">Recent Activity</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="flex items-start space-x-4">
                      <div className="bg-blue-500/20 p-2 rounded-full">
                        <ArrowTopRightOnSquareIcon className="h-5 w-5 text-blue-400" />
                      </div>
                      <div>
                        <p className="text-white font-medium">Remote Session</p>
                        <p className="text-sm text-slate-400">
                          Connected to your device using RustDesk
                        </p>
                        <p className="text-xs text-slate-500 mt-1">Today, 10:42 AM</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-4">
                      <div className="bg-green-500/20 p-2 rounded-full">
                        <CheckCircleIcon className="h-5 w-5 text-green-400" />
                      </div>
                      <div>
                        <p className="text-white font-medium">Device Check-In</p>
                        <p className="text-sm text-slate-400">
                          Your device checked in successfully
                        </p>
                        <p className="text-xs text-slate-500 mt-1">Yesterday, 3:15 PM</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Subscription & Support Section */}
            <div className="space-y-6">
              {/* Subscription Status */}
              <Card className="border-slate-800 bg-slate-900/50 backdrop-blur-sm shadow-lg">
                <CardHeader className="border-b border-slate-800 pb-4">
                  <CardTitle className="text-xl font-bold text-white">Subscription</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  {subscriptionLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                  ) : subscription && subscription.status === 'active' ? (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <CheckCircleIcon className="h-5 w-5 text-green-400" />
                        <span className="text-green-400 font-medium">Active</span>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Plan:</span>
                          <span className="text-white capitalize">
                            {subscription.planType.replace('-', ' ')}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Expires:</span>
                          <span className="text-white">
                            {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Days remaining:</span>
                          <span className="text-white font-medium">
                            {subscription.daysRemaining} days
                          </span>
                        </div>
                      </div>
                      <Link href="/subscription">
                        <Button variant="outline" className="w-full border-slate-700">
                          Manage Subscription
                        </Button>
                      </Link>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <ExclamationCircleIcon className="h-5 w-5 text-yellow-400" />
                        <span className="text-yellow-400 font-medium">No Active Subscription</span>
                      </div>
                      <p className="text-slate-300 text-sm mb-4">
                        Choose a subscription plan to access your remote devices:
                      </p>

                      {/* Direct Subscription Options */}
                      <div className="space-y-3">
                        {/* Single User Plan */}
                        <div className="border border-slate-700 rounded-lg p-3 bg-slate-800/30">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h4 className="font-medium text-white">Single User Plan</h4>
                              <p className="text-xs text-slate-400">1 desktop + 1 mobile device</p>
                            </div>
                            <div className="text-right">
                              <div className="text-xs text-slate-500 line-through">$299</div>
                              <div className="font-bold text-green-400">$199/mo</div>
                            </div>
                          </div>
                          <Link href="/checkout?plan=single-user&coupon=SPEARMINT">
                            <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700">
                              Subscribe Now
                            </Button>
                          </Link>
                        </div>

                        {/* Two User Bundle */}
                        <div className="border border-slate-700 rounded-lg p-3 bg-slate-800/30">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h4 className="font-medium text-white">Two User Bundle</h4>
                              <p className="text-xs text-slate-400">2 users, shared management</p>
                            </div>
                            <div className="text-right">
                              <div className="font-bold text-white">$298/mo</div>
                              <div className="text-xs text-slate-400">$149 per user</div>
                            </div>
                          </div>
                          <Link href="/checkout?plan=two-user">
                            <Button size="sm" className="w-full bg-purple-600 hover:bg-purple-700">
                              Subscribe Now
                            </Button>
                          </Link>
                        </div>
                      </div>

                      <div className="pt-2 border-t border-slate-700">
                        <Link href="/pricing">
                          <Button variant="outline" className="w-full border-slate-700 text-slate-300 hover:bg-slate-800">
                            View All Plans & Details
                          </Button>
                        </Link>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Support */}
              <Card className="border-slate-800 bg-slate-900/50 backdrop-blur-sm shadow-lg">
                <CardHeader className="border-b border-slate-800 pb-4">
                  <CardTitle className="text-xl font-bold text-white">Support</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <p className="text-slate-300">
                      Need help with your devices? Our support team is here to help.
                    </p>
                    <Link href="/help">
                      <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                        <EnvelopeIcon className="h-4 w-4 mr-2" />
                        Contact Support
                      </Button>
                    </Link>
                    <div className="text-center">
                      <Link
                        href="/help"
                        className="text-sm text-blue-400 hover:text-blue-300 hover:underline"
                      >
                        View Documentation
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>

      {/* Remote Control Overlay */}
      {isRemoteControlOpen && selectedDevice && (
        <ConnectionOverlay
          isOpen={isRemoteControlOpen}
          onClose={() => setIsRemoteControlOpen(false)}
        >
          <RustDeskConnection
            deviceId={selectedDevice.rustDeskId}
            deviceName={selectedDevice.name}
            password={selectedDevice.password}
          />
        </ConnectionOverlay>
      )}
    </div>
  );
}
