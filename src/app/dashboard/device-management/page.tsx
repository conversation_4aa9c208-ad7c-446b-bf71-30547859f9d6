"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ComputerDesktopIcon, DevicePhoneMobileIcon, CheckCircleIcon, ClockIcon, XCircleIcon } from "@heroicons/react/24/outline";
import DeviceSubmissionForm from "@/components/client/device-submission-form";
import TradeInForm from "@/components/client/trade-in-form";

interface Device {
  id: string;
  name: string;
  rustDeskId: string;
  deviceType: string;
  status: string;
  createdAt: string;
}

interface DeviceSubmission {
  id: string;
  deviceName: string;
  rustDeskId: string;
  deviceType: string;
  status: string;
  adminNotes?: string;
  submittedAt: string;
}

interface TradeInRequest {
  id: string;
  deviceModel: string;
  deviceCondition: string;
  estimatedValue: number;
  actualValue?: number;
  status: string;
  trackingNumber?: string;
  rebateAmount?: number;
  rebateApplied: boolean;
  submittedAt: string;
}

export default function DeviceManagementPage() {
  const [devices, setDevices] = useState<Device[]>([]);
  const [deviceSubmissions, setDeviceSubmissions] = useState<DeviceSubmission[]>([]);
  const [tradeInRequests, setTradeInRequests] = useState<TradeInRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [hasDesktopDevice, setHasDesktopDevice] = useState(false);
  const [hasPendingSubmission, setHasPendingSubmission] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load user devices
      const devicesResponse = await fetch("/api/devices");
      if (devicesResponse.ok) {
        const devicesData = await devicesResponse.json();
        setDevices(devicesData.devices || []);
        
        // Check if user has a desktop device
        const hasDesktop = devicesData.devices?.some((device: Device) => 
          device.deviceType === "desktop" && device.status === "active"
        );
        setHasDesktopDevice(hasDesktop);
      }

      // Load device submissions
      const submissionsResponse = await fetch("/api/device-submissions");
      if (submissionsResponse.ok) {
        const submissionsData = await submissionsResponse.json();
        setDeviceSubmissions(submissionsData.submissions || []);
        
        // Check if user has pending submissions
        const hasPending = submissionsData.submissions?.some((submission: DeviceSubmission) => 
          submission.status === "pending"
        );
        setHasPendingSubmission(hasPending);
      }

      // Load trade-in requests
      const tradeInResponse = await fetch("/api/trade-in-requests");
      if (tradeInResponse.ok) {
        const tradeInData = await tradeInResponse.json();
        setTradeInRequests(tradeInData.requests || []);
      }
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800", icon: ClockIcon },
      active: { color: "bg-green-100 text-green-800", icon: CheckCircleIcon },
      approved: { color: "bg-green-100 text-green-800", icon: CheckCircleIcon },
      rejected: { color: "bg-red-100 text-red-800", icon: XCircleIcon },
      inactive: { color: "bg-gray-100 text-gray-800", icon: XCircleIcon },
      shipped: { color: "bg-blue-100 text-blue-800", icon: ClockIcon },
      received: { color: "bg-purple-100 text-purple-800", icon: ClockIcon },
      processed: { color: "bg-indigo-100 text-indigo-800", icon: ClockIcon },
      completed: { color: "bg-green-100 text-green-800", icon: CheckCircleIcon },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <Icon className="w-3 h-3" />
        <span>{status.charAt(0).toUpperCase() + status.slice(1)}</span>
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading device management...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Device Management</h1>
        <p className="text-muted-foreground mt-2">
          Register your devices and manage trade-in requests
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <ComputerDesktopIcon className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Desktop Devices</p>
                <p className="text-2xl font-bold">{devices.filter(d => d.deviceType === "desktop").length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <DevicePhoneMobileIcon className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Mobile Devices</p>
                <p className="text-2xl font-bold">{devices.filter(d => d.deviceType === "mobile").length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <ClockIcon className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="text-sm text-muted-foreground">Pending Submissions</p>
                <p className="text-2xl font-bold">{deviceSubmissions.filter(s => s.status === "pending").length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <div>
                <p className="text-sm text-muted-foreground">Trade-In Requests</p>
                <p className="text-2xl font-bold">{tradeInRequests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="devices" className="space-y-6">
        <TabsList>
          <TabsTrigger value="devices">My Devices</TabsTrigger>
          <TabsTrigger value="register">Register Device</TabsTrigger>
          <TabsTrigger value="trade-in">Trade-In Program</TabsTrigger>
        </TabsList>

        <TabsContent value="devices">
          <div className="space-y-6">
            {/* Active Devices */}
            <Card>
              <CardHeader>
                <CardTitle>Active Devices</CardTitle>
                <CardDescription>
                  Your registered and approved devices
                </CardDescription>
              </CardHeader>
              <CardContent>
                {devices.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No active devices found. Register your first device to get started.
                  </div>
                ) : (
                  <div className="space-y-4">
                    {devices.map((device) => (
                      <div key={device.id} className="border rounded-lg p-4 flex justify-between items-center">
                        <div className="space-y-1">
                          <h3 className="font-semibold flex items-center space-x-2">
                            {device.deviceType === "desktop" ? (
                              <ComputerDesktopIcon className="w-4 h-4" />
                            ) : (
                              <DevicePhoneMobileIcon className="w-4 h-4" />
                            )}
                            <span>{device.name}</span>
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            RustDesk ID: {device.rustDeskId}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Type: {device.deviceType}
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          {getStatusBadge(device.status)}
                          {device.status === "active" && (
                            <Button size="sm" variant="outline">
                              Connect
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Device Submissions */}
            {deviceSubmissions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Device Submissions</CardTitle>
                  <CardDescription>
                    Track the status of your device registration requests
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {deviceSubmissions.map((submission) => (
                      <div key={submission.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="space-y-1">
                            <h3 className="font-semibold">{submission.deviceName}</h3>
                            <p className="text-sm text-muted-foreground">
                              RustDesk ID: {submission.rustDeskId}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Submitted: {new Date(submission.submittedAt).toLocaleDateString()}
                            </p>
                          </div>
                          {getStatusBadge(submission.status)}
                        </div>
                        {submission.adminNotes && (
                          <div className="mt-3 bg-gray-50 p-3 rounded">
                            <p className="text-sm">
                              <span className="font-medium">Admin Notes:</span> {submission.adminNotes}
                            </p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="register">
          <div className="space-y-6">
            {hasDesktopDevice || hasPendingSubmission ? (
              <Alert>
                <CheckCircleIcon className="h-4 w-4" />
                <AlertDescription>
                  {hasDesktopDevice 
                    ? "You already have a registered desktop device. Each user can only register one desktop device."
                    : "You have a pending device submission. Please wait for admin approval before submitting another device."
                  }
                </AlertDescription>
              </Alert>
            ) : (
              <DeviceSubmissionForm onSubmissionComplete={loadDashboardData} />
            )}
          </div>
        </TabsContent>

        <TabsContent value="trade-in">
          <div className="space-y-6">
            <TradeInForm onSubmissionComplete={loadDashboardData} />
            
            {/* Trade-In Requests */}
            {tradeInRequests.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Your Trade-In Requests</CardTitle>
                  <CardDescription>
                    Track the status of your Samsung A14 trade-in requests
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {tradeInRequests.map((request) => (
                      <div key={request.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div className="space-y-1">
                            <h3 className="font-semibold">{request.deviceModel}</h3>
                            <p className="text-sm text-muted-foreground">
                              Condition: {request.deviceCondition}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Estimated Value: ${request.estimatedValue}
                            </p>
                            {request.actualValue && (
                              <p className="text-sm text-muted-foreground">
                                Actual Value: ${request.actualValue}
                              </p>
                            )}
                            {request.rebateAmount && (
                              <p className="text-sm text-green-600">
                                Rebate: ${request.rebateAmount}
                                {request.rebateApplied && " (Applied)"}
                              </p>
                            )}
                          </div>
                          {getStatusBadge(request.status)}
                        </div>
                        {request.trackingNumber && (
                          <div className="mt-3 bg-blue-50 p-3 rounded">
                            <p className="text-sm">
                              <span className="font-medium">Tracking Number:</span> {request.trackingNumber}
                            </p>
                          </div>
                        )}
                        <p className="text-xs text-muted-foreground mt-2">
                          Submitted: {new Date(request.submittedAt).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
