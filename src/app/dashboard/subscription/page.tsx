"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import {
  CreditCardIcon,
  DocumentTextIcon,
  DevicePhoneMobileIcon,
  CheckCircleIcon,
  XMarkIcon,
  ArrowPathIcon,
  ClockIcon,
  ExclamationCircleIcon,
  QuestionMarkCircleIcon,
  CheckIcon,
  TruckIcon,
  LinkIcon
} from "@heroicons/react/24/outline";
import { useOrderStatusStream } from "@/hooks/useOrderStatusStream";
import { DeviceConnectionStatus } from "@/components/customer/device-connection-status";
import { ShippingTracker } from "@/components/customer/shipping-tracker";
import Link from "next/link";
import PaymentMethodForm from "@/components/payment/PaymentMethodForm";
import {
  getClientSubscription,
  getClientBillingHistory,
  getAvailablePlans,
  ClientSubscription,
  BillingHistoryItem,
  SubscriptionPlan
} from "@/app/actions/client-subscription";



interface Order {
  id: string;
  subscriptionPlan: string;
  amount: number;
  status: string;
  shippingAddress: any;
  trackingNumber?: string;
  createdAt: string;
  updatedAt: string;
  device?: {
    id: string;
    name: string;
    model?: string;
    deviceType: string;
    status: string;
    rustDeskId?: string;
  };
  statusHistory: Array<{
    id: string;
    status: string;
    notes?: string;
    createdAt: string;
  }>;
}

export default function SubscriptionPage() {
  const [subscription, setSubscription] = useState<ClientSubscription | null>(null);
  const [billingHistory, setBillingHistory] = useState<BillingHistoryItem[]>([]);
  const [availablePlans, setAvailablePlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [order, setOrder] = useState<Order | null>(null);
  const [realTimeConnected, setRealTimeConnected] = useState(false);

  // Load subscription data on component mount
  useEffect(() => {
    async function loadSubscriptionData() {
      try {
        setLoading(true);
        setError(null);

        // Get client subscription
        const subscriptionData = await getClientSubscription();
        setSubscription(subscriptionData);

        // Get billing history
        const billingHistoryData = await getClientBillingHistory();
        setBillingHistory(billingHistoryData);

        // Get available plans
        const plansData = await getAvailablePlans();
        setAvailablePlans(plansData);

        // Get order status for post-payment experience
        try {
          const orderResponse = await fetch('/api/user/subscription-status');
          if (orderResponse.ok) {
            const orderData = await orderResponse.json();
            setOrder(orderData.order);
          }
        } catch (orderErr) {
          console.error("Error loading order data:", orderErr);
          // Don't fail the whole page if order data fails
        }

        setLoading(false);
      } catch (err) {
        console.error("Error loading subscription data:", err);
        setError("Failed to load subscription data. Please try again.");
        setLoading(false);
      }
    }

    loadSubscriptionData();
  }, []);

  // Real-time status updates for order
  const { isConnected: streamConnected, error: streamError } = useOrderStatusStream({
    orderId: order?.id || '',
    enabled: !!order?.id,
    onUpdate: (update) => {
      if (update.type === 'order_update' && order) {
        // Update order state with real-time data
        setOrder(prevOrder => {
          if (!prevOrder) return prevOrder;
          return {
            ...prevOrder,
            status: update.status || prevOrder.status,
            trackingNumber: update.trackingNumber || prevOrder.trackingNumber,
            updatedAt: update.updatedAt || prevOrder.updatedAt,
          };
        });

        // Show toast notification for status changes
        if (update.status && update.status !== order.status) {
          const statusMessages = {
            device_assigned: '📱 Device has been assigned to your order!',
            shipped: '🚚 Your device has been shipped!',
            delivered: '📦 Your device has been delivered!',
            connected: '🔗 Your device is being connected!',
            active: '✅ Your SPEAR service is now active!',
          };

          const message = statusMessages[update.status as keyof typeof statusMessages];
          if (message) {
            toast.success(message);
          }
        }
      }
    },
    onConnect: () => {
      setRealTimeConnected(true);
    },
    onDisconnect: () => {
      setRealTimeConnected(false);
    },
    onError: (error) => {
      console.error('Real-time status error:', error);
    },
  });

  // Helper functions for order status display
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'device_prep':
        return <ClockIcon className="h-6 w-6 text-blue-500" />;
      case 'device_assigned':
        return <DevicePhoneMobileIcon className="h-6 w-6 text-purple-500" />;
      case 'shipped':
        return <TruckIcon className="h-6 w-6 text-orange-500" />;
      case 'delivered':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'connected':
      case 'active':
        return <LinkIcon className="h-6 w-6 text-green-500" />;
      default:
        return <ClockIcon className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'device_prep':
        return 'Device Being Prepared';
      case 'device_assigned':
        return 'Device Assigned';
      case 'shipped':
        return 'Device Shipped';
      case 'delivered':
        return 'Device Delivered';
      case 'connected':
        return 'Device Connected';
      case 'active':
        return 'Service Active';
      default:
        return 'Processing';
    }
  };

  const getStatusDescription = (status: string) => {
    switch (status) {
      case 'device_prep':
        return 'Your mobile device is being configured with secure access credentials';
      case 'device_assigned':
        return 'A device has been assigned and is being prepared for shipment';
      case 'shipped':
        return 'Your device is on its way to your address';
      case 'delivered':
        return 'Device has been delivered to your location';
      case 'connected':
        return 'Device is connected and being activated';
      case 'active':
        return 'Your SPEAR service is fully active and ready to use';
      default:
        return 'Your order is being processed';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleUpgrade = async (planId: string) => {
    setIsLoading(true);

    try {
      // Check if subscription and payment method exist
      if (!subscription) {
        toast.error("Subscription data not available");
        setIsLoading(false);
        return;
      }

      // Check if user has a payment method
      const hasPaymentMethod = subscription.paymentMethod && subscription.paymentMethod.last4;

      if (!hasPaymentMethod) {
        // Show payment method form
        toast.error("Please add a payment method before upgrading");
        document.querySelector('[data-value="payment"]')?.click();
        setIsLoading(false);
        return;
      }

      // Call API to upgrade subscription
      // In a real implementation, this would call your backend API
      // For now, we'll simulate the API call
      setTimeout(async () => {
        try {
          // Refresh data after upgrade
          const subscriptionData = await getClientSubscription();
          setSubscription(subscriptionData);

          const plansData = await getAvailablePlans();
          setAvailablePlans(plansData);

          toast.success(`Upgraded to ${availablePlans.find(p => p.id === planId)?.name}`);
        } catch (err) {
          console.error("Error refreshing subscription data:", err);
        }

        setIsLoading(false);
      }, 1500);
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      toast.error('Failed to upgrade subscription');
      setIsLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    setIsLoading(true);

    try {
      // Check if subscription exists
      if (!subscription) {
        toast.error("Subscription data not available");
        setIsLoading(false);
        return;
      }

      // Simulate API call
      setTimeout(async () => {
        try {
          // Refresh data after cancellation
          const subscriptionData = await getClientSubscription();
          setSubscription(subscriptionData);

          toast.success("Subscription cancellation request submitted");
        } catch (err) {
          console.error("Error refreshing subscription data:", err);
        }

        setIsLoading(false);
      }, 1500);
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast.error('Failed to cancel subscription');
      setIsLoading(false);
    }
  };

  const handleUpdatePaymentMethod = async () => {
    setIsLoading(true);

    try {
      // Check if subscription exists
      if (!subscription) {
        toast.error("Subscription data not available");
        setIsLoading(false);
        return;
      }

      // Simulate API call
      setTimeout(async () => {
        try {
          // Refresh data after payment method update
          const subscriptionData = await getClientSubscription();
          setSubscription(subscriptionData);

          toast.success("Payment method updated successfully");
        } catch (err) {
          console.error("Error refreshing subscription data:", err);
        }

        setIsLoading(false);
      }, 1500);
    } catch (error) {
      console.error('Error updating payment method:', error);
      toast.error('Failed to update payment method');
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Subscription Management</h1>
        <p className="text-muted-foreground">
          Manage your subscription, billing, and payment information
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
          <div className="flex">
            <ExclamationCircleIcon className="h-5 w-5 text-red-500 mr-2" />
            <span>{error}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </div>
      )}

      {loading && !subscription && (
        <div className="flex items-center justify-center p-12">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            <p className="mt-4 text-slate-500">Loading subscription data...</p>
          </div>
        </div>
      )}

      {!loading && !subscription && !error && (
        <div className="bg-amber-50 border border-amber-200 text-amber-800 rounded-md p-6 text-center">
          <ExclamationCircleIcon className="h-12 w-12 text-amber-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No Active Subscription</h3>
          <p className="mb-4">You don't have an active subscription. Please contact support to set up your subscription.</p>
          <Button asChild>
            <Link href="/help">Contact Support</Link>
          </Button>
        </div>
      )}

      {/* Post-Payment Experience - Device Fulfillment Progress */}
      {order && (
        <div className="space-y-6 mb-8">
          {/* Welcome Header */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CheckCircleIcon className="h-8 w-8 text-green-600 mr-3" />
                <div>
                  <h2 className="text-2xl font-bold text-green-800">
                    🎉 Welcome to SPEAR! Your subscription is active.
                  </h2>
                  <p className="text-green-700 mt-1">
                    Thank you for choosing SPEAR {order.subscriptionPlan.replace('_', ' ')}. Your secure remote access service is being prepared.
                  </p>
                </div>
              </div>

              {/* Real-time status indicator */}
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${realTimeConnected ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                <span className="text-xs text-green-600">
                  {realTimeConnected ? 'Live updates' : 'Connecting...'}
                </span>
              </div>
            </div>
          </div>

          {/* Device Status Tracker */}
          <Card>
            <CardHeader>
              <CardTitle>Device Setup Progress</CardTitle>
              <CardDescription>
                Track the progress of your SPEAR device preparation and delivery
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Payment Confirmed */}
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm mr-4">
                    ✓
                  </div>
                  <div className="flex-1">
                    <p className="font-semibold">Payment Confirmed</p>
                    <p className="text-sm text-gray-600">Your subscription is active</p>
                  </div>
                  <p className="text-sm text-gray-500">{formatDate(order.createdAt)}</p>
                </div>

                {/* Current Status */}
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm mr-4">
                    {getStatusIcon(order.status)}
                  </div>
                  <div className="flex-1">
                    <p className="font-semibold">{getStatusText(order.status)}</p>
                    <p className="text-sm text-gray-600">{getStatusDescription(order.status)}</p>
                  </div>
                  <p className="text-sm text-gray-500">{formatDate(order.updatedAt)}</p>
                </div>

                {/* Future Steps */}
                {order.status !== 'active' && (
                  <>
                    <div className="flex items-center opacity-50">
                      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-sm mr-4">
                        🚚
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold">Device Shipped</p>
                        <p className="text-sm text-gray-600">Tracking information will be provided</p>
                      </div>
                    </div>

                    <div className="flex items-center opacity-50">
                      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-sm mr-4">
                        📱
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold">Device Delivered</p>
                        <p className="text-sm text-gray-600">Device arrives at your location</p>
                      </div>
                    </div>

                    <div className="flex items-center opacity-50">
                      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-sm mr-4">
                        🔗
                      </div>
                      <div className="flex-1">
                        <p className="font-semibold">Device Connected & Ready</p>
                        <p className="text-sm text-gray-600">Full remote access available</p>
                      </div>
                    </div>
                  </>
                )}

                {/* Shipping Information */}
                {order.shippingAddress && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <h3 className="font-semibold mb-2">Shipping Address:</h3>
                    <div className="bg-gray-50 p-4 rounded">
                      <p>{order.shippingAddress.line1}</p>
                      {order.shippingAddress.line2 && <p>{order.shippingAddress.line2}</p>}
                      <p>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postal_code}</p>
                      <p>{order.shippingAddress.country}</p>
                    </div>
                    {order.trackingNumber && (
                      <div className="mt-2">
                        <p className="text-sm text-gray-600">Tracking Number: <span className="font-mono">{order.trackingNumber}</span></p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Device Information */}
          {order.device && (
            <Card>
              <CardHeader>
                <CardTitle className="text-purple-800 flex items-center">
                  <DevicePhoneMobileIcon className="h-5 w-5 mr-2" />
                  Your SPEAR Device
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-purple-800">Device Information</h4>
                      <div className="mt-2 space-y-1">
                        <p className="text-sm"><span className="font-medium">Model:</span> {order.device.model || 'Samsung Galaxy A14'}</p>
                        <p className="text-sm"><span className="font-medium">Device Name:</span> {order.device.name}</p>
                        <p className="text-sm"><span className="font-medium">Status:</span>
                          <span className={`ml-1 px-2 py-1 rounded text-xs ${
                            order.device.status === 'active' ? 'bg-green-100 text-green-800' :
                            order.device.status === 'assigned' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {order.device.status}
                          </span>
                        </p>
                      </div>
                    </div>

                    {order.device.rustDeskId && (
                      <div>
                        <h4 className="font-semibold text-purple-800">Connection Details</h4>
                        <div className="mt-2 space-y-1">
                          <p className="text-sm"><span className="font-medium">Device ID:</span>
                            <span className="ml-1 font-mono bg-white px-2 py-1 rounded border">
                              {order.device.rustDeskId}
                            </span>
                          </p>
                          <p className="text-xs text-purple-600 mt-2">
                            💡 This ID will match the device you receive. Use it to connect remotely once your device is delivered and activated.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {order.status === 'active' && order.device.rustDeskId && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                      <h4 className="font-semibold text-green-800 flex items-center">
                        <LinkIcon className="h-4 w-4 mr-1" />
                        Ready to Connect
                      </h4>
                      <p className="text-sm text-green-700 mt-1">
                        Your device is active and ready for remote access. Use the Device ID above to connect.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Shipping Tracker */}
          <ShippingTracker
            orderId={order.id}
            trackingNumber={order.trackingNumber}
            orderStatus={order.status}
          />

          {/* Device Connection Status */}
          <DeviceConnectionStatus
            device={order.device}
            orderStatus={order.status}
          />

          {/* What's Next */}
          <Card>
            <CardHeader>
              <CardTitle className="text-blue-800">What Happens Next?</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-blue-700">
                <li className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
                  Your mobile device is being prepared with your secure connection
                </li>
                <li className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
                  Once delivered, it will be ready to use immediately
                </li>
                <li className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
                  You'll receive setup instructions via email
                </li>
                <li className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
                  Our team will connect your profile to enable remote access
                </li>
                <li className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 text-blue-500 mr-2" />
                  You can contact support anytime for assistance
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      )}

      {subscription && (
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="plans">Plans & Pricing</TabsTrigger>
            <TabsTrigger value="billing">Billing History</TabsTrigger>
            <TabsTrigger value="payment">Payment Methods</TabsTrigger>
          </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Current Subscription</CardTitle>
                  <Badge className={subscription.status === "active" ? "bg-green-500" : "bg-red-500"}>
                    {subscription.status === "active" ? "Active" : "Inactive"}
                  </Badge>
                </div>
                <CardDescription>
                  Your subscription details and usage
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Plan</h3>
                    <p className="text-lg font-semibold">{subscription.plan}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Price</h3>
                    <p className="text-lg font-semibold">${subscription.price}/{subscription.billingCycle}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Billing Cycle</h3>
                    <p className="text-lg font-semibold capitalize">{subscription.billingCycle}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Next Billing Date</h3>
                    <p className="text-lg font-semibold">{subscription.nextBillingDate}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Start Date</h3>
                    <p className="text-lg font-semibold">{subscription.startDate}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Payment Method</h3>
                    <p className="text-lg font-semibold">{subscription.paymentMethod.brand} ending in {subscription.paymentMethod.last4}</p>
                  </div>
                </div>

                <Separator />

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-sm font-medium">Device Usage</h3>
                    <p className="text-sm text-muted-foreground">{subscription.devices} of {subscription.maxDevices} devices</p>
                  </div>
                  <Progress value={(subscription.devices / subscription.maxDevices) * 100} className="h-2" />
                </div>

                <Separator />

                <div>
                  <h3 className="text-sm font-medium mb-4">Included Features</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {subscription.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        {feature.included ? (
                          <CheckIcon className="h-5 w-5 text-green-500" />
                        ) : (
                          <XMarkIcon className="h-5 w-5 text-muted-foreground" />
                        )}
                        <span className={feature.included ? "" : "text-muted-foreground"}>
                          {feature.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => document.querySelector('[data-value="plans"]')?.click()}>
                  Upgrade Plan
                </Button>
                <Button variant="destructive" onClick={handleCancelSubscription} disabled={isLoading}>
                  {isLoading ? "Processing..." : "Cancel Subscription"}
                </Button>
              </CardFooter>
            </Card>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full justify-start" onClick={() => document.querySelector('[data-value="payment"]')?.click()}>
                    <CreditCardIcon className="h-4 w-4 mr-2" />
                    Update Payment Method
                  </Button>
                  <Button variant="outline" className="w-full justify-start" onClick={() => document.querySelector('[data-value="billing"]')?.click()}>
                    <DocumentTextIcon className="h-4 w-4 mr-2" />
                    View Billing History
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href="/help">
                      <QuestionMarkCircleIcon className="h-4 w-4 mr-2" />
                      Get Support
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Upcoming Payment</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <ClockIcon className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="font-medium">${subscription.price}.00</p>
                        <p className="text-sm text-muted-foreground">Due on {subscription.nextBillingDate}</p>
                      </div>
                    </div>
                    <Badge variant="outline">Scheduled</Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Your card ending in {subscription.paymentMethod.last4} will be charged automatically.
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Plans & Pricing Tab */}
        <TabsContent value="plans" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {availablePlans.map((plan) => (
              <Card key={plan.id} className={plan.current ? "border-primary" : ""}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>{plan.name}</CardTitle>
                    {plan.current && (
                      <Badge>Current Plan</Badge>
                    )}
                  </div>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-baseline">
                    <span className="text-3xl font-bold">${plan.price}</span>
                    <span className="text-muted-foreground ml-1">/{plan.billingCycle}</span>
                  </div>
                  <Separator />
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    variant={plan.current ? "outline" : "default"}
                    disabled={plan.current || isLoading}
                    onClick={() => handleUpgrade(plan.id)}
                  >
                    {isLoading ? "Processing..." : plan.current ? "Current Plan" : "Upgrade"}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Plan Comparison</CardTitle>
              <CardDescription>
                Compare features across different plans
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Feature</TableHead>
                    <TableHead>Basic</TableHead>
                    <TableHead>Professional</TableHead>
                    <TableHead>Enterprise</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Devices</TableCell>
                    <TableCell>Up to 5</TableCell>
                    <TableCell>Up to 15</TableCell>
                    <TableCell>Unlimited</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Remote Access</TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Location Verification</TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Session Recording</TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Priority Support</TableCell>
                    <TableCell><XMarkIcon className="h-5 w-5 text-muted-foreground" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Custom Branding</TableCell>
                    <TableCell><XMarkIcon className="h-5 w-5 text-muted-foreground" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">API Access</TableCell>
                    <TableCell><XMarkIcon className="h-5 w-5 text-muted-foreground" /></TableCell>
                    <TableCell><XMarkIcon className="h-5 w-5 text-muted-foreground" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Dedicated Account Manager</TableCell>
                    <TableCell><XMarkIcon className="h-5 w-5 text-muted-foreground" /></TableCell>
                    <TableCell><XMarkIcon className="h-5 w-5 text-muted-foreground" /></TableCell>
                    <TableCell><CheckIcon className="h-5 w-5 text-green-500" /></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
              <CardDescription>
                View and download your past invoices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {billingHistory.map((invoice) => (
                    <TableRow key={invoice.id}>
                      <TableCell className="font-medium">{invoice.id}</TableCell>
                      <TableCell>{invoice.date}</TableCell>
                      <TableCell>${invoice.amount.toFixed(2)}</TableCell>
                      <TableCell>
                        <Badge className={invoice.status === "paid" ? "bg-green-500" : "bg-amber-500"}>
                          {invoice.status === "paid" ? "Paid" : "Pending"}
                        </Badge>
                      </TableCell>
                      <TableCell>{invoice.description}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm">
                          Download
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Billing Address</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <p className="font-medium">John Doe</p>
                  <p>123 Main Street</p>
                  <p>Apt 4B</p>
                  <p>New York, NY 10001</p>
                  <p>United States</p>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline">Update Address</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Billing Contact</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <p className="font-medium">John Doe</p>
                  <p><EMAIL></p>
                  <p>+1 (555) 123-4567</p>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline">Update Contact</Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="payment" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
              <CardDescription>
                Manage your payment methods
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center text-white">
                      <CreditCardIcon className="h-6 w-6" />
                    </div>
                    <div>
                      <p className="font-medium">{subscription.paymentMethod.brand} ending in {subscription.paymentMethod.last4}</p>
                      <p className="text-sm text-muted-foreground">Expires {subscription.paymentMethod.expMonth}/{subscription.paymentMethod.expYear}</p>
                    </div>
                  </div>
                  <Badge>Default</Badge>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setShowPaymentForm(true)}
              >
                Add Payment Method
              </Button>
              <Button
                onClick={handleUpdatePaymentMethod}
                disabled={isLoading}
              >
                {isLoading ? "Updating..." : "Update Card"}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Payment Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Auto-Renew Subscription</h3>
                  <p className="text-sm text-muted-foreground">
                    Automatically renew your subscription when it expires
                  </p>
                </div>
                <div className="flex h-6 items-center space-x-2">
                  <div className="h-4 w-8 rounded-full bg-green-500 flex items-center">
                    <div className="h-3 w-3 rounded-full bg-white ml-auto mr-0.5"></div>
                  </div>
                  <span className="text-sm">On</span>
                </div>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Email Receipts</h3>
                  <p className="text-sm text-muted-foreground">
                    Receive email receipts for all payments
                  </p>
                </div>
                <div className="flex h-6 items-center space-x-2">
                  <div className="h-4 w-8 rounded-full bg-green-500 flex items-center">
                    <div className="h-3 w-3 rounded-full bg-white ml-auto mr-0.5"></div>
                  </div>
                  <span className="text-sm">On</span>
                </div>
              </div>
              <Separator />
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Payment Reminders</h3>
                  <p className="text-sm text-muted-foreground">
                    Receive reminders before payments are due
                  </p>
                </div>
                <div className="flex h-6 items-center space-x-2">
                  <div className="h-4 w-8 rounded-full bg-green-500 flex items-center">
                    <div className="h-3 w-3 rounded-full bg-white ml-auto mr-0.5"></div>
                  </div>
                  <span className="text-sm">On</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline">Save Settings</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
      )}

      {/* Stripe Payment Method Form */}
      <PaymentMethodForm
        open={showPaymentForm}
        onClose={() => setShowPaymentForm(false)}
        onSuccess={async () => {
          setShowPaymentForm(false);

          try {
            // Refresh subscription data after adding payment method
            const subscriptionData = await getClientSubscription();
            setSubscription(subscriptionData);

            toast.success("Payment method added successfully");
          } catch (err) {
            console.error("Error refreshing subscription data:", err);
            toast.success("Payment method added successfully, but failed to refresh data");
          }
        }}
      />
    </div>
  );
}
