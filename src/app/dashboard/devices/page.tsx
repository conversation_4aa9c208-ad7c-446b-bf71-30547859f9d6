"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON>alog<PERSON><PERSON><PERSON>, <PERSON><PERSON>T<PERSON>ger, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DevicePhoneMobileIcon, PencilIcon, ClockIcon, SignalIcon, PlusIcon, ExclamationCircleIcon } from "@heroicons/react/24/outline";
import { getUserDevices, updateDeviceName, canUserAddDevices } from "@/lib/client-dashboard";
import { useSession } from "next-auth/react";

// Device type definition
interface Device {
  id: string;
  name: string;
  deviceModel: string;
  deviceId: string;
  status: string;
  lastCheckIn: string;
}

export default function DevicesPage() {
  const { data: session } = useSession();
  const [devices, setDevices] = useState<Device[]>([]);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [currentDevice, setCurrentDevice] = useState<Device | null>(null);
  const [newDeviceName, setNewDeviceName] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [canAddDevice, setCanAddDevice] = useState(false);

  // Load user devices on component mount
  useEffect(() => {
    async function loadUserDevices() {
      if (!session?.user?.id) {
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Get user devices
        const userDevices = await getUserDevices(session.user.id);
        setDevices(userDevices);

        // Check if user can add more devices
        const canAdd = await canUserAddDevices(session.user.id);
        setCanAddDevice(canAdd);

        setLoading(false);
      } catch (err) {
        console.error("Error loading user devices:", err);
        setError("Failed to load your devices. Please try again.");
        setLoading(false);
      }
    }

    loadUserDevices();
  }, [session]);

  const handleEditDevice = (device: Device) => {
    setCurrentDevice(device);
    setNewDeviceName(device.name);
    setIsEditDialogOpen(true);
  };

  const handleSaveDeviceName = async () => {
    if (!currentDevice || !newDeviceName.trim() || !session?.user?.id) return;

    try {
      setLoading(true);

      // Call the API to update the device name
      const updatedDevice = await updateDeviceName(
        currentDevice.id,
        newDeviceName.trim(),
        session.user.id
      );

      // Update device name in our state
      setDevices(devices.map(device =>
        device.id === currentDevice.id
          ? updatedDevice
          : device
      ));

      setIsEditDialogOpen(false);
      setLoading(false);
    } catch (err) {
      console.error("Error updating device name:", err);
      setError("Failed to update device name. Please try again.");
      setIsEditDialogOpen(false);
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">My Devices</h1>
        <p className="text-slate-500 dark:text-slate-400">Manage your connected devices</p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
          <div className="flex">
            <ExclamationCircleIcon className="h-5 w-5 text-red-500 mr-2" />
            <span>{error}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => window.location.reload()}
          >
            Retry
          </Button>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {loading ? (
          // Loading skeleton cards
          Array.from({ length: 2 }).map((_, index) => (
            <Card key={`loading-${index}`} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="h-7 w-40 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                  <div className="h-8 w-8 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                </div>
                <div className="h-5 w-32 bg-slate-200 dark:bg-slate-700 rounded animate-pulse mt-2"></div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 pt-1">
                  <div className="h-5 w-full bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                  <div className="h-5 w-full bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                </div>
                <div className="pt-2">
                  <div className="h-10 w-full bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : devices.length === 0 ? (
          <Card className="col-span-full">
            <CardContent className="flex flex-col items-center justify-center py-10">
              <DevicePhoneMobileIcon className="h-12 w-12 text-slate-400 mb-4" />
              <h3 className="text-lg font-medium mb-1">No Devices Found</h3>
              <p className="text-sm text-slate-500 text-center mb-4">
                You don't have any devices assigned to your account yet.
              </p>
              <Button variant="outline">Contact Support</Button>
            </CardContent>
          </Card>
        ) : (
          // Actual device cards
          devices.map((device) => (
            <Card key={device.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-xl flex items-center">
                    <DevicePhoneMobileIcon className="h-5 w-5 mr-2" />
                    {device.name}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleEditDevice(device)}
                    disabled={loading}
                  >
                    <PencilIcon className="h-4 w-4" />
                  </Button>
                </div>
                <CardDescription className="flex items-center">
                  <span className="text-xs text-slate-500">{device.deviceModel}</span>
                  <span className="mx-2 text-slate-300">•</span>
                  <span className={`font-medium flex items-center ${device.status === 'online' ? 'text-green-500' : 'text-slate-500'}`}>
                    <span className={`h-2 w-2 rounded-full mr-1.5 ${device.status === 'online' ? 'bg-green-500' : 'bg-slate-400'}`}></span>
                    {device.status === 'online' ? 'Online' : 'Offline'}
                  </span>
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3 pt-1">
                  <div className="flex items-center text-sm text-slate-500">
                    <SignalIcon className={`h-4 w-4 mr-2 ${device.status === 'online' ? 'text-green-500' : 'text-slate-400'}`} />
                    <span className="font-medium">Connection Status:</span>
                    <span className={`ml-2 font-medium ${device.status === 'online' ? 'text-green-500' : 'text-slate-400'}`}>
                      {device.status === 'online' ? 'Ready' : 'Not Available'}
                    </span>
                  </div>
                  <div className="flex items-center text-sm text-slate-500">
                    <ClockIcon className="h-4 w-4 mr-2" />
                    <span className="font-medium">Last Activity:</span>
                    <span className="ml-2">{new Date(device.lastCheckIn).toLocaleString()}</span>
                  </div>
                </div>
                <div className="pt-2">
                  <Button
                    className="w-full h-10 font-medium"
                    onClick={() => window.location.href = "/dashboard"}
                    disabled={device.status !== 'online' || loading}
                  >
                    Connect Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}

        {/* Add New Device Card - Only show if subscription allows */}
        {!loading && canAddDevice && (
          <Card className="border-dashed border-2 border-slate-200 dark:border-slate-800 bg-transparent">
            <CardContent className="flex flex-col items-center justify-center h-full py-10">
              <div className="rounded-full bg-slate-100 dark:bg-slate-800 p-3 mb-4">
                <PlusIcon className="h-6 w-6 text-slate-500" />
              </div>
              <h3 className="text-lg font-medium mb-1">Add New Device</h3>
              <p className="text-sm text-slate-500 text-center mb-4">
                Contact support to add another device to your subscription
              </p>
              <Button variant="outline">Contact Support</Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Edit Device Name Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        if (!loading) {
          setIsEditDialogOpen(open);
        }
      }}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Rename Device</DialogTitle>
            <DialogDescription>
              Give your device a name that helps you identify it easily.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="deviceName">Device Name</Label>
              <Input
                id="deviceName"
                value={newDeviceName}
                onChange={(e) => setNewDeviceName(e.target.value)}
                placeholder="Enter a name for this device"
                disabled={loading}
              />
            </div>
            <div className="text-sm text-slate-500">
              <p>Device ID: {currentDevice?.deviceId}</p>
              <p>Model: {currentDevice?.deviceModel}</p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveDeviceName}
              disabled={loading || !newDeviceName.trim()}
            >
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-slate-200 border-t-slate-800"></div>
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
