"use client";

import { useEffect, useState, useRef, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';

function ProtectedRustDeskViewerContent() {
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const deviceId = searchParams.get('device');
  const token = searchParams.get('token');

  useEffect(() => {
    if (!session?.user?.email) {
      setError('Please log in to access your devices');
      return;
    }

    if (!deviceId || !token) {
      setError('Invalid access parameters');
      return;
    }

    verifyAccess();
  }, [session, deviceId, token]);

  const verifyAccess = async () => {
    try {
      const response = await fetch('/api/client/verify-access', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ deviceId, token })
      });

      if (response.ok) {
        const data = await response.json();
        setDeviceInfo(data.device);
        setIsAuthorized(true);
        
        // Load RustDesk web client after authorization
        loadRustDeskViewer();
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Access denied');
      }
    } catch (error) {
      console.error('Access verification failed:', error);
      setError('Failed to verify access. Please try again.');
    }
  };

  const loadRustDeskViewer = () => {
    if (!iframeRef.current) return;

    // Load RustDesk web client with pre-configured server settings
    const rustdeskUrl = `${process.env.NEXT_PUBLIC_RUSTDESK_SERVER_IP}:${process.env.NEXT_PUBLIC_RUSTDESK_API_PORT}/static/index.html`;
    
    // Set iframe source to RustDesk web client
    iframeRef.current.src = `http://${rustdeskUrl}`;
    
    // Post configuration message to RustDesk iframe when loaded
    iframeRef.current.onload = () => {
      const config = {
        server: process.env.NEXT_PUBLIC_RUSTDESK_SERVER_IP,
        port: process.env.NEXT_PUBLIC_RUSTDESK_SERVER_PORT,
        key: process.env.NEXT_PUBLIC_RUSTDESK_SERVER_KEY,
        deviceId: deviceId,
        autoConnect: true
      };
      
      // Send configuration to RustDesk web client
      iframeRef.current?.contentWindow?.postMessage({
        type: 'SPEAR_CONFIG',
        config
      }, '*');
    };
  };

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
            <p className="mt-1 text-sm text-gray-500">{error}</p>
            <div className="mt-6">
              <button
                onClick={() => window.close()}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Close Window
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-sm text-gray-600">Verifying access...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-screen bg-black">
      {/* SPEAR Header Bar */}
      <div className="bg-blue-600 text-white px-4 py-2 flex items-center justify-between text-sm">
        <div className="flex items-center space-x-4">
          <span className="font-semibold">SPEAR Remote Access</span>
          <span>•</span>
          <span>{deviceInfo?.name || `Device ${deviceId}`}</span>
          <span className="bg-green-500 px-2 py-1 rounded text-xs">Connected</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs opacity-75">Secure Connection</span>
          <button
            onClick={() => window.close()}
            className="bg-red-500 hover:bg-red-600 px-3 py-1 rounded text-xs"
          >
            Disconnect
          </button>
        </div>
      </div>

      {/* RustDesk Web Client Iframe */}
      <iframe
        ref={iframeRef}
        className="w-full h-full border-0"
        style={{ height: 'calc(100vh - 40px)' }}
        title="SPEAR Remote Desktop"
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-pointer-lock"
      />
    </div>
  );
}

export default function ProtectedRustDeskViewer() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <ProtectedRustDeskViewerContent />
    </Suspense>
  );
}
