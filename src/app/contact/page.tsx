"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { MetaTags } from "@/components/seo/meta-tags";
import {
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  QuestionMarkCircleIcon,
} from "@heroicons/react/24/outline";

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    subject: '',
    message: '',
    inquiryType: 'general',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // TODO: Implement actual form submission
      // For now, simulate submission
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setIsSubmitted(true);
      setFormData({
        name: '',
        email: '',
        company: '',
        subject: '',
        message: '',
        inquiryType: 'general',
      });
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <>
      <MetaTags
        title="Contact SPEAR - Get in Touch with Our Team"
        description="Contact SPEAR for sales inquiries, technical support, or general questions. We're here to help with your remote device management needs."
        keywords="contact SPEAR, support, sales, help, customer service"
        ogType="website"
      />

      <div className="flex flex-col min-h-screen">
        {/* Navigation Header */}
        <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <Link href="/" className="flex items-center space-x-2">
                <Image
                  src="/images/spear-logo.PNG"
                  alt="SPEAR Logo"
                  width={32}
                  height={32}
                  className="object-contain"
                />
                <span className="text-xl font-bold">SPEAR</span>
              </Link>
              <nav className="hidden md:flex items-center space-x-6">
                <Link href="/pricing" className="text-sm font-medium hover:text-primary">
                  Pricing
                </Link>
                <Link href="/faq" className="text-sm font-medium hover:text-primary">
                  FAQ
                </Link>
                <Link href="/blog" className="text-sm font-medium hover:text-primary">
                  Blog
                </Link>
                <Link href="/contact" className="text-sm font-medium hover:text-primary text-primary">
                  Contact
                </Link>
              </nav>
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/login">Sign In</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/pricing">Get Started</Link>
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary-foreground/5 z-0"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Get in Touch
              </h1>
              <p className="text-xl md:text-2xl text-muted-foreground mb-8">
                We're here to help you succeed with SPEAR. Reach out to our team for any questions or support.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Information */}
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <EnvelopeIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-bold mb-2">Email Support</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    Get help from our support team
                  </p>
                  <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                    <EMAIL>
                  </a>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <ChatBubbleLeftRightIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-bold mb-2">Sales Inquiries</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    Questions about pricing or plans
                  </p>
                  <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                    <EMAIL>
                  </a>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <ClockIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-bold mb-2">Response Time</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    We typically respond within
                  </p>
                  <p className="text-primary font-medium">24 hours</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="p-6">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-4">
                    <QuestionMarkCircleIcon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-bold mb-2">Help Center</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    Find answers instantly
                  </p>
                  <Link href="/support" className="text-primary hover:underline">
                    Visit Support Center
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Contact Form */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Form */}
                <div>
                  <h2 className="text-3xl font-bold mb-6">Send us a Message</h2>
                  <p className="text-muted-foreground mb-8">
                    Fill out the form below and we'll get back to you as soon as possible.
                  </p>

                  {isSubmitted ? (
                    <Card className="border-green-200 bg-green-50">
                      <CardContent className="p-6">
                        <div className="text-center">
                          <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
                            <EnvelopeIcon className="h-6 w-6 text-green-600" />
                          </div>
                          <h3 className="text-lg font-bold text-green-800 mb-2">Message Sent!</h3>
                          <p className="text-green-700">
                            Thank you for contacting us. We'll get back to you within 24 hours.
                          </p>
                          <Button 
                            variant="outline" 
                            className="mt-4"
                            onClick={() => setIsSubmitted(false)}
                          >
                            Send Another Message
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="name" className="block text-sm font-medium mb-2">
                            Full Name *
                          </label>
                          <Input
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            placeholder="Your full name"
                            required
                          />
                        </div>
                        <div>
                          <label htmlFor="email" className="block text-sm font-medium mb-2">
                            Email Address *
                          </label>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            placeholder="<EMAIL>"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label htmlFor="company" className="block text-sm font-medium mb-2">
                            Company
                          </label>
                          <Input
                            id="company"
                            name="company"
                            value={formData.company}
                            onChange={handleInputChange}
                            placeholder="Your company name"
                          />
                        </div>
                        <div>
                          <label htmlFor="inquiryType" className="block text-sm font-medium mb-2">
                            Inquiry Type
                          </label>
                          <select
                            id="inquiryType"
                            name="inquiryType"
                            value={formData.inquiryType}
                            onChange={handleInputChange}
                            className="w-full p-2 border border-gray-300 rounded-md"
                          >
                            <option value="general">General Inquiry</option>
                            <option value="sales">Sales Question</option>
                            <option value="support">Technical Support</option>
                            <option value="partnership">Partnership</option>
                            <option value="billing">Billing Question</option>
                          </select>
                        </div>
                      </div>

                      <div>
                        <label htmlFor="subject" className="block text-sm font-medium mb-2">
                          Subject *
                        </label>
                        <Input
                          id="subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleInputChange}
                          placeholder="Brief description of your inquiry"
                          required
                        />
                      </div>

                      <div>
                        <label htmlFor="message" className="block text-sm font-medium mb-2">
                          Message *
                        </label>
                        <Textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          placeholder="Please provide details about your inquiry..."
                          rows={6}
                          required
                        />
                      </div>

                      <Button type="submit" disabled={isSubmitting} className="w-full">
                        {isSubmitting ? 'Sending...' : 'Send Message'}
                      </Button>
                    </form>
                  )}
                </div>

                {/* Additional Information */}
                <div className="space-y-8">
                  <Card>
                    <CardHeader>
                      <CardTitle>Quick Links</CardTitle>
                      <CardDescription>
                        Find answers to common questions
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <Link href="/faq" className="flex items-center text-primary hover:underline">
                        <QuestionMarkCircleIcon className="h-4 w-4 mr-2" />
                        Frequently Asked Questions
                      </Link>
                      <Link href="/support" className="flex items-center text-primary hover:underline">
                        <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                        Support Center
                      </Link>
                      <Link href="/pricing" className="flex items-center text-primary hover:underline">
                        <EnvelopeIcon className="h-4 w-4 mr-2" />
                        Pricing Information
                      </Link>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Enterprise Solutions</CardTitle>
                      <CardDescription>
                        Need a custom solution for your organization?
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-4">
                        We offer enterprise-grade solutions with custom features, 
                        dedicated support, and flexible deployment options.
                      </p>
                      <Button variant="outline" asChild>
                        <a href="mailto:<EMAIL>">
                          Contact Enterprise Sales
                        </a>
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Partnership Opportunities</CardTitle>
                      <CardDescription>
                        Interested in partnering with SPEAR?
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-4">
                        We're always looking for strategic partners to help us 
                        expand our reach and provide better solutions to customers.
                      </p>
                      <Button variant="outline" asChild>
                        <a href="mailto:<EMAIL>">
                          Explore Partnerships
                        </a>
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-muted py-12 mt-auto">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-6 md:mb-0">
                <div className="flex items-center">
                  <Image
                    src="/images/spear-logo.PNG"
                    alt="SPEAR Logo"
                    width={40}
                    height={40}
                    className="mr-2"
                  />
                  <span className="text-xl font-bold">SPEAR</span>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Secure Platform for Extended Augmented Reality
                </p>
              </div>
              <div className="flex flex-wrap gap-6">
                <Link href="/pricing" className="text-sm hover:text-primary">Pricing</Link>
                <Link href="/about" className="text-sm hover:text-primary">About</Link>
                <Link href="/blog" className="text-sm hover:text-primary">Blog</Link>
                <Link href="/faq" className="text-sm hover:text-primary">FAQ</Link>
                <Link href="/contact" className="text-sm hover:text-primary">Contact</Link>
                <Link href="/privacy" className="text-sm hover:text-primary">Privacy</Link>
                <Link href="/terms" className="text-sm hover:text-primary">Terms</Link>
              </div>
            </div>
            <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} SPEAR Platform. All rights reserved.
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
