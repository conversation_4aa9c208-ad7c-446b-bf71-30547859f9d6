import type { <PERSON>ada<PERSON> } from "next";
// Temporarily disabled Google Fonts to fix lightningcss build issues
// import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { AuthProvider } from "@/components/auth-provider";
import { DiscordChatWidget } from "@/components/discord/discord-chat-widget";
import { ErrorBoundary } from "@/components/error-boundary";

// Temporarily disabled to fix lightningcss build issues
// const geistSans = Geist({
//   variable: "--font-geist-sans",
//   subsets: ["latin"],
// });

// const geistMono = Geist_Mono({
//   variable: "--font-geist-mono",
//   subsets: ["latin"],
// });

export const metadata: Metadata = {
  title: "SPEAR",
  description: "Secure Platform for Extended Augmented Reality",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className="antialiased"
      >
        <ErrorBoundary>
          <AuthProvider>
            {children}
            <Toaster />
            <DiscordChatWidget />
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
