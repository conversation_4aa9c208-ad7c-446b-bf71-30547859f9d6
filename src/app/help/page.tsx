"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "sonner";
import Image from "next/image";
import Link from "next/link";
import { MagnifyingGlassIcon, ArrowRightIcon, QuestionMarkCircleIcon, ChatBubbleLeftRightIcon, DocumentTextIcon, LifebuoyIcon, CheckCircleIcon, ExclamationCircleIcon, ClockIcon } from "@heroicons/react/24/outline";

export default function HelpPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [ticketSubject, setTicketSubject] = useState("");
  const [ticketDescription, setTicketDescription] = useState("");
  const [ticketCategory, setTicketCategory] = useState("technical");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tickets, setTickets] = useState<any[]>([]);
  const [loadingTickets, setLoadingTickets] = useState(true);
  const [selectedTicket, setSelectedTicket] = useState<any>(null);
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const [isAddingComment, setIsAddingComment] = useState(false);
  const [newComment, setNewComment] = useState("");
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);

  // Fetch user's tickets
  useEffect(() => {
    const fetchTickets = async () => {
      try {
        const response = await fetch('/api/support-tickets');
        if (response.ok) {
          const data = await response.json();
          setTickets(data.tickets || []);
        }
      } catch (error) {
        console.error('Error fetching tickets:', error);
      } finally {
        setLoadingTickets(false);
      }
    };

    fetchTickets();
  }, []);

  const handleTicketSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/support-tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subject: ticketSubject,
          description: ticketDescription,
          category: ticketCategory,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit ticket');
      }

      const newTicket = await response.json();

      // Add the new ticket to the list
      setTickets(prev => [newTicket, ...prev]);

      toast.success("Support ticket submitted successfully! Our team will respond soon.");
      setTicketSubject("");
      setTicketDescription("");
      setTicketCategory("technical");
    } catch (error) {
      console.error('Error submitting ticket:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit ticket. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleViewTicket = async (ticketId: string) => {
    try {
      const response = await fetch(`/api/support-tickets/${ticketId}`);
      if (response.ok) {
        const ticketData = await response.json();
        setSelectedTicket(ticketData);
        setIsTicketModalOpen(true);
      } else {
        toast.error('Failed to load ticket details');
      }
    } catch (error) {
      console.error('Error fetching ticket:', error);
      toast.error('Failed to load ticket details');
    }
  };

  const handleAddComment = async () => {
    if (!selectedTicket || !newComment.trim()) return;

    setIsSubmittingComment(true);
    try {
      const response = await fetch(`/api/support-tickets/${selectedTicket.id}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newComment.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add comment');
      }

      const newCommentData = await response.json();

      // Update the selected ticket with the new comment
      setSelectedTicket(prev => ({
        ...prev,
        comments: [...(prev.comments || []), newCommentData],
        updatedAt: new Date().toISOString(),
      }));

      // Update the ticket in the tickets list
      setTickets(prev => prev.map(ticket =>
        ticket.id === selectedTicket.id
          ? { ...ticket, updatedAt: new Date().toISOString() }
          : ticket
      ));

      setNewComment("");
      setIsAddingComment(false);
      toast.success("Comment added successfully!");
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to add comment. Please try again.');
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const faqCategories = [
    {
      id: "general",
      name: "General",
      icon: QuestionMarkCircleIcon,
    },
    {
      id: "account",
      name: "Account & Billing",
      icon: DocumentTextIcon,
    },
    {
      id: "devices",
      name: "Devices & Connectivity",
      icon: LifebuoyIcon,
    },

  ];

  const faqs = [
    {
      id: "faq-1",
      category: "general",
      question: "What is SPEAR?",
      answer: "SPEAR (Secure Platform for Extended Augmented Reality) is a comprehensive remote device management platform that allows organizations to securely access, monitor, and control remote devices. It provides enterprise-grade security, location verification, and compliance solutions for various industries."
    },
    {
      id: "faq-2",
      category: "general",
      question: "How does SPEAR ensure security?",
      answer: "SPEAR implements multiple layers of security including end-to-end encryption, multi-factor authentication, role-based access controls, and detailed audit logs. All connections are secured using industry-standard protocols, and data is encrypted both in transit and at rest."
    },
    {
      id: "faq-3",
      category: "account",
      question: "How do I change my password?",
      answer: "You can change your password by navigating to your profile settings. Click on your avatar in the top-right corner, select 'Profile', then go to the 'Security' tab. From there, you can update your password by entering your current password and your new password."
    },
    {
      id: "faq-4",
      category: "account",
      question: "How do I upgrade my subscription?",
      answer: "To upgrade your subscription, go to the 'Subscription' page from the main navigation menu. You'll see your current plan and available upgrade options. Select the plan you want to upgrade to and follow the payment instructions."
    },
    {
      id: "faq-5",
      category: "devices",
      question: "How do I add a new device?",
      answer: "To add a new device, navigate to the 'Devices' section from the main menu. Click on the 'Add Device' button and follow the setup instructions. You'll need to provide a name for the device and complete the provisioning process."
    },
    {
      id: "faq-6",
      category: "devices",
      question: "What should I do if a device goes offline?",
      answer: "If a device goes offline, first check the device's physical connection and power status. Then, verify the network connectivity. If the device is still offline, you can try to restart it remotely from the 'Devices' page if that option was previously enabled. If issues persist, contact our support team."
    },

  ];

  const supportArticles = [
    {
      id: "article-1",
      title: "Getting Started with SPEAR",
      description: "Learn the basics of setting up and using SPEAR for remote device management.",
      category: "Beginner",
      readTime: "5 min read",
      image: "/images/blogimg/spear-remote-access.png",
      slug: "getting-started-with-spear"
    },
    {
      id: "article-2",
      title: "Device Provisioning Guide",
      description: "Learn how to provision and set up new devices in your SPEAR account.",
      category: "Devices",
      readTime: "6 min read",
      image: "/images/blogimg/remote-device-tech.png",
      slug: "device-provisioning-guide"
    },
    {
      id: "article-3",
      title: "Security Best Practices",
      description: "Recommended security settings and practices for your SPEAR implementation.",
      category: "Security",
      readTime: "10 min read",
      image: "/images/blogimg/security-compliance.png",
      slug: "security-best-practices"
    },
  ];

  // Helper function to format dates
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));

    if (diffInDays > 0) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    } else if (diffInHours > 0) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  // Filter FAQs based on search query
  const filteredFaqs = faqs.filter(
    (faq) =>
      searchQuery === "" ||
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Help & Support Center</h1>
        <p className="text-muted-foreground">
          Find answers, get help, and contact our support team
        </p>
      </div>

      {/* Search Bar */}
      <div className="relative max-w-2xl mx-auto">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search for help articles, FAQs, or topics..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <Tabs defaultValue="faq" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="faq">FAQs</TabsTrigger>
          <TabsTrigger value="articles">Help Articles</TabsTrigger>
          <TabsTrigger value="tickets">My Tickets</TabsTrigger>
          <TabsTrigger value="contact">Contact Support</TabsTrigger>
        </TabsList>

        {/* FAQs Tab */}
        <TabsContent value="faq" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {faqCategories.map((category) => (
              <Card key={category.id} className="cursor-pointer hover:border-primary transition-colors">
                <CardHeader className="pb-2">
                  <div className="flex items-center space-x-2">
                    <category.icon className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">{category.name}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    {faqs.filter(faq => faq.category === category.id).length} articles
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Frequently Asked Questions</h2>
            <Accordion type="single" collapsible className="w-full">
              {filteredFaqs.length > 0 ? (
                filteredFaqs.map((faq) => (
                  <AccordionItem key={faq.id} value={faq.id}>
                    <AccordionTrigger className="text-left">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent>
                      <p className="text-muted-foreground">{faq.answer}</p>
                    </AccordionContent>
                  </AccordionItem>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No FAQs found matching your search.</p>
                </div>
              )}
            </Accordion>
          </div>
        </TabsContent>

        {/* Help Articles Tab */}
        <TabsContent value="articles" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {supportArticles.map((article) => (
              <Card key={article.id} className="overflow-hidden">
                <div className="h-40 relative">
                  <Image
                    src={article.image}
                    alt={article.title}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{article.category}</Badge>
                    <span className="text-xs text-muted-foreground">{article.readTime}</span>
                  </div>
                  <CardTitle className="text-lg mt-2">{article.title}</CardTitle>
                  <CardDescription>{article.description}</CardDescription>
                </CardHeader>
                <CardFooter>
                  <Button variant="outline" className="w-full" asChild>
                    <Link href={`/knowledge-base/${article.slug}`}>
                      Read Article
                      <ArrowRightIcon className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button variant="outline" asChild>
              <Link href="/knowledge-base">
                View All Articles
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </Link>
            </Button>
          </div>
        </TabsContent>

        {/* My Tickets Tab */}
        <TabsContent value="tickets" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>My Support Tickets</CardTitle>
              <CardDescription>
                View and manage your support requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loadingTickets ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : tickets.length > 0 ? (
                <div className="space-y-4">
                  {tickets.map((ticket) => (
                    <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-start space-x-4">
                        <div className={`mt-1 h-2 w-2 rounded-full ${
                          ticket.status === "open" ? "bg-blue-500" :
                          ticket.status === "in_progress" ? "bg-amber-500" :
                          "bg-green-500"
                        }`} />
                        <div>
                          <h3 className="font-medium">{ticket.subject}</h3>
                          <div className="flex items-center space-x-4 mt-1">
                            <p className="text-xs text-muted-foreground">
                              Created: {formatDate(ticket.createdAt)}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Last update: {formatDate(ticket.updatedAt)}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Badge className={
                          ticket.status === "open" ? "bg-blue-500" :
                          ticket.status === "in_progress" ? "bg-amber-500" :
                          "bg-green-500"
                        }>
                          {ticket.status === "open" ? "Open" :
                           ticket.status === "in_progress" ? "In Progress" :
                           "Closed"}
                        </Badge>
                        <span className={`ml-2 text-xs px-2 py-1 rounded-full ${
                          ticket.priority === "urgent" ? "bg-red-100 text-red-800" :
                          ticket.priority === "high" ? "bg-orange-100 text-orange-800" :
                          ticket.priority === "medium" ? "bg-yellow-100 text-yellow-800" :
                          "bg-gray-100 text-gray-800"
                        }`}>
                          {ticket.priority.charAt(0).toUpperCase() + ticket.priority.slice(1)}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-2"
                          onClick={() => handleViewTicket(ticket.id)}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">You don't have any support tickets yet.</p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={() => document.querySelector('[data-value="contact"]')?.click()}>
                Create New Ticket
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Ticket Status Guide</CardTitle>
              <CardDescription>
                Understanding your ticket status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="mt-1">
                    <div className="h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center">
                      <ClockIcon className="h-3 w-3 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium">Open</h3>
                    <p className="text-sm text-muted-foreground">
                      Your ticket has been received and is waiting to be assigned to a support agent.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="mt-1">
                    <div className="h-5 w-5 rounded-full bg-amber-500 flex items-center justify-center">
                      <ExclamationCircleIcon className="h-3 w-3 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium">In Progress</h3>
                    <p className="text-sm text-muted-foreground">
                      A support agent is actively working on your ticket and will provide updates.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="mt-1">
                    <div className="h-5 w-5 rounded-full bg-green-500 flex items-center justify-center">
                      <CheckCircleIcon className="h-3 w-3 text-white" />
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium">Closed</h3>
                    <p className="text-sm text-muted-foreground">
                      Your issue has been resolved and the ticket is now closed.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contact Support Tab */}
        <TabsContent value="contact" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Submit a Support Ticket</CardTitle>
                <CardDescription>
                  Our support team will respond to your inquiry as soon as possible
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleTicketSubmit}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="subject" className="text-sm font-medium">
                      Subject
                    </label>
                    <Input
                      id="subject"
                      placeholder="Brief description of your issue"
                      value={ticketSubject}
                      onChange={(e) => setTicketSubject(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="category" className="text-sm font-medium">
                      Category
                    </label>
                    <select
                      id="category"
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                      value={ticketCategory}
                      onChange={(e) => setTicketCategory(e.target.value)}
                    >
                      <option value="technical">Technical Support</option>
                      <option value="billing">Billing & Subscription</option>
                      <option value="account">Account Management</option>
                      <option value="feature">Feature Request</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="description" className="text-sm font-medium">
                      Description
                    </label>
                    <Textarea
                      id="description"
                      placeholder="Please provide as much detail as possible about your issue"
                      rows={6}
                      value={ticketDescription}
                      onChange={(e) => setTicketDescription(e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="attachments" className="text-sm font-medium">
                      Attachments (Optional)
                    </label>
                    <Input
                      id="attachments"
                      type="file"
                      multiple
                      disabled
                    />
                    <p className="text-xs text-muted-foreground">
                      File uploads are disabled in this demo
                    </p>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button type="submit" disabled={isSubmitting || !ticketSubject || !ticketDescription}>
                    {isSubmitting ? "Submitting..." : "Submit Ticket"}
                  </Button>
                </CardFooter>
              </form>
            </Card>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <LifebuoyIcon className="h-5 w-5 text-primary" />
                    <div>
                      <h3 className="font-medium">Support Hours</h3>
                      <p className="text-sm text-muted-foreground">
                        Monday - Friday: 9am - 8pm EST
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <ChatBubbleLeftRightIcon className="h-5 w-5 text-primary" />
                    <div>
                      <h3 className="font-medium">Email Support</h3>
                      <p className="text-sm text-muted-foreground">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <DocumentTextIcon className="h-5 w-5 text-primary" />
                    <div>
                      <h3 className="font-medium">Documentation</h3>
                      <Link href="/knowledge-base" className="text-sm text-primary hover:underline">
                        View Documentation
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>


            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Ticket Detail Modal */}
      <Dialog open={isTicketModalOpen} onOpenChange={(open) => {
        setIsTicketModalOpen(open);
        if (!open) {
          // Reset comment form state when modal closes
          setIsAddingComment(false);
          setNewComment("");
        }
      }}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Ticket Details</DialogTitle>
            <DialogDescription>
              View and manage your support ticket
            </DialogDescription>
          </DialogHeader>

          {selectedTicket && (
            <div className="space-y-6">
              {/* Ticket Header */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold">{selectedTicket.subject}</h3>
                  <div className="flex items-center space-x-4 mt-2">
                    <Badge className={
                      selectedTicket.status === "open" ? "bg-blue-500" :
                      selectedTicket.status === "in_progress" ? "bg-amber-500" :
                      "bg-green-500"
                    }>
                      {selectedTicket.status === "open" ? "Open" :
                       selectedTicket.status === "in_progress" ? "In Progress" :
                       "Closed"}
                    </Badge>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      selectedTicket.priority === "urgent" ? "bg-red-100 text-red-800" :
                      selectedTicket.priority === "high" ? "bg-orange-100 text-orange-800" :
                      selectedTicket.priority === "medium" ? "bg-yellow-100 text-yellow-800" :
                      "bg-gray-100 text-gray-800"
                    }`}>
                      {selectedTicket.priority.charAt(0).toUpperCase() + selectedTicket.priority.slice(1)} Priority
                    </span>
                    <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-800">
                      {selectedTicket.category.charAt(0).toUpperCase() + selectedTicket.category.slice(1)}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Created:</span>
                    <p className="text-muted-foreground">{formatDate(selectedTicket.createdAt)}</p>
                  </div>
                  <div>
                    <span className="font-medium">Last Updated:</span>
                    <p className="text-muted-foreground">{formatDate(selectedTicket.updatedAt)}</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Ticket Description */}
              <div className="space-y-2">
                <h4 className="font-medium">Description</h4>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{selectedTicket.description}</p>
                </div>
              </div>

              {/* Comments Section */}
              {selectedTicket.comments && selectedTicket.comments.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <h4 className="font-medium">Comments & Updates</h4>
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {selectedTicket.comments.map((comment: any) => (
                        <div key={comment.id} className={`p-3 rounded-lg ${
                          comment.isAdmin
                            ? 'bg-blue-50 border-l-4 border-blue-500'
                            : 'bg-gray-50 border-l-4 border-gray-300'
                        }`}>
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-sm">
                                {comment.user.name || comment.user.email}
                              </span>
                              {comment.isAdmin && (
                                <Badge variant="outline" className="text-xs">Support Team</Badge>
                              )}
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {formatDate(comment.createdAt)}
                            </span>
                          </div>
                          <p className="text-sm whitespace-pre-wrap">{comment.content}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}

              {/* Admin Notes (if any) */}
              {selectedTicket.adminNotes && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h4 className="font-medium">Admin Notes</h4>
                    <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                      <p className="text-sm whitespace-pre-wrap">{selectedTicket.adminNotes}</p>
                    </div>
                  </div>
                </>
              )}

              {/* Add Comment Section */}
              {selectedTicket.status === "open" && (
                <>
                  <Separator />
                  {isAddingComment ? (
                    <div className="space-y-4">
                      <h4 className="font-medium">Add Comment</h4>
                      <Textarea
                        placeholder="Add your comment or update..."
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        rows={4}
                      />
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setIsAddingComment(false);
                            setNewComment("");
                          }}
                          disabled={isSubmittingComment}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleAddComment}
                          disabled={!newComment.trim() || isSubmittingComment}
                        >
                          {isSubmittingComment ? "Adding..." : "Add Comment"}
                        </Button>
                      </div>
                    </div>
                  ) : null}
                </>
              )}

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={() => setIsTicketModalOpen(false)}>
                  Close
                </Button>
                {selectedTicket.status === "open" && !isAddingComment && (
                  <Button
                    variant="outline"
                    onClick={() => setIsAddingComment(true)}
                  >
                    Add Comment
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
