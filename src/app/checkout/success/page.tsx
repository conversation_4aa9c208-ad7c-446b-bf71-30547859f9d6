"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircleIcon, CreditCardIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

function CheckoutSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionDetails, setSubscriptionDetails] = useState<any>(null);
  const [countdown, setCountdown] = useState(10);

  const provider = searchParams.get("provider");
  const orderId = searchParams.get("orderId");
  const payerId = searchParams.get("payerId");
  const amount = searchParams.get("amount");
  const currency = searchParams.get("currency");

  useEffect(() => {
    async function processPayment() {
      if (!orderId || !provider) {
        setError("Missing payment information");
        setLoading(false);
        return;
      }

      try {
        if (provider === "paypal") {
          // Capture PayPal payment
          const response = await fetch("/api/paypal/capture-payment", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ orderId }),
          });

          if (response.ok) {
            const data = await response.json();
            setSubscriptionDetails({
              provider: "PayPal",
              subscriptionId: data.subscriptionId,
              amount: data.amount,
              currency: data.currency,
              captureId: data.captureId,
            });
          } else {
            const errorData = await response.json();
            setError(errorData.error || "Payment processing failed");
          }
        } else if (provider === "stripe") {
          // Handle Stripe success (if needed)
          setSubscriptionDetails({
            provider: "Stripe",
            orderId: orderId,
          });
        } else if (provider === "paypal") {
          // Handle PayPal success - payment already processed
          setSubscriptionDetails({
            provider: "PayPal",
            subscriptionId: orderId,
            amount: amount ? (parseInt(amount) / 100).toFixed(2) : null, // Convert cents to dollars
            currency: currency || "USD",
            captureId: orderId,
          });
        }
      } catch (error) {
        console.error("Error processing payment:", error);
        setError("An error occurred while processing your payment");
      } finally {
        setLoading(false);
      }
    }

    processPayment();
  }, [orderId, provider, payerId]);

  // Countdown and auto-redirect to subscription dashboard
  useEffect(() => {
    if (!loading && !error && subscriptionDetails) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            router.push('/dashboard/subscription');
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [loading, error, subscriptionDetails, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Processing Payment...</CardTitle>
            <CardDescription>
              Please wait while we confirm your payment.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">Payment Failed</CardTitle>
            <CardDescription>
              There was an issue processing your payment.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">{error}</p>
            <div className="flex space-x-2">
              <Button asChild className="flex-1">
                <Link href="/pricing">Try Again</Link>
              </Button>
              <Button variant="outline" asChild className="flex-1">
                <Link href="/contact">Contact Support</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="text-2xl font-bold text-primary">
            SPEAR
          </Link>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          {/* Success Icon */}
          <div className="flex justify-center mb-6">
            <CheckCircleIcon className="h-16 w-16 text-green-500" />
          </div>

          <h1 className="text-3xl font-bold mb-4">🎉 Welcome to SPEAR!</h1>
          <p className="text-lg text-muted-foreground mb-4">
            Your payment was successful and your subscription is now active. We're preparing your devices for shipment.
          </p>
          {!loading && !error && subscriptionDetails && countdown > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
              <p className="text-blue-800 text-sm">
                🚀 Redirecting to your subscription dashboard in <strong>{countdown}</strong> seconds...
              </p>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => router.push('/dashboard/subscription')}
              >
                Go Now
              </Button>
            </div>
          )}

          {/* Payment Details */}
          {subscriptionDetails && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center justify-center space-x-2">
                  <CreditCardIcon className="h-5 w-5" />
                  <span>Payment Details</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Payment Method:</span>
                  <span className="font-medium">{subscriptionDetails.provider}</span>
                </div>
                {subscriptionDetails.amount && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Amount:</span>
                    <span className="font-medium">
                      ${subscriptionDetails.amount} {subscriptionDetails.currency}
                    </span>
                  </div>
                )}
                {subscriptionDetails.subscriptionId && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Subscription ID:</span>
                    <span className="font-medium text-xs">
                      {subscriptionDetails.subscriptionId}
                    </span>
                  </div>
                )}
                {subscriptionDetails.captureId && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Transaction ID:</span>
                    <span className="font-medium text-xs">
                      {subscriptionDetails.captureId}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Next Steps */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">What Happens Next?</h2>
            <div className="grid md:grid-cols-2 gap-4 text-left">
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-2 flex items-center">
                    📱 1. Device Preparation
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    We're configuring your mobile device with SPEAR software. This typically takes 1-2 business days.
                  </p>
                  <Button asChild className="w-full">
                    <Link href="/dashboard/subscription">Track Progress</Link>
                  </Button>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-2 flex items-center">
                    🚚 2. Device Shipping
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Your pre-configured device will be shipped to your address. You'll receive tracking information.
                  </p>
                  <Button asChild className="w-full" variant="outline">
                    <Link href="/dashboard/devices">View Devices</Link>
                  </Button>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-2 flex items-center">
                    🖥️ 3. Desktop Access
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Your desktop remote access is ready now. Start managing your remote desktop immediately.
                  </p>
                  <Button asChild className="w-full" variant="outline">
                    <Link href="/dashboard">Access Dashboard</Link>
                  </Button>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-2 flex items-center">
                    ✅ 4. Setup Complete
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Once you receive your device, place it at your location and you'll have full remote access.
                  </p>
                  <Button asChild className="w-full" variant="outline">
                    <Link href="/dashboard/support">Get Support</Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Timeline */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-left">
            <h3 className="font-semibold mb-3 text-blue-900">📅 Expected Timeline</h3>
            <div className="space-y-2 text-sm text-blue-800">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                <span><strong>Today:</strong> Payment confirmed, desktop access active</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                <span><strong>1-2 days:</strong> Mobile device configuration complete</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                <span><strong>3-5 days:</strong> Device shipped with tracking info</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                <span><strong>5-7 days:</strong> Device delivered, full access ready</span>
              </div>
            </div>
          </div>

          {/* Important Note */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="font-semibold mb-2 text-green-900">🎯 Important</h3>
            <p className="text-sm text-green-800">
              Your desktop remote access is <strong>active right now</strong>. You can start using SPEAR
              for desktop management immediately while we prepare your mobile device.
            </p>
          </div>

          {/* Support */}
          <div className="mt-12 p-6 bg-muted/50 rounded-lg">
            <h3 className="font-semibold mb-2">Need Help?</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Our support team is here to help you get started. Check out our FAQ or contact us directly.
            </p>
            <div className="flex justify-center space-x-4">
              <Button variant="outline" size="sm" asChild>
                <Link href="/faq">View FAQ</Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/contact">Contact Support</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CheckoutSuccessPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-background flex items-center justify-center">Loading...</div>}>
      <CheckoutSuccessContent />
    </Suspense>
  );
}
