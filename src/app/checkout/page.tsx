"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckIcon, CreditCardIcon, AlertCircle, Lock } from "lucide-react";
import Link from "next/link";
import PayPalPaymentForm from "@/components/payment/PayPalPaymentForm";

interface PricingPlan {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  features: string[];
}

const plans: Record<string, PricingPlan> = {
  "single-user": {
    id: "single-user",
    name: "Single User Plan",
    monthlyPrice: 299, // $299.00 regular price
    yearlyPrice: 3588,
    features: [
      "Access to 1 remote device",
      "24/7 customer support",
      "Secure encrypted connections",
      "Mobile device support (beta)",
      "Desktop device registration"
    ],
  },
  "two-user": {
    id: "two-user",
    name: "Two User Bundle",
    monthlyPrice: 598, // $598.00 regular price (grandfathered at $298 for early customers)
    yearlyPrice: 7176,
    features: [
      "Access for 2 users",
      "Shared device management",
      "24/7 priority support",
      "Secure encrypted connections",
      "Mobile device support (beta)",
      "Team collaboration features"
    ],
  },
};

function CheckoutContent() {
  const { data: session, status } = useSession();
  const searchParams = useSearchParams();
  const router = useRouter();

  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
  const [couponCode, setCouponCode] = useState("");
  const [couponInput, setCouponInput] = useState("");
  const [finalAmount, setFinalAmount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState("");

  useEffect(() => {
    if (status === 'loading') return;

    if (!session) {
      const planId = searchParams.get("plan");
      const coupon = searchParams.get("coupon");
      router.push(`/auth/signin?callbackUrl=/checkout?plan=${planId}&coupon=${coupon || ''}`);
      return;
    }

    const planId = searchParams.get("plan");
    const coupon = searchParams.get("coupon") || "";

    if (!planId || !plans[planId]) {
      router.push("/pricing");
      return;
    }

    setSelectedPlan(plans[planId]);
    setCouponCode(coupon);
    setCouponInput(coupon); // Pre-fill coupon input if provided in URL

    // Calculate final amount with discount
    const plan = plans[planId];
    let priceInCents = plan.monthlyPrice * 100; // Convert to cents

    // Special grandfathered pricing for two-user bundle (early customers get $298 instead of $598)
    if (planId === 'two-user') {
      priceInCents = 29800; // $298.00 grandfathered price
    }

    // Apply coupon discounts for single-user plan only
    let discountedAmount = priceInCents;
    if (planId === 'single-user') {
      if (coupon.toUpperCase() === 'SPEARMINT') {
        discountedAmount = Math.max(priceInCents - 10000, 0); // $100 discount ($299 → $199)
      } else if (coupon.toUpperCase() === 'INSIDER2024') {
        discountedAmount = Math.max(priceInCents - 28900, 0); // $289 discount ($299 → $10)
      }
    }

    setFinalAmount(discountedAmount);
    setLoading(false);
  }, [session, status, router, searchParams]);

  const formatPrice = (priceInCents: number) => {
    return `$${(priceInCents / 100).toFixed(2)}`;
  };

  const applyCoupon = () => {
    if (!selectedPlan) return;

    const newCouponCode = couponInput.trim();
    setCouponCode(newCouponCode);

    // Recalculate amount with new coupon
    let priceInCents = selectedPlan.monthlyPrice * 100;

    // Special grandfathered pricing for two-user bundle
    if (selectedPlan.id === 'two-user') {
      priceInCents = 29800; // $298.00 grandfathered price
    }

    // Apply coupon discounts for single-user plan only
    let discountedAmount = priceInCents;
    if (selectedPlan.id === 'single-user') {
      if (newCouponCode.toUpperCase() === 'SPEARMINT') {
        discountedAmount = Math.max(priceInCents - 10000, 0); // $100 discount ($299 → $199)
      } else if (newCouponCode.toUpperCase() === 'INSIDER2024') {
        discountedAmount = Math.max(priceInCents - 28900, 0); // $289 discount ($299 → $10)
      }
    }

    setFinalAmount(discountedAmount);
  };

  const removeCoupon = () => {
    setCouponCode("");
    setCouponInput("");

    if (selectedPlan) {
      let priceInCents = selectedPlan.monthlyPrice * 100;

      // Special grandfathered pricing for two-user bundle
      if (selectedPlan.id === 'two-user') {
        priceInCents = 29800; // $298.00 grandfathered price
      }

      setFinalAmount(priceInCents);
    }
  };

  const handlePaymentSuccess = async (paymentResult: any) => {
    setProcessing(true);
    try {
      // Payment was successful, redirect to success page with PayPal provider
      router.push(`/checkout/success?provider=paypal&orderId=${paymentResult.payment.id}&amount=${paymentResult.payment.amount.amount}&currency=${paymentResult.payment.amount.currency}`);
    } catch (error) {
      console.error('Error handling payment success:', error);
      setPaymentError('Payment succeeded but there was an error processing your subscription. Please contact support.');
    } finally {
      setProcessing(false);
    }
  };

  const handlePaymentError = (error: string) => {
    setPaymentError(error);
    setProcessing(false);
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session || !selectedPlan) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Plan Not Found</CardTitle>
            <CardDescription>
              The selected plan could not be found. Please go back and select a valid plan.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href="/pricing">Back to Pricing</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="border-b">
        <div className="container mx-auto px-4 py-4">
          <Link href="/" className="text-2xl font-bold text-blue-600">
            SPEAR
          </Link>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Complete Your Purchase
            </h1>
            <p className="text-gray-600">
              Secure checkout powered by PayPal
            </p>
          </div>

          {paymentError && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{paymentError}</AlertDescription>
            </Alert>
          )}

          {/* Coupon Code Section */}
          <div className="mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <span className="text-green-600">🎟️</span>
                  Coupon Code
                </CardTitle>
                <CardDescription>
                  Have a coupon code? Enter it below to apply your discount
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-3">
                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder="Enter coupon code (e.g., SPEARMINT)"
                      value={couponInput}
                      onChange={(e) => setCouponInput(e.target.value.toUpperCase())}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                    />
                  </div>
                  <Button
                    onClick={applyCoupon}
                    disabled={!couponInput.trim() || couponInput.toUpperCase() === couponCode.toUpperCase()}
                    variant="outline"
                  >
                    Apply
                  </Button>
                  {couponCode && (
                    <Button
                      onClick={removeCoupon}
                      variant="outline"
                      className="text-red-600 border-red-300 hover:bg-red-50"
                    >
                      Remove
                    </Button>
                  )}
                </div>

                {/* Coupon success messages */}
                {selectedPlan?.id === 'single-user' && (
                  <>
                    {couponCode.toUpperCase() === 'SPEARMINT' && (
                      <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center text-green-800">
                          <CheckIcon className="h-4 w-4 mr-2" />
                          <span className="font-medium">SPEARMINT coupon applied! You save $100.00</span>
                        </div>
                      </div>
                    )}
                    {couponCode.toUpperCase() === 'INSIDER2024' && (
                      <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center text-green-800">
                          <CheckIcon className="h-4 w-4 mr-2" />
                          <span className="font-medium">Insider pricing applied! You save $289.00</span>
                        </div>
                      </div>
                    )}
                  </>
                )}

                {couponInput &&
                 couponInput.toUpperCase() !== 'SPEARMINT' &&
                 couponInput.toUpperCase() !== 'INSIDER2024' &&
                 couponCode !== couponInput && (
                  <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center text-yellow-800">
                      <span className="text-sm">Invalid coupon code. Try "SPEARMINT" for $100 off single-user plans.</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Order Summary */}
            <div className="order-2 lg:order-1">
              <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{selectedPlan.name}</h3>
                    <p className="text-sm text-gray-600">Monthly subscription</p>
                  </div>
                  <div className="text-right">
                    {/* Show crossed out price for discounted plans */}
                    {((couponCode.toUpperCase() === 'SPEARMINT' || couponCode.toUpperCase() === 'INSIDER2024') && selectedPlan.id === 'single-user') && (
                      <div className="text-sm text-gray-500 line-through">
                        ${selectedPlan.monthlyPrice}.00
                      </div>
                    )}
                    {selectedPlan.id === 'two-user' && (
                      <div className="text-sm text-gray-500 line-through">
                        ${selectedPlan.monthlyPrice}.00
                      </div>
                    )}
                    <div className="font-semibold">
                      {formatPrice(finalAmount)}
                    </div>
                    {selectedPlan.id === 'two-user' && (
                      <div className="text-xs text-green-600">
                        Grandfathered Price!
                      </div>
                    )}
                  </div>
                </div>

                {/* Discount display */}
                {selectedPlan.id === 'single-user' && (
                  <>
                    {couponCode.toUpperCase() === 'SPEARMINT' && (
                      <div className="flex justify-between items-center text-green-600">
                        <div>
                          <span className="font-medium">SPEARMINT Discount</span>
                        </div>
                        <div className="font-semibold">
                          -$100.00
                        </div>
                      </div>
                    )}
                    {couponCode.toUpperCase() === 'INSIDER2024' && (
                      <div className="flex justify-between items-center text-green-600">
                        <div>
                          <span className="font-medium">Insider Discount</span>
                        </div>
                        <div className="font-semibold">
                          -$289.00
                        </div>
                      </div>
                    )}
                  </>
                )}

                {selectedPlan.id === 'two-user' && (
                  <div className="flex justify-between items-center text-green-600">
                    <div>
                      <span className="font-medium">Early Customer Discount</span>
                    </div>
                    <div className="font-semibold">
                      -$300.00
                    </div>
                  </div>
                )}

                <Separator />

                <div className="flex justify-between items-center text-lg font-bold">
                  <span>Total</span>
                  <span>{formatPrice(finalAmount)}</span>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-sm">What's included:</h4>
                  <ul className="space-y-1">
                    {selectedPlan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <CheckIcon className="h-3 w-3 text-green-600 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="flex items-center text-blue-800 text-sm">
                    <Lock className="h-4 w-4 mr-2" />
                    <span>Secure payment processing by PayPal</span>
                  </div>
                </div>
              </CardContent>
              </Card>
            </div>

            {/* Payment Form */}
            <div className="order-1 lg:order-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCardIcon className="h-5 w-5" />
                  Payment Information
                </CardTitle>
                <CardDescription>
                  Enter your payment details to complete your subscription
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PayPalPaymentForm
                  amount={finalAmount}
                  planType={selectedPlan.id}
                  couponCode={couponCode}
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                  processing={processing}
                />
              </CardContent>
            </Card>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 text-center">
            <div className="flex justify-center items-center space-x-8 text-gray-500">
              <div className="flex items-center">
                <Lock className="h-5 w-5 mr-2" />
                <span className="text-sm">SSL Encrypted</span>
              </div>
              <div className="flex items-center">
                <CheckIcon className="h-5 w-5 mr-2" />
                <span className="text-sm">30-Day Guarantee</span>
              </div>
              <div className="flex items-center">
                <CreditCardIcon className="h-5 w-5 mr-2" />
                <span className="text-sm">Secure Payments</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CheckoutPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gray-50 flex items-center justify-center">Loading...</div>}>
      <CheckoutContent />
    </Suspense>
  );
}
