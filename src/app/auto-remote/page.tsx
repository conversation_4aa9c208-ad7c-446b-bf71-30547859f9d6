"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Configuration
const config = {
  spearGlobalUrl: 'https://spear-global.com/web/',
  serverFallbackUrl: '/api/remote-connect',
  autoClickAttempts: 10,
  autoClickInterval: 500, // ms
  deviceIconSelector: '.device-icon, .device-card', // Multiple possible selectors
  connectionTimeout: 30000 // 30 seconds
};

export default function AutoRemoteConnect() {
  // State
  const [deviceId, setDeviceId] = useState("1681512408");
  const [password, setPassword] = useState("82AirmaN@$");
  const [isConnecting, setIsConnecting] = useState(false);
  const [statusMessage, setStatusMessage] = useState<{ text: string; type: 'default' | 'info' | 'success' | 'error' } | null>(null);
  const [showConnectionFrame, setShowConnectionFrame] = useState(false);

  // Refs
  const connectionFrameRef = useRef<HTMLIFrameElement>(null);
  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (connectionTimeoutRef.current) {
        clearTimeout(connectionTimeoutRef.current);
      }
    };
  }, []);

  // Show status message
  const showStatus = (text: string, type: 'default' | 'info' | 'success' | 'error' = 'info') => {
    setStatusMessage({ text, type });
  };

  // Hide status message
  const hideStatus = () => {
    setStatusMessage(null);
  };

  // Main connect function
  const handleConnect = () => {
    const trimmedDeviceId = deviceId.trim();

    if (!trimmedDeviceId) {
      showStatus('Please enter a device ID', 'error');
      return;
    }

    // Start connection process
    setIsConnecting(true);
    showStatus('Initiating connection...', 'info');

    // Format the device ID by removing spaces if present
    const formattedDeviceId = trimmedDeviceId.replace(/\s+/g, '');

    // Skip client-side automation and go directly to server-side approach
    tryServerFallback(formattedDeviceId, password);
  };

  // Client-side automation attempt
  const tryClientSideAutomation = (connectionUrl: string, deviceId: string, password: string) => {
    // Show connection frame
    setShowConnectionFrame(true);

    // Set iframe source
    if (connectionFrameRef.current) {
      connectionFrameRef.current.src = connectionUrl;
    }

    // Set up a timeout for the connection
    connectionTimeoutRef.current = setTimeout(() => {
      // If we reach the timeout, try server fallback
      setShowConnectionFrame(false);
      showStatus('Connection taking too long. Trying alternative method...', 'info');
      tryServerFallback(deviceId, password);
    }, config.connectionTimeout);

    // Listen for iframe load
    if (connectionFrameRef.current) {
      connectionFrameRef.current.onload = function() {
        // Try to access iframe content (will fail if cross-origin)
        try {
          // This will throw an error due to cross-origin policy
          const iframeDocument = connectionFrameRef.current?.contentDocument ||
                               (connectionFrameRef.current?.contentWindow?.document);

          // If we get here, we can access the iframe (unlikely due to CORS)
          if (connectionTimeoutRef.current) {
            clearTimeout(connectionTimeoutRef.current);
          }
          attemptAutoClick(0);
        } catch (e) {
          // Cross-origin error as expected, open in new window instead
          if (connectionTimeoutRef.current) {
            clearTimeout(connectionTimeoutRef.current);
          }
          setShowConnectionFrame(false);

          // Open in new window and provide instructions
          openInNewWindowWithInstructions(connectionUrl);
        }
      };
    }
  };

  // Attempt to auto-click the device icon (will likely fail due to CORS)
  const attemptAutoClick = (attempt: number) => {
    if (attempt >= config.autoClickAttempts || !connectionFrameRef.current) {
      // Give up after max attempts
      setShowConnectionFrame(false);
      openInNewWindowWithInstructions(connectionFrameRef.current?.src || '');
      return;
    }

    try {
      const iframeDocument = connectionFrameRef.current.contentDocument ||
                           connectionFrameRef.current.contentWindow?.document;

      if (!iframeDocument) {
        throw new Error("Cannot access iframe document");
      }

      const deviceIcon = iframeDocument.querySelector(config.deviceIconSelector);

      if (deviceIcon) {
        // Found the device icon, click it
        (deviceIcon as HTMLElement).click();

        // Hide external branding
        const style = document.createElement('style');
        style.textContent = `
          .header-bar, .sidebar, .external-header { display: none !important; }
          body { overflow: hidden; }
        `;
        iframeDocument.head.appendChild(style);

        // Success!
        showStatus('Connected successfully!', 'success');
      } else {
        // Device icon not found yet, try again
        setTimeout(() => {
          attemptAutoClick(attempt + 1);
        }, config.autoClickInterval);
      }
    } catch (e) {
      // CORS error, fall back to new window
      setShowConnectionFrame(false);
      openInNewWindowWithInstructions(connectionFrameRef.current.src || '');
    }
  };

  // Open in new window with instructions
  const openInNewWindowWithInstructions = (url: string) => {
    // Reset the UI
    setIsConnecting(false);
    setShowConnectionFrame(false);

    // Show instructions
    showStatus('Opening remote control in a new window. Please click on your device when the page loads.', 'info');

    // Open in new window
    window.open(url, '_blank');
  };

  // Server-side automation implementation
  const tryServerFallback = (deviceId: string, password: string) => {
    showStatus('Connecting to your device via server...', 'info');

    // Call our server API
    fetch(config.serverFallbackUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ deviceId, password }),
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Server successfully connected or provided a URL

        // Check if we're in development mode
        if (data.isDevelopment) {
          showStatus('Opening SPEAR Global web client. Please click on your device when the page loads.', 'info');
        } else {
          showStatus('Connection established! Opening remote session...', 'success');
        }

        // If we have a connection URL, open it
        if (data.connectionUrl) {
          // Open the connection URL in a new window
          const remoteWindow = window.open(data.connectionUrl, '_blank');

          // Check if window was blocked
          if (!remoteWindow) {
            showStatus('Popup blocked. Please allow popups and try again.', 'error');
            setIsConnecting(false);
            return;
          }

          // Reset the connecting state after a short delay
          setTimeout(() => {
            setIsConnecting(false);
            if (data.isDevelopment) {
              showStatus('SPEAR Global web client opened. Please click on your device to connect.', 'info');
            } else {
              showStatus('Remote control session opened in a new window.', 'success');
            }
          }, 2000);
        } else {
          // Fallback to direct connection if no URL was returned
          const connectionUrl = `${config.spearGlobalUrl}?id=${deviceId}`;
          if (password) {
            connectionUrl += `&password=${encodeURIComponent(password)}`;
          }
          window.open(connectionUrl, '_blank');
          setIsConnecting(false);
        }
      } else {
        // Server failed to connect
        let errorMessage = data.error || 'Unknown error';

        // If we have a debug screenshot, show it
        if (data.debugScreenshot) {
          // Create a debug image element
          const debugImage = document.createElement('img');
          debugImage.src = data.debugScreenshot;
          debugImage.style.display = 'none';
          document.body.appendChild(debugImage);

          // Log the debug info
          console.log('Debug screenshot available:', debugImage.src);
        }

        showStatus(`Connection failed: ${errorMessage}. Please try again or use the manual connection option.`, 'error');
        setIsConnecting(false);

        // Fallback to direct connection after a short delay
        setTimeout(() => {
          const connectionUrl = `${config.spearGlobalUrl}?id=${deviceId}`;
          if (password) {
            connectionUrl += `&password=${encodeURIComponent(password)}`;
          }
          window.open(connectionUrl, '_blank');
        }, 3000);
      }
    })
    .catch(error => {
      console.error('Server connection error:', error);
      showStatus('Connection error. Falling back to manual connection...', 'error');
      setIsConnecting(false);

      // Fallback to direct connection
      setTimeout(() => {
        const connectionUrl = `${config.spearGlobalUrl}?id=${deviceId}`;
        if (password) {
          connectionUrl += `&password=${encodeURIComponent(password)}`;
        }
        window.open(connectionUrl, '_blank');
      }, 2000);
    });
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Image
              src="/images/spear-logo.PNG"
              alt="Spear Logo"
              width={120}
              height={60}
              className="h-auto"
            />
          </div>
          <CardTitle>Remote Control</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="deviceId">Device ID</Label>
              <Input
                id="deviceId"
                placeholder="Enter device ID"
                value={deviceId}
                onChange={(e) => setDeviceId(e.target.value)}
                disabled={isConnecting}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Access Code</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter access code"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isConnecting}
              />
            </div>

            <Button
              onClick={handleConnect}
              disabled={isConnecting || !deviceId.trim()}
              className="w-full"
            >
              {isConnecting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Connecting...
                </>
              ) : (
                "Connect to Device"
              )}
            </Button>

            {statusMessage && (
              <Alert className={
                statusMessage.type === 'error' ? 'bg-red-900/20 text-red-400 border-red-900/50' :
                statusMessage.type === 'success' ? 'bg-green-900/20 text-green-400 border-green-900/50' :
                statusMessage.type === 'info' ? 'bg-blue-900/20 text-blue-400 border-blue-900/50' :
                'bg-slate-900/20 text-slate-400 border-slate-900/50'
              }>
                <AlertDescription>
                  {statusMessage.text}
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Hidden iframe for connection attempts */}
      <iframe
        ref={connectionFrameRef}
        className={`fixed top-0 left-0 w-full h-full border-0 ${showConnectionFrame ? 'block' : 'hidden'}`}
        title="Remote Connection"
      />

      {/* Loading overlay */}
      {isConnecting && (
        <div className="fixed inset-0 bg-slate-900/90 flex items-center justify-center z-50 flex-col">
          <div className="w-12 h-12 border-4 border-t-transparent border-indigo-600 rounded-full animate-spin mb-4"></div>
          <p className="text-slate-200 text-lg font-medium">Connecting to your device...</p>
        </div>
      )}
    </div>
  );
}
