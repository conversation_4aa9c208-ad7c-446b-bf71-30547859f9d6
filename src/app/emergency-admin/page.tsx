'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';

// EMERGENCY ADMIN ACCESS PAGE
// This bypasses the database and provides direct admin access

export default function EmergencyAdminPage() {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // Hardcoded emergency password - change this to something secure
  const EMERGENCY_PASSWORD = 'SpearEmergencyAccess2024!';

  const handleEmergencyLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    if (password === EMERGENCY_PASSWORD) {
      try {
        // Create a temporary session by calling the emergency auth endpoint
        const response = await fetch('/api/auth/emergency-admin', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ password }),
        });

        if (response.ok) {
          // Redirect to admin dashboard
          router.push('/admin');
        } else {
          setError('Emergency access failed');
        }
      } catch (error) {
        console.error('Emergency login error:', error);
        setError('Emergency access failed');
      }
    } else {
      setError('Invalid emergency password');
    }

    setIsLoading(false);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            🚨 Emergency Admin Access
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Database connection issues? Use this emergency access.
          </p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleEmergencyLogin}>
          <div>
            <label htmlFor="password" className="sr-only">
              Emergency Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              required
              className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              placeholder="Emergency Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center">{error}</div>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              {isLoading ? 'Accessing...' : 'Emergency Admin Access'}
            </button>
          </div>

          <div className="text-center">
            <a
              href="/login"
              className="font-medium text-indigo-600 hover:text-indigo-500"
            >
              Back to normal login
            </a>
          </div>
        </form>

        <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <h3 className="text-sm font-medium text-yellow-800">
            Emergency Access Instructions:
          </h3>
          <ul className="mt-2 text-sm text-yellow-700 list-disc list-inside">
            <li>This bypasses database authentication</li>
            <li>Use only when normal login fails</li>
            <li>Password: SpearEmergencyAccess2024!</li>
            <li>Remove this page after fixing database issues</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
