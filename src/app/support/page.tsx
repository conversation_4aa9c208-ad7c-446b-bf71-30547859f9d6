"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ChatBubbleLeftRightIcon,
  QuestionMarkCircleIcon,
  DocumentTextIcon,
  PhoneIcon,
  EnvelopeIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";

interface CustomerData {
  customer: {
    id: string;
    name: string;
    email: string;
    joinDate: string;
  };
  subscription?: {
    status: string;
    planType: string;
    currentPeriodEnd?: string;
  };
  orders: Array<{
    id: string;
    status: string;
    subscriptionPlan: string;
    trackingNumber?: string;
    device?: {
      name: string;
      rustDeskId?: string;
      status: string;
    };
  }>;
  devices: Array<{
    id: string;
    name: string;
    status: string;
    rustDeskId?: string;
  }>;
  summary: {
    hasActiveSubscription: boolean;
    totalOrders: number;
    activeDevices: number;
    needsAttention: boolean;
  };
}

export default function SupportPage() {
  const [customerData, setCustomerData] = useState<CustomerData | null>(null);
  const [loading, setLoading] = useState(true);
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
    priority: 'normal',
  });
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchCustomerData();
  }, []);

  const fetchCustomerData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/customer-service');
      if (response.ok) {
        const data = await response.json();
        setCustomerData(data);
      }
    } catch (error) {
      console.error('Error fetching customer data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      // TODO: Implement contact form submission
      // This will be integrated with AI agent in the future
      console.log('Contact form submitted:', contactForm);
      
      // Simulate submission
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('Thank you for contacting us! We\'ll get back to you soon.');
      setContactForm({ subject: '', message: '', priority: 'normal' });
    } catch (error) {
      console.error('Error submitting contact form:', error);
      alert('There was an error submitting your request. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: 'Active', variant: 'default' as const, color: 'text-green-600' },
      device_prep: { label: 'Device Prep', variant: 'secondary' as const, color: 'text-orange-600' },
      shipped: { label: 'Shipped', variant: 'default' as const, color: 'text-blue-600' },
      delivered: { label: 'Delivered', variant: 'default' as const, color: 'text-purple-600' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      variant: 'outline' as const,
      color: 'text-gray-600',
    };

    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">SPEAR Support Center</h1>
          <p className="mt-2 text-gray-600">
            Get help with your SPEAR remote access service
          </p>
        </div>

        {/* AI Agent Integration Notice */}
        <Card className="mb-8 border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-start space-x-3">
              <InformationCircleIcon className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900">AI-Powered Support Coming Soon</h3>
                <p className="text-sm text-blue-700 mt-1">
                  We're building an intelligent support agent that will have access to your account information 
                  and can help resolve issues instantly. For now, use the contact form below for assistance.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Support Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Help</CardTitle>
                <CardDescription>Common questions and quick solutions</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                    <QuestionMarkCircleIcon className="h-5 w-5 mb-2" />
                    <span className="font-medium">Connection Issues</span>
                    <span className="text-sm text-muted-foreground">Can't connect to your device?</span>
                  </Button>
                  
                  <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                    <DocumentTextIcon className="h-5 w-5 mb-2" />
                    <span className="font-medium">Setup Guide</span>
                    <span className="text-sm text-muted-foreground">First-time setup instructions</span>
                  </Button>
                  
                  <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                    <ClockIcon className="h-5 w-5 mb-2" />
                    <span className="font-medium">Order Status</span>
                    <span className="text-sm text-muted-foreground">Check your order progress</span>
                  </Button>
                  
                  <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                    <ChatBubbleLeftRightIcon className="h-5 w-5 mb-2" />
                    <span className="font-medium">Contact Support</span>
                    <span className="text-sm text-muted-foreground">Get personalized help</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Account Overview */}
            {customerData && (
              <Card>
                <CardHeader>
                  <CardTitle>Your Account Overview</CardTitle>
                  <CardDescription>Current status of your SPEAR service</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {customerData.summary.hasActiveSubscription ? 'Active' : 'Inactive'}
                      </div>
                      <p className="text-sm text-muted-foreground">Subscription</p>
                    </div>
                    
                    <div className="text-center p-4 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {customerData.summary.totalOrders}
                      </div>
                      <p className="text-sm text-muted-foreground">Total Orders</p>
                    </div>
                    
                    <div className="text-center p-4 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">
                        {customerData.summary.activeDevices}
                      </div>
                      <p className="text-sm text-muted-foreground">Active Devices</p>
                    </div>
                  </div>

                  {customerData.summary.needsAttention && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-yellow-800">Attention Required</h4>
                          <p className="text-sm text-yellow-700 mt-1">
                            Your account may need attention. Please check your subscription status or contact support.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Recent Orders */}
                  {customerData.orders.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3">Recent Orders</h4>
                      <div className="space-y-2">
                        {customerData.orders.slice(0, 3).map((order) => (
                          <div key={order.id} className="flex items-center justify-between p-3 bg-muted rounded">
                            <div>
                              <p className="font-medium">#{order.id.slice(-8)}</p>
                              <p className="text-sm text-muted-foreground">
                                {order.subscriptionPlan.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </p>
                            </div>
                            {getStatusBadge(order.status)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Contact Form */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Support</CardTitle>
                <CardDescription>
                  Describe your issue and we'll help you resolve it
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleContactSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium mb-2">
                      Subject
                    </label>
                    <Input
                      id="subject"
                      value={contactForm.subject}
                      onChange={(e) => setContactForm({ ...contactForm, subject: e.target.value })}
                      placeholder="Brief description of your issue"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium mb-2">
                      Message
                    </label>
                    <Textarea
                      id="message"
                      value={contactForm.message}
                      onChange={(e) => setContactForm({ ...contactForm, message: e.target.value })}
                      placeholder="Please provide details about your issue..."
                      rows={5}
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="priority" className="block text-sm font-medium mb-2">
                      Priority
                    </label>
                    <select
                      id="priority"
                      value={contactForm.priority}
                      onChange={(e) => setContactForm({ ...contactForm, priority: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="low">Low - General question</option>
                      <option value="normal">Normal - Standard issue</option>
                      <option value="high">High - Service disruption</option>
                      <option value="urgent">Urgent - Critical issue</option>
                    </select>
                  </div>
                  
                  <Button type="submit" disabled={submitting} className="w-full">
                    {submitting ? 'Submitting...' : 'Submit Support Request'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <EnvelopeIcon className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Email Support</p>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex items-center space-x-3">
                  <ClockIcon className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Response Time</p>
                    <p className="text-sm text-muted-foreground">Within 24 hours</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Status */}
            <Card>
              <CardHeader>
                <CardTitle>Service Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium text-green-800">All Systems Operational</p>
                    <p className="text-sm text-muted-foreground">Last updated: Just now</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Resources */}
            <Card>
              <CardHeader>
                <CardTitle>Helpful Resources</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="ghost" className="w-full justify-start">
                  <DocumentTextIcon className="h-4 w-4 mr-2" />
                  User Guide
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <QuestionMarkCircleIcon className="h-4 w-4 mr-2" />
                  FAQ
                </Button>
                <Button variant="ghost" className="w-full justify-start">
                  <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                  Community Forum
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
