"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircleIcon, XCircleIcon, ClockIcon, ComputerDesktopIcon, DevicePhoneMobileIcon } from "@heroicons/react/24/outline";

interface DeviceSubmission {
  id: string;
  deviceName: string;
  rustDeskId: string;
  deviceType: string;
  status: string;
  adminNotes?: string;
  submittedAt: string;
  reviewedAt?: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

interface TradeInRequest {
  id: string;
  deviceModel: string;
  deviceCondition: string;
  estimatedValue: number;
  actualValue?: number;
  status: string;
  trackingNumber?: string;
  rebateAmount?: number;
  rebateApplied: boolean;
  adminNotes?: string;
  submittedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

export default function DeviceManagementPage() {
  const [deviceSubmissions, setDeviceSubmissions] = useState<DeviceSubmission[]>([]);
  const [tradeInRequests, setTradeInRequests] = useState<TradeInRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSubmission, setSelectedSubmission] = useState<DeviceSubmission | null>(null);
  const [selectedTradeIn, setSelectedTradeIn] = useState<TradeInRequest | null>(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [tradeInDialogOpen, setTradeInDialogOpen] = useState(false);
  const [reviewData, setReviewData] = useState({ status: "", adminNotes: "" });
  const [tradeInData, setTradeInData] = useState({
    status: "",
    trackingNumber: "",
    actualValue: "",
    rebateAmount: "",
    rebateApplied: false,
    adminNotes: "",
    deviceFunctional: undefined as boolean | undefined,
    conditionMatches: undefined as boolean | undefined,
    returnTrackingNumber: "",
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load device submissions
      const submissionsResponse = await fetch("/api/device-submissions");
      if (submissionsResponse.ok) {
        const submissionsData = await submissionsResponse.json();
        setDeviceSubmissions(submissionsData.submissions);
      }

      // Load trade-in requests
      const tradeInResponse = await fetch("/api/trade-in-requests");
      if (tradeInResponse.ok) {
        const tradeInData = await tradeInResponse.json();
        setTradeInRequests(tradeInData.requests);
      }
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleReviewSubmission = (submission: DeviceSubmission) => {
    setSelectedSubmission(submission);
    setReviewData({ status: submission.status, adminNotes: submission.adminNotes || "" });
    setReviewDialogOpen(true);
  };

  const handleManageTradeIn = (tradeIn: TradeInRequest) => {
    setSelectedTradeIn(tradeIn);
    setTradeInData({
      status: tradeIn.status,
      trackingNumber: tradeIn.trackingNumber || "",
      actualValue: tradeIn.actualValue?.toString() || "",
      rebateAmount: tradeIn.rebateAmount?.toString() || "",
      rebateApplied: tradeIn.rebateApplied,
      adminNotes: tradeIn.adminNotes || "",
      deviceFunctional: undefined,
      conditionMatches: undefined,
      returnTrackingNumber: "",
    });
    setTradeInDialogOpen(true);
  };

  const submitReview = async () => {
    if (!selectedSubmission) return;

    try {
      const response = await fetch("/api/device-submissions", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: selectedSubmission.id,
          ...reviewData,
        }),
      });

      if (response.ok) {
        setReviewDialogOpen(false);
        loadData();
      }
    } catch (error) {
      console.error("Error updating submission:", error);
    }
  };

  const submitTradeInUpdate = async () => {
    if (!selectedTradeIn) return;

    try {
      const response = await fetch("/api/trade-in-requests", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: selectedTradeIn.id,
          ...tradeInData,
          actualValue: tradeInData.actualValue ? parseFloat(tradeInData.actualValue) : undefined,
          rebateAmount: tradeInData.rebateAmount ? parseFloat(tradeInData.rebateAmount) : undefined,
        }),
      });

      if (response.ok) {
        setTradeInDialogOpen(false);
        loadData();
      }
    } catch (error) {
      console.error("Error updating trade-in request:", error);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800", icon: ClockIcon },
      approved: { color: "bg-green-100 text-green-800", icon: CheckCircleIcon },
      rejected: { color: "bg-red-100 text-red-800", icon: XCircleIcon },
      shipped: { color: "bg-blue-100 text-blue-800", icon: ClockIcon },
      received: { color: "bg-purple-100 text-purple-800", icon: ClockIcon },
      processed: { color: "bg-indigo-100 text-indigo-800", icon: ClockIcon },
      completed: { color: "bg-green-100 text-green-800", icon: CheckCircleIcon },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} flex items-center space-x-1`}>
        <Icon className="w-3 h-3" />
        <span>{status.charAt(0).toUpperCase() + status.slice(1)}</span>
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading device management data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Device Management</h1>
        <p className="text-muted-foreground mt-2">
          Manage device submissions and trade-in requests from clients
        </p>
      </div>

      <Tabs defaultValue="submissions" className="space-y-6">
        <TabsList>
          <TabsTrigger value="submissions" className="flex items-center space-x-2">
            <ComputerDesktopIcon className="w-4 h-4" />
            <span>Device Submissions ({deviceSubmissions.filter(s => s.status === 'pending').length})</span>
          </TabsTrigger>
          <TabsTrigger value="trade-ins" className="flex items-center space-x-2">
            <DevicePhoneMobileIcon className="w-4 h-4" />
            <span>Trade-In Requests ({tradeInRequests.filter(t => t.status === 'pending').length})</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="submissions">
          <Card>
            <CardHeader>
              <CardTitle>Device Submissions</CardTitle>
              <CardDescription>
                Review and approve client desktop device registrations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {deviceSubmissions.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No device submissions found
                  </div>
                ) : (
                  deviceSubmissions.map((submission) => (
                    <div key={submission.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex justify-between items-start">
                        <div className="space-y-1">
                          <h3 className="font-semibold">{submission.deviceName}</h3>
                          <p className="text-sm text-muted-foreground">
                            {submission.user.name} ({submission.user.email})
                          </p>
                          <p className="text-sm">
                            <span className="font-medium">RustDesk ID:</span> {submission.rustDeskId}
                          </p>
                          <p className="text-sm">
                            <span className="font-medium">Type:</span> {submission.deviceType}
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          {getStatusBadge(submission.status)}
                          <Button
                            size="sm"
                            onClick={() => handleReviewSubmission(submission)}
                          >
                            Review
                          </Button>
                        </div>
                      </div>
                      {submission.adminNotes && (
                        <div className="bg-gray-50 p-3 rounded">
                          <p className="text-sm">
                            <span className="font-medium">Admin Notes:</span> {submission.adminNotes}
                          </p>
                        </div>
                      )}
                      <p className="text-xs text-muted-foreground">
                        Submitted: {new Date(submission.submittedAt).toLocaleString()}
                      </p>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trade-ins">
          <Card>
            <CardHeader>
              <CardTitle>Trade-In Requests</CardTitle>
              <CardDescription>
                Manage Samsung A14 trade-in requests and rebate processing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {tradeInRequests.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No trade-in requests found
                  </div>
                ) : (
                  tradeInRequests.map((request) => (
                    <div key={request.id} className="border rounded-lg p-4 space-y-3">
                      <div className="flex justify-between items-start">
                        <div className="space-y-1">
                          <h3 className="font-semibold">{request.deviceModel}</h3>
                          <p className="text-sm text-muted-foreground">
                            {request.user.name} ({request.user.email})
                          </p>
                          <p className="text-sm">
                            <span className="font-medium">Condition:</span> {request.deviceCondition}
                          </p>
                          <p className="text-sm">
                            <span className="font-medium">Estimated Value:</span> ${request.estimatedValue}
                          </p>
                          {request.actualValue && (
                            <p className="text-sm">
                              <span className="font-medium">Actual Value:</span> ${request.actualValue}
                            </p>
                          )}
                          {request.rebateAmount && (
                            <p className="text-sm">
                              <span className="font-medium">Rebate:</span> ${request.rebateAmount}
                              {request.rebateApplied && <span className="text-green-600 ml-1">(Applied)</span>}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center space-x-3">
                          {getStatusBadge(request.status)}
                          <Button
                            size="sm"
                            onClick={() => handleManageTradeIn(request)}
                          >
                            Manage
                          </Button>
                        </div>
                      </div>
                      {request.trackingNumber && (
                        <div className="bg-blue-50 p-3 rounded">
                          <p className="text-sm">
                            <span className="font-medium">Tracking:</span> {request.trackingNumber}
                          </p>
                        </div>
                      )}
                      {request.adminNotes && (
                        <div className="bg-gray-50 p-3 rounded">
                          <p className="text-sm">
                            <span className="font-medium">Admin Notes:</span> {request.adminNotes}
                          </p>
                        </div>
                      )}
                      <p className="text-xs text-muted-foreground">
                        Submitted: {new Date(request.submittedAt).toLocaleString()}
                      </p>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Device Review Dialog */}
      <Dialog open={reviewDialogOpen} onOpenChange={setReviewDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Review Device Submission</DialogTitle>
            <DialogDescription>
              Review and approve or reject this device submission
            </DialogDescription>
          </DialogHeader>
          {selectedSubmission && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded">
                <h4 className="font-medium">{selectedSubmission.deviceName}</h4>
                <p className="text-sm text-muted-foreground">
                  {selectedSubmission.user.name} ({selectedSubmission.user.email})
                </p>
                <p className="text-sm">RustDesk ID: {selectedSubmission.rustDeskId}</p>
              </div>

              <div className="space-y-2">
                <Label>Status</Label>
                <Select value={reviewData.status} onValueChange={(value) => setReviewData(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Admin Notes</Label>
                <Textarea
                  value={reviewData.adminNotes}
                  onChange={(e) => setReviewData(prev => ({ ...prev, adminNotes: e.target.value }))}
                  placeholder="Add notes about this review..."
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setReviewDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={submitReview}>
              Update Submission
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Trade-In Management Dialog */}
      <Dialog open={tradeInDialogOpen} onOpenChange={setTradeInDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Manage Trade-In Request</DialogTitle>
            <DialogDescription>
              Update the status and details of this trade-in request
            </DialogDescription>
          </DialogHeader>
          {selectedTradeIn && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded">
                <h4 className="font-medium">{selectedTradeIn.deviceModel}</h4>
                <p className="text-sm text-muted-foreground">
                  {selectedTradeIn.user.name} ({selectedTradeIn.user.email})
                </p>
                <p className="text-sm">Condition: {selectedTradeIn.deviceCondition}</p>
                <p className="text-sm">Estimated Value: ${selectedTradeIn.estimatedValue}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select value={tradeInData.status} onValueChange={(value) => setTradeInData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="shipped">Shipped</SelectItem>
                      <SelectItem value="received">Received</SelectItem>
                      <SelectItem value="processed">Processed</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Tracking Number</Label>
                  <Input
                    value={tradeInData.trackingNumber}
                    onChange={(e) => setTradeInData(prev => ({ ...prev, trackingNumber: e.target.value }))}
                    placeholder="Enter tracking number"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Actual Value ($)</Label>
                  <Input
                    type="number"
                    value={tradeInData.actualValue}
                    onChange={(e) => setTradeInData(prev => ({ ...prev, actualValue: e.target.value }))}
                    placeholder="0.00"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Rebate Amount ($)</Label>
                  <Input
                    type="number"
                    value={tradeInData.rebateAmount}
                    onChange={(e) => setTradeInData(prev => ({ ...prev, rebateAmount: e.target.value }))}
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="rebateApplied"
                  checked={tradeInData.rebateApplied}
                  onChange={(e) => setTradeInData(prev => ({ ...prev, rebateApplied: e.target.checked }))}
                />
                <Label htmlFor="rebateApplied">Rebate has been applied to subscription</Label>
              </div>

              {/* Device Processing Section */}
              {tradeInData.status === "received" && (
                <div className="border-t pt-4">
                  <h4 className="font-semibold mb-4 text-blue-900">Device Processing</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Device Functional?</Label>
                      <Select
                        value={tradeInData.deviceFunctional?.toString() || ""}
                        onValueChange={(value) => setTradeInData(prev => ({
                          ...prev,
                          deviceFunctional: value === "true" ? true : value === "false" ? false : undefined
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="true">Yes - Device is functional</SelectItem>
                          <SelectItem value="false">No - Device has issues</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Condition Matches Description?</Label>
                      <Select
                        value={tradeInData.conditionMatches?.toString() || ""}
                        onValueChange={(value) => setTradeInData(prev => ({
                          ...prev,
                          conditionMatches: value === "true" ? true : value === "false" ? false : undefined
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="true">Yes - Condition matches</SelectItem>
                          <SelectItem value="false">No - Condition differs</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="mt-4 space-y-2">
                    <Label>Return Tracking Number (if rejected)</Label>
                    <Input
                      value={tradeInData.returnTrackingNumber}
                      onChange={(e) => setTradeInData(prev => ({ ...prev, returnTrackingNumber: e.target.value }))}
                      placeholder="Enter return tracking number"
                    />
                  </div>

                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h5 className="font-semibold text-blue-900 mb-2">Processing Instructions:</h5>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• If device is functional and condition matches: Configure device and ship back to client</li>
                      <li>• If device is not functional or condition doesn't match: Ship back without configuration</li>
                      <li>• Rebate will be automatically applied or denied based on your assessment</li>
                      <li>• Client will receive automatic notification of the outcome</li>
                    </ul>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label>Admin Notes</Label>
                <Textarea
                  value={tradeInData.adminNotes}
                  onChange={(e) => setTradeInData(prev => ({ ...prev, adminNotes: e.target.value }))}
                  placeholder="Add notes about this trade-in..."
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setTradeInDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={submitTradeInUpdate}>
              Update Trade-In
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
