"use client";

import { useState, useEffect } from "react";
import { AdminTitle } from "@/components/admin/admin-title";
import { SectionTitle } from "@/components/admin/section-title";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { getAllPosts, getAllCategories, getSeoKeywords, createPost } from "@/app/actions/blog";
import { useSession } from "next-auth/react";

// Blog post interface
interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  status: string;
  author: string;
  category: string;
  publishedAt: string | null;
  views: number;
  comments: number;
}

// Category interface
interface Category {
  name: string;
  count: number;
}

// SEO keyword interface
interface SeoKeyword {
  keyword: string;
  volume: number;
  difficulty: number;
  cpc: number;
}

export default function BlogAdminPage() {
  const { data: session } = useSession();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [seoKeywords, setSeoKeywords] = useState<SeoKeyword[]>([]);
  const [activeTab, setActiveTab] = useState("posts");
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isNewPostDialogOpen, setIsNewPostDialogOpen] = useState(false);
  const [newPostTitle, setNewPostTitle] = useState("");
  const [newPostExcerpt, setNewPostExcerpt] = useState("");
  const [newPostCategory, setNewPostCategory] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load blog posts, categories, and SEO keywords on component mount
  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        setError(null);

        // Load blog posts
        const postsData = await getAllPosts();
        setPosts(postsData);

        // Load categories
        const categoriesData = await getAllCategories();
        setCategories(categoriesData);

        // Load SEO keywords
        const keywordsData = await getSeoKeywords();
        setSeoKeywords(keywordsData);

        setLoading(false);
      } catch (err) {
        console.error("Error loading blog data:", err);
        setError("Failed to load blog data. Please try again.");
        setLoading(false);
      }
    }

    loadData();
  }, []);

  // Filter posts based on search query and status filter
  const filteredPosts = posts.filter(post => {
    const matchesSearch =
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.author.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === "all" || post.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleCreatePost = async () => {
    if (!newPostTitle || !newPostExcerpt || !newPostCategory || !session?.user?.name) {
      alert("Please fill in all required fields");
      return;
    }

    try {
      setLoading(true);

      // Generate slug from title
      const slug = newPostTitle
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      // Call the API to create a new post
      const newPost = await createPost({
        title: newPostTitle,
        slug,
        excerpt: newPostExcerpt,
        status: "draft",
        author: session.user.name,
        category: newPostCategory,
      });

      // Add the new post to the posts list
      setPosts([newPost, ...posts]);

      // Reset form fields
      setNewPostTitle("");
      setNewPostExcerpt("");
      setNewPostCategory("");
      setIsNewPostDialogOpen(false);
      setLoading(false);
    } catch (err) {
      console.error("Error creating post:", err);
      alert("Failed to create post. Please try again.");
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-8">
      <AdminTitle
        title="Blog Management"
        description="Create and manage blog content with SEO optimization"
        icon={
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M12 20h9"></path>
            <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
          </svg>
        }
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="posts">Posts</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
        </TabsList>

        <TabsContent value="posts" className="space-y-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex flex-col sm:flex-row gap-2 flex-1">
              <Input
                placeholder="Search posts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="sm:max-w-xs"
              />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="sm:w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Dialog open={isNewPostDialogOpen} onOpenChange={setIsNewPostDialogOpen}>
              <DialogTrigger asChild>
                <Button>Create New Post</Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create New Blog Post</DialogTitle>
                  <DialogDescription>
                    Create a new blog post draft. You can edit and publish it later.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="post-title">Post Title</Label>
                    <Input
                      id="post-title"
                      placeholder="Enter post title"
                      value={newPostTitle}
                      onChange={(e) => setNewPostTitle(e.target.value)}
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="post-excerpt">Excerpt</Label>
                    <Textarea
                      id="post-excerpt"
                      placeholder="Enter a brief excerpt"
                      value={newPostExcerpt}
                      onChange={(e) => setNewPostExcerpt(e.target.value)}
                      rows={3}
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="post-category">Category</Label>
                    <Select value={newPostCategory} onValueChange={setNewPostCategory}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.name} value={category.name}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid gap-2">
                    <Label>SEO Keywords (Optional)</Label>
                    <Input placeholder="Enter keywords separated by commas" />
                    <p className="text-xs text-muted-foreground">
                      Adding relevant keywords will help with search engine optimization.
                    </p>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsNewPostDialogOpen(false)}>Cancel</Button>
                  <Button onClick={handleCreatePost}>Create Draft</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
              <div className="flex">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span>{error}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </div>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Author</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Published</TableHead>
                  <TableHead>Views</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  // Loading skeleton rows
                  Array.from({ length: 3 }).map((_, index) => (
                    <TableRow key={`loading-${index}`}>
                      <TableCell>
                        <div className="h-5 w-40 bg-slate-200 dark:bg-slate-700 rounded animate-pulse mb-2"></div>
                        <div className="h-3 w-64 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-4 w-20 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-5 w-24 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-5 w-16 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-4 w-24 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-4 w-12 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <div className="h-8 w-16 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                          <div className="h-8 w-16 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : filteredPosts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6">
                      <p className="text-muted-foreground">
                        {searchQuery || statusFilter !== "all"
                          ? "No posts found matching your filters"
                          : "No blog posts found in the database"}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() => setIsNewPostDialogOpen(true)}
                      >
                        Create Your First Post
                      </Button>
                    </TableCell>
                  </TableRow>
                ) : (
                  // Actual post rows
                  filteredPosts.map((post) => (
                    <TableRow key={post.id}>
                      <TableCell className="font-medium">
                        <div>
                          <p>{post.title}</p>
                          <p className="text-xs text-muted-foreground truncate max-w-xs">
                            {post.excerpt}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>{post.author}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{post.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          post.status === "published" ? "default" :
                          post.status === "draft" ? "secondary" :
                          "outline"
                        }>
                          {post.status.charAt(0).toUpperCase() + post.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {post.publishedAt
                          ? new Date(post.publishedAt).toLocaleDateString()
                          : "—"}
                      </TableCell>
                      <TableCell>{post.views.toLocaleString()}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button variant="ghost" size="sm" disabled={loading}>Edit</Button>
                          {post.status === "draft" && (
                            <Button variant="ghost" size="sm" disabled={loading}>Publish</Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-500 hover:text-red-600 hover:bg-red-50"
                            disabled={loading}
                          >
                            Delete
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <SectionTitle
            title="Blog Categories"
            description="Manage categories for organizing blog content"
          />

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
              <div className="flex">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span>{error}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {loading ? (
              // Loading skeleton cards
              Array.from({ length: 3 }).map((_, index) => (
                <Card key={`loading-category-${index}`}>
                  <CardHeader className="pb-2">
                    <div className="h-6 w-32 bg-slate-200 dark:bg-slate-700 rounded animate-pulse mb-2"></div>
                    <div className="h-4 w-24 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-2 bg-slate-200 dark:bg-slate-700 rounded-full animate-pulse"></div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <div className="h-8 w-16 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                    <div className="h-8 w-24 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                  </CardFooter>
                </Card>
              ))
            ) : categories.length === 0 ? (
              <Card className="col-span-full">
                <CardContent className="flex flex-col items-center justify-center py-10">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-slate-400 mb-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M2 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 002 2H4a2 2 0 01-2-2V5zm3 1h6v4H5V6zm6 6H5v2h6v-2z" clipRule="evenodd" />
                    <path d="M15 7h1a2 2 0 012 2v5.5a1.5 1.5 0 01-3 0V7z" />
                  </svg>
                  <h3 className="text-lg font-medium mb-1">No Categories Found</h3>
                  <p className="text-sm text-slate-500 text-center mb-4">
                    You don't have any blog categories yet.
                  </p>
                  <Button variant="outline">Create Your First Category</Button>
                </CardContent>
              </Card>
            ) : (
              // Actual category cards
              categories.map((category) => (
                <Card key={category.name}>
                  <CardHeader className="pb-2">
                    <CardTitle>{category.name}</CardTitle>
                    <CardDescription>{category.count} posts</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-2 bg-muted rounded-full overflow-hidden">
                      <div
                        className="h-full bg-primary"
                        style={{ width: `${(category.count / Math.max(...categories.map(c => c.count), 1)) * 100}%` }}
                      ></div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="ghost" size="sm" disabled={loading}>Edit</Button>
                    <Button variant="ghost" size="sm" disabled={loading}>View Posts</Button>
                  </CardFooter>
                </Card>
              ))
            )}

            {!loading && (
              <Card className="border-dashed">
                <CardHeader className="pb-2">
                  <CardTitle>Create Category</CardTitle>
                  <CardDescription>Add a new blog category</CardDescription>
                </CardHeader>
                <CardContent className="flex items-center justify-center py-6">
                  <Button variant="outline" disabled={loading}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                      <path d="M12 5v14M5 12h14"></path>
                    </svg>
                    Add Category
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="comments" className="space-y-6">
          <SectionTitle
            title="Blog Comments"
            description="Manage and moderate user comments"
          />

          <div className="bg-card rounded-lg border p-6 text-center">
            <h3 className="text-lg font-semibold mb-2">Comment Management</h3>
            <p className="text-muted-foreground mb-6">
              Comment management features will be displayed here.
            </p>
            <Button>View Comments</Button>
          </div>
        </TabsContent>

        <TabsContent value="seo" className="space-y-6">
          <SectionTitle
            title="SEO Optimization"
            description="Optimize blog content for search engines"
          />

          <Card>
            <CardHeader>
              <CardTitle>Keyword Analysis</CardTitle>
              <CardDescription>
                Track performance of target keywords
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Keyword</TableHead>
                      <TableHead>Search Volume</TableHead>
                      <TableHead>Difficulty</TableHead>
                      <TableHead>CPC</TableHead>
                      <TableHead>Articles</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      // Loading skeleton rows
                      Array.from({ length: 3 }).map((_, index) => (
                        <TableRow key={`loading-keyword-${index}`}>
                          <TableCell>
                            <div className="h-5 w-32 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                          </TableCell>
                          <TableCell>
                            <div className="h-5 w-20 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <div className="w-16 h-2 bg-slate-200 dark:bg-slate-700 rounded-full animate-pulse mr-2"></div>
                              <div className="h-5 w-12 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="h-5 w-16 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                          </TableCell>
                          <TableCell>
                            <div className="h-5 w-24 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="h-8 w-20 bg-slate-200 dark:bg-slate-700 rounded animate-pulse ml-auto"></div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : seoKeywords.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-6">
                          <p className="text-muted-foreground">No SEO keywords found</p>
                          <Button
                            variant="outline"
                            size="sm"
                            className="mt-2"
                          >
                            Add Your First Keyword
                          </Button>
                        </TableCell>
                      </TableRow>
                    ) : (
                      // Actual keyword rows
                      seoKeywords.map((keyword) => (
                        <TableRow key={keyword.keyword}>
                          <TableCell className="font-medium">{keyword.keyword}</TableCell>
                          <TableCell>{keyword.volume.toLocaleString()}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <div className="w-16 h-2 bg-muted rounded-full overflow-hidden mr-2">
                                <div
                                  className={`h-full ${
                                    keyword.difficulty < 40 ? "bg-green-500" :
                                    keyword.difficulty < 60 ? "bg-amber-500" :
                                    "bg-red-500"
                                  }`}
                                  style={{ width: `${keyword.difficulty}%` }}
                                ></div>
                              </div>
                              <span>{keyword.difficulty}/100</span>
                            </div>
                          </TableCell>
                          <TableCell>${keyword.cpc.toFixed(2)}</TableCell>
                          <TableCell>
                            <Badge variant="outline">3 articles</Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" disabled={loading}>Analyze</Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline">Add Keyword</Button>
              <Button>Run SEO Analysis</Button>
            </CardFooter>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Content Optimization</CardTitle>
                <CardDescription>
                  AI-powered content optimization suggestions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Average Content Score</span>
                    <div className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-amber-500 mr-2"></div>
                      <span className="font-medium">72/100</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Readability Score</span>
                    <div className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-green-500 mr-2"></div>
                      <span className="font-medium">Good</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Keyword Density</span>
                    <div className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-amber-500 mr-2"></div>
                      <span className="font-medium">Needs Improvement</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Meta Descriptions</span>
                    <div className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-red-500 mr-2"></div>
                      <span className="font-medium">Missing (2)</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full">Optimize Content</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>SEO Performance</CardTitle>
                <CardDescription>
                  Track search engine performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px] flex items-center justify-center bg-muted rounded-md">
                  <p className="text-muted-foreground">SEO performance chart would be displayed here</p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <span className="text-sm text-muted-foreground">Last updated: Today</span>
                <Button variant="outline">Refresh Data</Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      <SectionTitle
        title="Content Calendar"
        description="Plan and schedule upcoming blog content"
      />

      <Card>
        <CardHeader>
          <CardTitle>Content Schedule</CardTitle>
          <CardDescription>
            Upcoming and scheduled blog posts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center bg-muted rounded-md">
            <p className="text-muted-foreground">Content calendar would be displayed here</p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline">View Calendar</Button>
          <Button>Schedule Post</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
