"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  TruckIcon,
  DevicePhoneMobileIcon,
  LinkIcon,
  UserIcon,
  MapPinIcon,
  CreditCardIcon,
  CalendarIcon,
} from "@heroicons/react/24/outline";
import { Loading } from "@/components/ui/loading";
import { toast } from "sonner";
import { useOrderStatusStream } from "@/hooks/useOrderStatusStream";
import { validateTrackingNumber, detectCarrier, getSupportedCarriers } from "@/lib/shipping-service";

interface Order {
  id: string;
  subscriptionPlan: string;
  amount: number;
  status: string;
  paymentId: string;
  trackingNumber?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  shippingAddress?: any;
  user: {
    id: string;
    name: string;
    email: string;
    shippingAddress?: any;
  };
  device?: {
    id: string;
    name: string;
    model?: string;
    deviceType: string;
    status: string;
    rustDeskId?: string;
    hasCredentials: boolean;
  };
  statusHistory: Array<{
    id: string;
    status: string;
    notes?: string;
    adminUserId?: string;
    createdAt: string;
  }>;
}

interface Device {
  id: string;
  name: string;
  model?: string;
  deviceType: string;
  status: string;
}

export default function OrderFulfillmentPage() {
  const params = useParams();
  const router = useRouter();
  const orderId = params.id as string;

  const [order, setOrder] = useState<Order | null>(null);
  const [availableDevices, setAvailableDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [selectedDeviceId, setSelectedDeviceId] = useState('');
  const [trackingNumber, setTrackingNumber] = useState('');
  const [notes, setNotes] = useState('');

  useEffect(() => {
    fetchOrder();
  }, [orderId]);

  // Real-time status updates
  const { isConnected: streamConnected } = useOrderStatusStream({
    orderId: order?.id || '',
    enabled: !!order?.id,
    onUpdate: (update) => {
      if (update.type === 'order_update' && order) {
        // Update order state with real-time data
        setOrder(prevOrder => {
          if (!prevOrder) return prevOrder;
          return {
            ...prevOrder,
            status: update.status || prevOrder.status,
            trackingNumber: update.trackingNumber || prevOrder.trackingNumber,
            updatedAt: update.updatedAt || prevOrder.updatedAt,
          };
        });

        // Refresh full order data to get updated status history
        if (update.adminUpdate) {
          // Small delay to ensure database is updated
          setTimeout(() => {
            fetchOrder();
          }, 500);
        }
      }
    },
  });

  const fetchOrder = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/orders/${orderId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch order');
      }
      
      const data = await response.json();
      setOrder(data.order);
      setAvailableDevices(data.availableDevices);
      
      // Pre-fill form with existing data
      if (data.order.device) {
        setSelectedDeviceId(data.order.device.id);
      }
      if (data.order.trackingNumber) {
        setTrackingNumber(data.order.trackingNumber);
      }
      if (data.order.notes) {
        setNotes(data.order.notes);
      }
    } catch (err) {
      console.error('Error fetching order:', err);
      setError('Failed to load order');
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (newStatus: string, additionalData: any = {}) => {
    try {
      setUpdating(true);
      const response = await fetch(`/api/admin/orders/${orderId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          notes,
          ...additionalData,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update order');
      }

      toast.success(`Order status updated to ${newStatus}`);
      await fetchOrder(); // Refresh data
    } catch (err) {
      console.error('Error updating order:', err);
      toast.error('Failed to update order');
    } finally {
      setUpdating(false);
    }
  };

  const assignDevice = async () => {
    if (!selectedDeviceId) {
      toast.error('Please select a device');
      return;
    }

    await updateOrderStatus('device_assigned', { deviceId: selectedDeviceId });
  };

  const markAsShipped = async () => {
    if (!trackingNumber) {
      toast.error('Please enter a tracking number');
      return;
    }

    await updateOrderStatus('shipped', { trackingNumber });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'device_prep':
        return <ClockIcon className="h-5 w-5 text-blue-500" />;
      case 'device_assigned':
        return <DevicePhoneMobileIcon className="h-5 w-5 text-purple-500" />;
      case 'shipped':
        return <TruckIcon className="h-5 w-5 text-orange-500" />;
      case 'delivered':
      case 'connected':
      case 'active':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (loading) {
    return <Loading fullScreen message="Loading order details..." />;
  }

  if (error || !order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Order</h2>
        <p className="text-gray-600 mb-4">{error || 'Order not found'}</p>
        <Button onClick={() => router.push('/admin/orders')}>Back to Orders</Button>
      </div>
    );
  }

  const shippingAddress = order.shippingAddress || order.user.shippingAddress;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/admin/orders')}
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Order #{order.id.slice(-8)}
            </h1>
            <p className="text-muted-foreground">
              Fulfill customer order and manage device shipping
            </p>
          </div>
        </div>

        {/* Real-time status indicator */}
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${streamConnected ? 'bg-green-500' : 'bg-gray-400'}`}></div>
          <span className="text-xs text-muted-foreground">
            {streamConnected ? 'Live sync' : 'Connecting...'}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Order Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <UserIcon className="h-5 w-5 mr-2" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Name</p>
                  <p className="font-medium">{order.user.name}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <p className="font-medium">{order.user.email}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Plan</p>
                  <p className="font-medium">
                    {order.subscriptionPlan.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Amount</p>
                  <p className="font-medium">{formatCurrency(order.amount)}</p>
                </div>
              </div>

              {shippingAddress && (
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Shipping Address</p>
                  <div className="bg-muted p-3 rounded-md">
                    <p>{shippingAddress.line1}</p>
                    {shippingAddress.line2 && <p>{shippingAddress.line2}</p>}
                    <p>
                      {shippingAddress.city}, {shippingAddress.state} {shippingAddress.postal_code}
                    </p>
                    <p>{shippingAddress.country}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Fulfillment Checklist */}
          <Card>
            <CardHeader>
              <CardTitle>Fulfillment Checklist</CardTitle>
              <CardDescription>
                Complete these steps to fulfill the customer's order
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Step 1: Payment Verification */}
              <div className="flex items-start space-x-3">
                <Checkbox checked disabled />
                <div className="flex-1">
                  <h4 className="font-medium">Payment Verified</h4>
                  <p className="text-sm text-muted-foreground">
                    Payment ID: {order.paymentId}
                  </p>
                </div>
              </div>

              <Separator />

              {/* Step 2: Device Assignment */}
              <div className="flex items-start space-x-3">
                <Checkbox checked={!!order.device} disabled />
                <div className="flex-1 space-y-3">
                  <div>
                    <h4 className="font-medium">Assign Device</h4>
                    <p className="text-sm text-muted-foreground">
                      Select and assign a mobile device to this order
                    </p>
                  </div>
                  
                  {!order.device ? (
                    <div className="space-y-3">
                      <Select value={selectedDeviceId} onValueChange={setSelectedDeviceId}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a device" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableDevices.map((device) => (
                            <SelectItem key={device.id} value={device.id}>
                              <div className="flex flex-col">
                                <span>{device.name} ({device.model || device.deviceType})</span>
                                <span className="text-xs text-muted-foreground">
                                  RustDesk ID: {device.rustDeskId || 'Not configured'}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button 
                        onClick={assignDevice} 
                        disabled={!selectedDeviceId || updating}
                        size="sm"
                      >
                        Assign Device
                      </Button>
                    </div>
                  ) : (
                    <div className="bg-green-50 p-3 rounded-md">
                      <p className="font-medium text-green-800">
                        Device Assigned: {order.device.name}
                      </p>
                      <p className="text-sm text-green-600">
                        {order.device.model || order.device.deviceType} • Status: {order.device.status}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Step 3: Device Configuration */}
              <div className="flex items-start space-x-3">
                <Checkbox checked={order.device?.hasCredentials} disabled />
                <div className="flex-1">
                  <h4 className="font-medium">Configure RustDesk</h4>
                  <p className="text-sm text-muted-foreground">
                    Set up secure remote access credentials
                  </p>
                  {order.device?.hasCredentials && (
                    <div className="mt-2 bg-green-50 p-3 rounded-md">
                      <p className="text-sm text-green-800">
                        ✓ RustDesk credentials configured
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Step 4: Shipping */}
              <div className="flex items-start space-x-3">
                <Checkbox checked={order.status === 'shipped' || order.status === 'delivered'} disabled />
                <div className="flex-1 space-y-3">
                  <div>
                    <h4 className="font-medium">Ship Device</h4>
                    <p className="text-sm text-muted-foreground">
                      Package and ship the device to the customer
                    </p>
                  </div>
                  
                  {order.status !== 'shipped' && order.status !== 'delivered' ? (
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Input
                          placeholder="Enter tracking number (e.g., 1Z999AA1234567890)"
                          value={trackingNumber}
                          onChange={(e) => {
                            const value = e.target.value;
                            setTrackingNumber(value);

                            // Validate and show carrier detection
                            if (value.trim()) {
                              const validation = validateTrackingNumber(value);
                              if (validation.isValid && validation.carrier) {
                                const carrier = validation.carrier.toUpperCase();
                                console.log(`Detected carrier: ${carrier}`);
                              }
                            }
                          }}
                        />
                        {trackingNumber && (
                          <div className="text-xs text-muted-foreground">
                            {(() => {
                              const validation = validateTrackingNumber(trackingNumber);
                              if (validation.isValid && validation.carrier) {
                                return `Detected carrier: ${validation.carrier.toUpperCase()}`;
                              } else if (!validation.isValid) {
                                return `⚠️ ${validation.error}`;
                              }
                              return '';
                            })()}
                          </div>
                        )}
                      </div>
                      <Button
                        onClick={markAsShipped}
                        disabled={!trackingNumber || updating}
                        size="sm"
                      >
                        Mark as Shipped
                      </Button>
                    </div>
                  ) : (
                    <div className="bg-green-50 p-3 rounded-md">
                      <p className="font-medium text-green-800">
                        ✓ Device Shipped
                      </p>
                      {order.trackingNumber && (
                        <div className="text-sm text-green-600 space-y-1">
                          <p>Tracking: {order.trackingNumber}</p>
                          <p>Carrier: {detectCarrier(order.trackingNumber).toUpperCase()}</p>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const url = `https://tools.usps.com/go/TrackConfirmAction?tLabels=${order.trackingNumber}`;
                              window.open(url, '_blank');
                            }}
                          >
                            Track Package
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Notes */}
              <div className="space-y-3">
                <h4 className="font-medium">Order Notes</h4>
                <Textarea
                  placeholder="Add notes about this order..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                />
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => updateOrderStatus(order.status, {})}
                  disabled={updating}
                >
                  Save Notes
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Order Status */}
          <Card>
            <CardHeader>
              <CardTitle>Order Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                {getStatusIcon(order.status)}
                <span className="font-medium capitalize">
                  {order.status.replace('_', ' ')}
                </span>
              </div>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>Created: {formatDate(order.createdAt)}</p>
                <p>Updated: {formatDate(order.updatedAt)}</p>
              </div>
            </CardContent>
          </Card>

          {/* Status History */}
          <Card>
            <CardHeader>
              <CardTitle>Status History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {order.statusHistory.map((history, index) => (
                  <div key={history.id} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="font-medium text-sm capitalize">
                        {history.status.replace('_', ' ')}
                      </p>
                      {history.notes && (
                        <p className="text-xs text-muted-foreground">
                          {history.notes}
                        </p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        {formatDate(history.createdAt)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
