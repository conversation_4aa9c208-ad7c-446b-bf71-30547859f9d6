'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, Download, FileText } from 'lucide-react';

export default function GenerateInvoicePage() {
  const [formData, setFormData] = useState({
    paymentId: '',
    customerName: '',
    customerEmail: '',
    billingAddress: '',
    planType: 'single-user',
    amount: '19900', // $199 in cents
    couponCode: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [invoice, setInvoice] = useState<any>(null);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/invoices/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setInvoice(data.invoice);
      } else {
        throw new Error(data.error || 'Failed to generate invoice');
      }
    } catch (error) {
      console.error('Error generating invoice:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate invoice');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    if (invoice?.html) {
      const blob = new Blob([invoice.html], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `invoice-${invoice.invoiceNumber}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handlePrint = () => {
    if (invoice?.html) {
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(invoice.html);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Generate Invoice</h1>
          <p className="text-muted-foreground">Create an invoice for Square payment verification</p>
        </div>

        {!invoice ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Invoice Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="paymentId">PayPal Payment ID</Label>
                    <Input
                      id="paymentId"
                      value={formData.paymentId}
                      onChange={(e) => setFormData(prev => ({ ...prev, paymentId: e.target.value }))}
                      placeholder="e.g., BcSEtUPovoYcte4cjzCxQwfsV3CZY"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="customerName">Customer Name</Label>
                    <Input
                      id="customerName"
                      value={formData.customerName}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                      placeholder="Customer's full name"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="customerEmail">Customer Email</Label>
                    <Input
                      id="customerEmail"
                      type="email"
                      value={formData.customerEmail}
                      onChange={(e) => setFormData(prev => ({ ...prev, customerEmail: e.target.value }))}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="planType">Plan Type</Label>
                    <select
                      id="planType"
                      value={formData.planType}
                      onChange={(e) => setFormData(prev => ({ ...prev, planType: e.target.value }))}
                      className="w-full p-2 border border-input rounded-md"
                    >
                      <option value="single-user">Single User Plan ($199)</option>
                      <option value="two-user">Two User Bundle ($298)</option>
                    </select>
                  </div>
                  
                  <div>
                    <Label htmlFor="amount">Amount (in cents)</Label>
                    <Input
                      id="amount"
                      value={formData.amount}
                      onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                      placeholder="19900 for $199.00"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="couponCode">Coupon Code (if used)</Label>
                    <Input
                      id="couponCode"
                      value={formData.couponCode}
                      onChange={(e) => setFormData(prev => ({ ...prev, couponCode: e.target.value }))}
                      placeholder="SPEARMINT"
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="billingAddress">Billing Address</Label>
                  <Textarea
                    id="billingAddress"
                    value={formData.billingAddress}
                    onChange={(e) => setFormData(prev => ({ ...prev, billingAddress: e.target.value }))}
                    placeholder="Customer's billing address"
                    rows={3}
                  />
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <Button type="submit" disabled={loading} className="w-full">
                  {loading ? 'Generating...' : 'Generate Invoice'}
                </Button>
              </form>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="h-5 w-5" />
                  Invoice Generated Successfully
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>Invoice Number:</strong> {invoice.invoiceNumber}
                    </div>
                    <div>
                      <strong>Amount:</strong> ${invoice.amount.toFixed(2)}
                    </div>
                    <div>
                      <strong>Status:</strong> {invoice.status}
                    </div>
                    <div>
                      <strong>Date:</strong> {invoice.date}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={handleDownload} variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Download HTML
                    </Button>
                    <Button onClick={handlePrint} variant="outline">
                      <FileText className="h-4 w-4 mr-2" />
                      Print/PDF
                    </Button>
                    <Button onClick={() => setInvoice(null)} variant="secondary">
                      Generate Another
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Invoice Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Invoice Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  className="border rounded-lg p-4 bg-white"
                  dangerouslySetInnerHTML={{ __html: invoice.html }}
                />
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
