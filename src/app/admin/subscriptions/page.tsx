"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { UnifiedSubscriptionManagement } from "@/components/admin/unified-subscription-management";
import { getAllClientSubscriptions, getClientSubscription, updateClientSubscription } from "@/app/actions/subscriptions";

// Client subscription interface
interface ClientSubscription {
  id: string;
  name: string;
  service: string;
  plan: string;
  status: string;
  expires: string;
  devices: number;
}

export default function SubscriptionsPage() {
  const [clients, setClients] = useState<ClientSubscription[]>([]);
  const [selectedClient, setSelectedClient] = useState<ClientSubscription | null>(null);
  const [isManageDialogOpen, setIsManageDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load client subscriptions on component mount
  useEffect(() => {
    async function loadClientSubscriptions() {
      try {
        setLoading(true);
        setError(null);

        // Get client subscriptions
        const clientsData = await getAllClientSubscriptions();
        setClients(clientsData);

        setLoading(false);
      } catch (err) {
        console.error("Error loading client subscriptions:", err);
        setError("Failed to load client subscriptions. Please try again.");
        setLoading(false);
      }
    }

    loadClientSubscriptions();
  }, []);

  const handleManageSubscription = async (client: ClientSubscription) => {
    try {
      setLoading(true);

      // Get the latest client subscription data
      const clientData = await getClientSubscription(client.id);

      setSelectedClient(clientData);
      setIsManageDialogOpen(true);
      setLoading(false);
    } catch (err) {
      console.error("Error getting client subscription:", err);
      // Fallback to using the client data we already have
      setSelectedClient(client);
      setIsManageDialogOpen(true);
      setLoading(false);
    }
  };

  const getServiceIcon = (service: string) => {
    if (service === "rustdesk") {
      return (
        <img
          src="/images/rustdesk-logo.png"
          alt="RustDesk"
          className="h-5 w-5"
          onError={(e) => {
            // Fallback if image doesn't exist
            e.currentTarget.style.display = 'none';
          }}
        />
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Remote Access Subscriptions</h1>
        <p className="text-slate-500 dark:text-slate-400">Manage client subscriptions for remote access services</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Client Subscriptions</CardTitle>
          <CardDescription>
            View and manage remote access service subscriptions for all clients
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
              <div className="flex">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <span>{error}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </div>
          )}

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Client</TableHead>
                  <TableHead>Service</TableHead>
                  <TableHead>Plan</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Devices</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  // Loading skeleton rows
                  Array.from({ length: 3 }).map((_, index) => (
                    <TableRow key={`loading-${index}`}>
                      <TableCell>
                        <div className="h-5 w-32 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="h-5 w-5 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                          <div className="h-5 w-20 bg-slate-200 dark:bg-slate-700 rounded animate-pulse ml-2"></div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="h-5 w-24 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-5 w-16 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-5 w-24 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-5 w-8 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="h-8 w-20 bg-slate-200 dark:bg-slate-700 rounded animate-pulse ml-auto"></div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : clients.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6">
                      <p className="text-muted-foreground">No client subscriptions found</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  // Actual client rows
                  clients.map((client) => (
                    <TableRow key={client.id}>
                      <TableCell className="font-medium">{client.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {getServiceIcon(client.service)}
                          <span className="ml-2">
                            RustDesk
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="capitalize">{client.plan}</TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          client.status === "active"
                            ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                            : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                        }`}>
                          {client.status}
                        </span>
                      </TableCell>
                      <TableCell>{client.expires}</TableCell>
                      <TableCell>{client.devices}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleManageSubscription(client)}
                          disabled={loading}
                        >
                          Manage
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Dialog open={isManageDialogOpen} onOpenChange={setIsManageDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Manage Subscription</DialogTitle>
            <DialogDescription>
              Update the remote access subscription for {selectedClient?.name}
            </DialogDescription>
          </DialogHeader>

          {selectedClient && (
            <UnifiedSubscriptionManagement
              clientId={selectedClient.id}
              clientName={selectedClient.name}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
