"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon, 
  ExclamationTriangleIcon,
  CreditCardIcon,
  UserIcon
} from "@heroicons/react/24/outline";

interface Subscription {
  id: string;
  userId: string;
  user: {
    id: string;
    email: string;
    name: string;
    createdAt: string;
  };
  subscriptionPlan: string;
  amount: number;
  currency: string;
  paymentId: string;
  status: string;
  subscriptionStatus: string;
  paymentStatus: string;
  deviceAccess: string;
  nextBillingDate: string | null;
  createdAt: string;
  updatedAt: string;
  notes: any;
}

interface TestPayment {
  id: string;
  paymentId: string;
  amount: number;
  currency: string;
  status: string;
  subscriptionPlan: string;
  createdAt: string;
  notes: any;
}

export default function SubscriptionMonitorPage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [testPayments, setTestPayments] = useState<TestPayment[]>([]);
  const [summary, setSummary] = useState({ total: 0, active: 0, pastDue: 0, unpaid: 0 });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  
  // Test payment form
  const [testAmount, setTestAmount] = useState("100"); // $1.00
  const [testDescription, setTestDescription] = useState("Test Payment");
  const [testType, setTestType] = useState("payment");
  const [testLoading, setTestLoading] = useState(false);

  useEffect(() => {
    fetchSubscriptions();
    fetchTestPayments();
  }, [statusFilter]);

  const fetchSubscriptions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }
      
      const response = await fetch(`/api/admin/subscriptions?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setSubscriptions(data.subscriptions);
        setSummary(data.summary);
      } else {
        setError(data.error || "Failed to fetch subscriptions");
      }
    } catch (err) {
      setError("Error fetching subscriptions");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchTestPayments = async () => {
    try {
      const response = await fetch("/api/test-payment");
      const data = await response.json();
      
      if (data.success) {
        setTestPayments(data.testPayments);
      }
    } catch (err) {
      console.error("Error fetching test payments:", err);
    }
  };

  const handleSubscriptionAction = async (subscriptionId: string, action: string, reason?: string) => {
    try {
      const response = await fetch("/api/admin/subscriptions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action, subscriptionId, reason })
      });
      
      const data = await response.json();
      
      if (data.success) {
        fetchSubscriptions(); // Refresh data
        alert(`Subscription ${action} successful`);
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (err) {
      alert("Error performing action");
      console.error(err);
    }
  };

  const handleTestPayment = async () => {
    try {
      setTestLoading(true);
      const response = await fetch("/api/test-payment", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          amount: parseInt(testAmount),
          description: testDescription,
          testType: testType
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        alert("Test payment created successfully!");
        fetchTestPayments(); // Refresh test payments
        
        // Open PayPal approval URL if available
        if (data.payment?.approvalUrl) {
          window.open(data.payment.approvalUrl, '_blank');
        }
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (err) {
      alert("Error creating test payment");
      console.error(err);
    } finally {
      setTestLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500"><CheckCircleIcon className="w-4 h-4 mr-1" />Active</Badge>;
      case 'past_due':
        return <Badge className="bg-yellow-500"><ClockIcon className="w-4 h-4 mr-1" />Past Due</Badge>;
      case 'unpaid':
        return <Badge className="bg-red-500"><XCircleIcon className="w-4 h-4 mr-1" />Unpaid</Badge>;
      case 'cancelled':
        return <Badge variant="secondary"><XCircleIcon className="w-4 h-4 mr-1" />Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getDeviceAccessBadge = (access: string) => {
    switch (access) {
      case 'active':
        return <Badge className="bg-green-500">✓ Active</Badge>;
      case 'revoked':
        return <Badge className="bg-red-500">✗ Revoked</Badge>;
      default:
        return <Badge variant="outline">{access}</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Subscription Monitor</h1>
        <p className="text-muted-foreground">Monitor customer subscriptions and manage device access</p>
      </div>

      {error && (
        <Alert className="mb-6">
          <ExclamationTriangleIcon className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="subscriptions" className="space-y-6">
        <TabsList>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="test-payments">Test Payments</TabsTrigger>
        </TabsList>

        <TabsContent value="subscriptions" className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Subscriptions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summary.total}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Active</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{summary.active}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Past Due</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{summary.pastDue}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Unpaid</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{summary.unpaid}</div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div>
                  <Label htmlFor="status-filter">Status Filter</Label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="past_due">Past Due</SelectItem>
                      <SelectItem value="unpaid">Unpaid</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button onClick={fetchSubscriptions} disabled={loading}>
                    Refresh
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Subscriptions List */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Subscriptions</CardTitle>
              <CardDescription>Monitor payment status and manage device access</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">Loading subscriptions...</div>
              ) : subscriptions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">No subscriptions found</div>
              ) : (
                <div className="space-y-4">
                  {subscriptions.map((subscription) => (
                    <div key={subscription.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <UserIcon className="w-4 h-4" />
                            <span className="font-medium">{subscription.user.email}</span>
                            <span className="text-sm text-muted-foreground">({subscription.user.name})</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <CreditCardIcon className="w-4 h-4" />
                            <span>{subscription.subscriptionPlan}</span>
                            <span>•</span>
                            <span>${(subscription.amount / 100).toFixed(2)} {subscription.currency}</span>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          {getStatusBadge(subscription.subscriptionStatus)}
                          {getDeviceAccessBadge(subscription.deviceAccess)}
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                        <div>
                          <span className="text-muted-foreground">Payment Status:</span>
                          <div>{subscription.paymentStatus}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Created:</span>
                          <div>{new Date(subscription.createdAt).toLocaleDateString()}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Next Billing:</span>
                          <div>{subscription.nextBillingDate ? new Date(subscription.nextBillingDate).toLocaleDateString() : 'N/A'}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Payment ID:</span>
                          <div className="font-mono text-xs">{subscription.paymentId}</div>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleSubscriptionAction(subscription.id, 'check_status')}
                        >
                          Check Status
                        </Button>
                        {subscription.subscriptionStatus === 'active' && (
                          <Button 
                            size="sm" 
                            variant="destructive"
                            onClick={() => {
                              const reason = prompt("Reason for cancellation:");
                              if (reason) handleSubscriptionAction(subscription.id, 'cancel', reason);
                            }}
                          >
                            Cancel Subscription
                          </Button>
                        )}
                        {subscription.subscriptionStatus === 'cancelled' && (
                          <Button 
                            size="sm" 
                            onClick={() => handleSubscriptionAction(subscription.id, 'reactivate')}
                          >
                            Reactivate
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="test-payments" className="space-y-6">
          {/* Test Payment Form */}
          <Card>
            <CardHeader>
              <CardTitle>Create Test Payment</CardTitle>
              <CardDescription>Test PayPal integration with small amounts</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="test-amount">Amount (cents)</Label>
                  <Input
                    id="test-amount"
                    type="number"
                    min="1"
                    max="1000"
                    value={testAmount}
                    onChange={(e) => setTestAmount(e.target.value)}
                    placeholder="100 ($1.00)"
                  />
                </div>
                <div>
                  <Label htmlFor="test-type">Test Type</Label>
                  <Select value={testType} onValueChange={setTestType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="payment">One-time Payment</SelectItem>
                      <SelectItem value="subscription">Subscription</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="test-description">Description</Label>
                  <Input
                    id="test-description"
                    value={testDescription}
                    onChange={(e) => setTestDescription(e.target.value)}
                    placeholder="Test Payment"
                  />
                </div>
              </div>
              <Button onClick={handleTestPayment} disabled={testLoading}>
                {testLoading ? "Creating..." : "Create Test Payment"}
              </Button>
            </CardContent>
          </Card>

          {/* Test Payment History */}
          <Card>
            <CardHeader>
              <CardTitle>Test Payment History</CardTitle>
              <CardDescription>Recent test payments and their status</CardDescription>
            </CardHeader>
            <CardContent>
              {testPayments.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">No test payments found</div>
              ) : (
                <div className="space-y-3">
                  {testPayments.map((payment) => (
                    <div key={payment.id} className="border rounded-lg p-3">
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium">${(payment.amount / 100).toFixed(2)} {payment.currency}</div>
                          <div className="text-sm text-muted-foreground">
                            {payment.subscriptionPlan} • {new Date(payment.createdAt).toLocaleString()}
                          </div>
                          <div className="text-xs font-mono text-muted-foreground">{payment.paymentId}</div>
                        </div>
                        <div>
                          {getStatusBadge(payment.status)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
