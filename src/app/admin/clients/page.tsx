"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { UsersIcon, PlusIcon, ExclamationCircleIcon, DevicePhoneMobileIcon, ArrowPathIcon, ServerIcon } from "@heroicons/react/24/outline";
import { getAllClients, getClientById, updateClientSubscriptionStatus } from "@/lib/admin-clients";
import { testDatabaseConnection, getDatabaseInfo } from "@/lib/db-test";

// Client type definition
interface Client {
  id: string;
  name: string;
  email: string;
  company: string;
  phone: string;
  subscription: {
    id: string;
    status: string;
    currentPeriodEnd: string;
    plan: string;
    price: string;
  };
  devices: Array<{
    id: string;
    name: string;
    deviceModel: string;
    deviceId: string;
    status: string;
  }>;
  createdAt: string;
}

export default function ClientsPage() {
  const [clients, setClients] = useState<Client[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isClientDetailsOpen, setIsClientDetailsOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [isConfirmRevokeOpen, setIsConfirmRevokeOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dbStatus, setDbStatus] = useState<{connected: boolean, error: string | null} | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);

  // Load clients data on component mount
  useEffect(() => {
    async function loadClients() {
      try {
        setLoading(true);
        setError(null);
        setIsRetrying(false);

        // First test database connection
        const connectionStatus = await testDatabaseConnection();
        setDbStatus(connectionStatus);

        if (!connectionStatus.connected) {
          throw new Error(`Database connection failed: ${connectionStatus.error}`);
        }

        const clientsData = await getAllClients();

        // Add default values for fields that might be missing
        const formattedClients = clientsData.map(client => ({
          ...client,
          phone: client.phone || "",
          company: client.company || "",
          subscription: {
            ...client.subscription,
            plan: client.subscription.plan || "Basic Plan",
            price: client.subscription.price || "$350.00",
          }
        }));

        setClients(formattedClients);
        setLoading(false);
      } catch (err) {
        console.error("Error loading clients:", err);
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        setError(`Failed to load clients: ${errorMessage}`);
        setLoading(false);
      }
    }

    loadClients();
  }, [isRetrying]);

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.company.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleViewClient = async (client: Client) => {
    try {
      setLoading(true);
      // Get the latest client data from the database
      const clientData = await getClientById(client.id);

      // Add default values for fields that might be missing
      const formattedClient = {
        ...clientData,
        phone: clientData.phone || "",
        company: clientData.company || "",
        subscription: {
          ...clientData.subscription,
          plan: clientData.subscription.plan || "Basic Plan",
          price: clientData.subscription.price || "$350.00",
        }
      };

      setSelectedClient(formattedClient);
      setIsClientDetailsOpen(true);
      setLoading(false);
    } catch (err) {
      console.error("Error getting client details:", err);
      // Fallback to using the client data we already have
      setSelectedClient(client);
      setIsClientDetailsOpen(true);
      setLoading(false);
    }
  };

  const handleRevokeAccess = async () => {
    if (!selectedClient) return;

    try {
      setLoading(true);

      // Call the API to revoke access
      await updateClientSubscriptionStatus(selectedClient.id, "canceled");

      // Update the clients list
      setClients(clients.map(client =>
        client.id === selectedClient.id
          ? {
              ...client,
              subscription: {
                ...client.subscription,
                status: "canceled"
              },
              devices: client.devices.map(device => ({
                ...device,
                status: "unassigned"
              }))
            }
          : client
      ));

      // Update the selected client to reflect changes
      setSelectedClient({
        ...selectedClient,
        subscription: {
          ...selectedClient.subscription,
          status: "canceled"
        },
        devices: selectedClient.devices.map((device) => ({
          ...device,
          status: "unassigned"
        }))
      });

      setIsConfirmRevokeOpen(false);
      setLoading(false);
    } catch (err) {
      console.error("Error revoking access:", err);
      setError("Failed to revoke access. Please try again.");
      setIsConfirmRevokeOpen(false);
      setLoading(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case "past_due":
        return "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400";
      case "canceled":
      case "unpaid":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      default:
        return "bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Clients</h1>
          <p className="text-slate-500 dark:text-slate-400">Manage your clients and their subscriptions</p>
        </div>
        <Button>
          <PlusIcon className="h-4 w-4 mr-2" />
          Add New Client
        </Button>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-xl flex items-center">
            <UsersIcon className="h-5 w-5 mr-2" />
            Client Management
          </CardTitle>
          <CardDescription>
            View and manage all clients and their subscription status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <Input
              placeholder="Search clients by name, email, or company..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-md"
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
              <div className="flex">
                <ExclamationCircleIcon className="h-5 w-5 text-red-500 mr-2" />
                <span className="font-medium">Error:</span>
                <span className="ml-1">{error}</span>
              </div>

              {dbStatus && !dbStatus.connected && (
                <div className="mt-2 text-sm">
                  <div className="flex items-center text-red-700">
                    <ServerIcon className="h-4 w-4 mr-1" />
                    <span>Database connection failed</span>
                  </div>
                  <p className="mt-1 text-red-600">
                    Please check your database configuration and ensure the database server is running.
                  </p>
                  <div className="mt-2 p-2 bg-red-100 rounded text-xs font-mono overflow-auto">
                    {dbStatus.error}
                  </div>
                </div>
              )}

              <div className="mt-3 flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsRetrying(true)}
                  disabled={isRetrying}
                  className="flex items-center"
                >
                  {isRetrying ? (
                    <>
                      <div className="animate-spin h-4 w-4 mr-1 border-b-2 border-primary rounded-full"></div>
                      Retrying...
                    </>
                  ) : (
                    <>
                      <ArrowPathIcon className="h-4 w-4 mr-1" />
                      Retry
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.location.reload()}
                >
                  Reload Page
                </Button>
              </div>
            </div>
          )}

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Company</TableHead>
                <TableHead>Subscription Status</TableHead>
                <TableHead>Renewal Date</TableHead>
                <TableHead>Devices</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                      <span className="ml-2">Loading clients...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredClients.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-slate-500">
                    {searchTerm ? "No clients found matching your search criteria" : "No clients found in the database"}
                  </TableCell>
                </TableRow>
              ) : (
                filteredClients.map((client) => (
                  <TableRow key={client.id}>
                    <TableCell className="font-medium">{client.name}</TableCell>
                    <TableCell>{client.email}</TableCell>
                    <TableCell>{client.company}</TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeClass(client.subscription.status)}`}
                      >
                        {client.subscription.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      {client.subscription.status !== "canceled"
                        ? new Date(client.subscription.currentPeriodEnd).toLocaleDateString()
                        : "—"
                      }
                    </TableCell>
                    <TableCell>{client.devices.length}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewClient(client)}
                          disabled={loading}
                        >
                          View
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled={loading}
                          onClick={() => {
                            handleViewClient(client);
                            // Focus on the Manage Devices button after dialog opens
                            setTimeout(() => {
                              const manageDevicesBtn = document.querySelector('[data-manage-devices]') as HTMLButtonElement;
                              if (manageDevicesBtn) manageDevicesBtn.focus();
                            }, 100);
                          }}
                        >
                          Manage
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Client Details Dialog */}
      <Dialog open={isClientDetailsOpen} onOpenChange={setIsClientDetailsOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader className="pb-4 border-b">
            <DialogTitle className="text-xl font-semibold">
              Client Details
            </DialogTitle>
          </DialogHeader>
          {selectedClient && (
            <div className="space-y-8 pt-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-slate-500 uppercase tracking-wider">Client Information</h3>
                  <div className="bg-slate-50 dark:bg-slate-900/50 p-4 rounded-lg space-y-3">
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">Full Name</span>
                      <span className="font-medium">{selectedClient.name}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">Email Address</span>
                      <span className="font-medium">{selectedClient.email}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">Company</span>
                      <span className="font-medium">{selectedClient.company}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">Phone Number</span>
                      <span className="font-medium">{selectedClient.phone}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">Client Since</span>
                      <span className="font-medium">{new Date(selectedClient.createdAt).toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' })}</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-slate-500 uppercase tracking-wider">Subscription Details</h3>
                  <div className="bg-slate-50 dark:bg-slate-900/50 p-4 rounded-lg space-y-3">
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">Status</span>
                      <span
                        className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium w-fit ${getStatusBadgeClass(selectedClient.subscription.status)}`}
                      >
                        {selectedClient.subscription.status.charAt(0).toUpperCase() + selectedClient.subscription.status.slice(1)}
                      </span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">Plan</span>
                      <span className="font-medium">{selectedClient.subscription.plan}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">Monthly Price</span>
                      <span className="font-medium">{selectedClient.subscription.price}</span>
                    </div>
                    {selectedClient.subscription.status !== "canceled" && (
                      <div className="flex flex-col space-y-1">
                        <span className="text-xs text-slate-500">Next Billing Date</span>
                        <span className="font-medium">{new Date(selectedClient.subscription.currentPeriodEnd).toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' })}</span>
                      </div>
                    )}
                    <div className="flex flex-col space-y-1">
                      <span className="text-xs text-slate-500">Subscription ID</span>
                      <span className="font-mono text-xs">{selectedClient.subscription.id}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium text-slate-500 uppercase tracking-wider">Devices ({selectedClient.devices.length})</h3>
                {selectedClient.devices.length > 0 ? (
                  <div className="border rounded-lg overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Device Name</TableHead>
                          <TableHead>Model</TableHead>
                          <TableHead>Device ID</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {selectedClient.devices.map((device: any) => (
                          <TableRow key={device.id}>
                            <TableCell className="font-medium">{device.name}</TableCell>
                            <TableCell>{device.deviceModel}</TableCell>
                            <TableCell className="font-mono text-xs">{device.deviceId}</TableCell>
                            <TableCell>
                              <span
                                className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                                  device.status === "online"
                                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                    : device.status === "offline"
                                    ? "bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300"
                                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                                }`}
                              >
                                {device.status.charAt(0).toUpperCase() + device.status.slice(1)}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center py-8 text-slate-500 bg-slate-50 dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-800">
                    <p>No devices assigned to this client</p>
                    <p className="text-sm mt-1">Assign devices from the Devices management page</p>
                  </div>
                )}
              </div>

              <DialogFooter className="flex justify-between items-center border-t pt-6">
                <div className="flex space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => {
                      // In a real app, this would navigate to an edit form
                      window.alert(`Edit client: ${selectedClient.name}`);
                    }}
                  >
                    Edit Client
                  </Button>
                  <Button
                    data-manage-devices
                    onClick={() => {
                      // Close the current dialog
                      setIsClientDetailsOpen(false);
                      // Navigate to devices page with client filter
                      window.location.href = `/admin/devices?client=${selectedClient.id}`;
                    }}
                  >
                    Manage Devices
                  </Button>
                </div>
                {selectedClient.subscription.status !== "canceled" && (
                  <Button
                    variant="destructive"
                    onClick={() => setIsConfirmRevokeOpen(true)}
                  >
                    Revoke Access
                  </Button>
                )}
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Confirm Revoke Access Dialog */}
      <Dialog open={isConfirmRevokeOpen} onOpenChange={setIsConfirmRevokeOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-600">
              <ExclamationCircleIcon className="h-5 w-5 mr-2" />
              Revoke Client Access
            </DialogTitle>
            <DialogDescription>
              This action will cancel the client's subscription and revoke access to all devices.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="bg-red-50 dark:bg-red-950/20 p-4 rounded-md text-sm text-red-800 dark:text-red-300 space-y-2">
              <p>The following actions will occur:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Client subscription will be canceled immediately</li>
                <li>All device connections will be terminated</li>
                <li>Client will lose access to the dashboard</li>
              </ul>
              <p className="font-medium">This action cannot be undone.</p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsConfirmRevokeOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleRevokeAccess}>
              Confirm Revocation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
