"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RustDeskUnattendedAccess } from "@/components/dashboard/rustdesk-unattended-access";
import { getRustDeskConfig } from "@/lib/rustdesk-config";

export default function UnattendedAccessPage() {
  const [isRustDeskReady, setIsRustDeskReady] = useState(false);

  // Check if RustDesk is configured
  useEffect(() => {
    // Check if RustDesk is configured
    const config = getRustDeskConfig();
    const isConfigured = config.isConfigured();
    setIsRustDeskReady(isConfigured);

    // Log configuration status for debugging
    console.log("RustDesk configured:", isConfigured);
    console.log("RustDesk config:", {
      serverAddress: config.serverAddress,
      apiAddress: config.apiAddress,
      isUsingCustomServer: config.isUsingCustomServer,
      androidDeviceId: config.androidDeviceId ? config.androidDeviceId.substring(0, 4) + '...' : 'not set',
    });
  }, []);

  if (!isRustDeskReady) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Unattended Access</h1>
          <p className="text-slate-500 dark:text-slate-400">Connect to devices without requiring physical acceptance</p>
        </div>

        <Card className="p-8 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="h-16 w-16 text-slate-400">
              <path fillRule="evenodd" d="M2.25 5.25a3 3 0 013-3h13.5a3 3 0 013 3V15a3 3 0 01-3 3h-3v.257c0 .597.237 1.17.659 1.591l.621.622a.75.75 0 01-.53 1.28h-9a.75.75 0 01-.53-1.28l.621-.622a2.25 2.25 0 00.659-1.59V18h-3a3 3 0 01-3-3V5.25zm1.5 0v9.75c0 .83.67 1.5 1.5 1.5h13.5c.83 0 1.5-.67 1.5-1.5V5.25c0-.83-.67-1.5-1.5-1.5H5.25c-.83 0-1.5.67-1.5 1.5z" clipRule="evenodd" />
            </svg>
            <h2 className="text-2xl font-bold">RustDesk Not Configured</h2>
            <p className="text-slate-500 dark:text-slate-400 max-w-md mx-auto">
              RustDesk configuration is not complete. Please set up the RustDesk integration to use this feature.
            </p>
            <Button
              onClick={() => window.location.href = '/admin/integrations'}
              className="mt-4"
            >
              Configure RustDesk
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Unattended Access</h1>
          <p className="text-slate-500 dark:text-slate-400">
            Connect to devices without requiring physical acceptance
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => {
            window.location.href = '/admin/devices';
          }}
          className="flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4 mr-2">
            <path fillRule="evenodd" d="M15.75 1.5a6.75 6.75 0 00-6.651 7.906c.067.39-.032.717-.221.906l-6.5 6.499a3 3 0 00-.878 2.121v2.818c0 .414.336.75.75.75H6a.75.75 0 00.75-.75v-1.5h1.5A.75.75 0 009 19.5V18h1.5a.75.75 0 00.75-.75V15h1.5a.75.75 0 00.53-.22l.5-.5a.75.75 0 00.22-.53V12h.75a.75.75 0 00.53-.22l.5-.5a.75.75 0 00.22-.53V9.75A6.75 6.75 0 0015.75 1.5zm0 3a.75.75 0 000 1.5A2.25 2.25 0 0118 8.25a.75.75 0 001.5 0 3.75 3.75 0 00-3.75-3.75z" clipRule="evenodd" />
          </svg>
          Manage Devices
        </Button>
      </div>

      <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-md">
        <p className="text-sm text-blue-700 dark:text-blue-400 mb-1">
          <span className="font-medium">About Unattended Access:</span> This feature allows you to connect to devices without someone physically accepting the connection.
        </p>
        <p className="text-sm text-blue-700 dark:text-blue-400 mb-1">
          To set up unattended access for a device:
        </p>
        <ol className="list-decimal list-inside text-sm text-blue-700 dark:text-blue-400 ml-2 mb-1">
          <li>Install RustDesk on the device</li>
          <li>Configure the device with a permanent password</li>
          <li>Note the device ID displayed in RustDesk</li>
          <li>Add the device to the Spear platform with its ID and password</li>
        </ol>
        <p className="text-sm text-blue-700 dark:text-blue-400">
          Once set up, you can connect to the device using the form below.
        </p>
      </div>

      <RustDeskUnattendedAccess />
    </div>
  );
}
