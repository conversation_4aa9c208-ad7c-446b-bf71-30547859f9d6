"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function BypassLogin() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">Spear Direct Access</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-center text-slate-500 dark:text-slate-400">
              Choose an option to bypass the login system:
            </p>

            <div className="space-y-2">
              <Link href="/direct-admin" className="w-full">
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  Admin Dashboard
                </Button>
              </Link>

              <Link href="/direct-remote" className="w-full">
                <Button className="w-full bg-green-600 hover:bg-green-700">
                  Remote Control
                </Button>
              </Link>

              <Link href="/web-remote" className="w-full">
                <Button className="w-full bg-indigo-600 hover:bg-indigo-700">
                  Web Remote Control
                </Button>
              </Link>

              <Link href="/auto-remote" className="w-full">
                <Button className="w-full bg-emerald-600 hover:bg-emerald-700">
                  Auto Remote Control
                </Button>
              </Link>

              <Link href="/auto-click" className="w-full">
                <Button className="w-full bg-green-600 hover:bg-green-700">
                  Auto Click (Recommended)
                </Button>
              </Link>

              <Link href="/auto-click.html" className="w-full">
                <Button className="w-full bg-emerald-600 hover:bg-emerald-700">
                  Auto Click (HTML)
                </Button>
              </Link>

              <Link href="/connect-window" className="w-full">
                <Button className="w-full bg-teal-600 hover:bg-teal-700">
                  Connect Window (React)
                </Button>
              </Link>

              <Link href="/connect-window.html" className="w-full">
                <Button className="w-full bg-cyan-600 hover:bg-cyan-700">
                  Connect Window (HTML)
                </Button>
              </Link>

              <Link href="/direct-connect" className="w-full">
                <Button className="w-full bg-amber-600 hover:bg-amber-700">
                  Direct Connect (React)
                </Button>
              </Link>

              <Link href="/direct-connect.html" className="w-full">
                <Button className="w-full bg-yellow-600 hover:bg-yellow-700">
                  Direct Connect (HTML)
                </Button>
              </Link>

              <Link href="/spear-remote.html" className="w-full">
                <Button className="w-full bg-purple-600 hover:bg-purple-700">
                  Simple Remote Control
                </Button>
              </Link>

              <Link href="/spear-auto-connect.html" className="w-full">
                <Button className="w-full bg-pink-600 hover:bg-pink-700">
                  Simple Auto Connect
                </Button>
              </Link>
            </div>

            <p className="text-xs text-center text-slate-400 dark:text-slate-500 mt-6">
              These links bypass the authentication system for testing purposes.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
