"use client";

import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MetaTags } from "@/components/seo/meta-tags";
import { OrganizationLD, WebsiteLD } from "@/components/seo/json-ld";
import {
  DevicePhoneMobileIcon,
  ShieldCheckIcon,
  MapPinIcon,
  ChartBarIcon,
  ServerIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";

export default function Home() {
  return (
    <>
      <MetaTags
        title="SPEAR - Secure Platform for Extended Augmented Reality"
        description="SPEAR provides enterprise-grade remote device management with security, location verification, and compliance solutions for various industries."
        keywords="remote device management, TeamViewer integration, security, compliance, location verification"
        ogType="website"
        ogImage="/images/spear-logo.PNG"
      />
      <OrganizationLD />
      <WebsiteLD />

      <div className="flex flex-col min-h-screen">
        {/* Navigation Header */}
        <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <Link href="/" className="flex items-center space-x-2">
                <Image
                  src="/images/spear-logo.PNG"
                  alt="SPEAR Logo"
                  width={32}
                  height={32}
                  className="object-contain"
                />
                <span className="text-xl font-bold">SPEAR</span>
              </Link>
              <nav className="hidden md:flex items-center space-x-6">
                <Link href="/pricing" className="text-sm font-medium hover:text-primary">
                  Pricing
                </Link>
                <Link href="/faq" className="text-sm font-medium hover:text-primary">
                  FAQ
                </Link>
                <Link href="/blog" className="text-sm font-medium hover:text-primary">
                  Blog
                </Link>
                <Link href="/contact" className="text-sm font-medium hover:text-primary">
                  Contact
                </Link>
              </nav>
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/login">Sign In</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/pricing">Get Started</Link>
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative py-20 md:py-32 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary-foreground/5 z-0"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  Secure Remote Device Management
                </h1>
                <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
                  SPEAR provides enterprise-grade remote access solutions with military-level security,
                  real-time monitoring, and seamless device management for businesses worldwide.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button size="lg" asChild>
                    <Link href="/pricing">View Pricing</Link>
                  </Button>
                  <Button size="lg" variant="outline" asChild>
                    <Link href="/register">Create Account</Link>
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-4">
                  Create account → Choose plan → Get your devices • Cancel anytime
                </p>
              </div>
              <div className="relative h-[400px] rounded-lg overflow-hidden shadow-xl bg-gradient-to-r from-slate-900 via-purple-950 to-slate-900 flex items-center justify-center">
                <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-30"></div>
                <Image
                  src="/images/spear-logo.PNG"
                  alt="SPEAR Logo"
                  width={300}
                  height={300}
                  className="object-contain z-10"
                  priority
                />
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Comprehensive Remote Management
              </h2>
              <p className="text-xl text-muted-foreground">
                SPEAR combines powerful features to provide a complete remote device management solution
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div className="bg-card rounded-lg p-6 border shadow-sm">
                <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                  <DevicePhoneMobileIcon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Remote Access</h3>
                <p className="text-muted-foreground">
                  Securely access and control remote devices from anywhere with enterprise-grade RustDesk integration.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 border shadow-sm">
                <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                  <ShieldCheckIcon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Enterprise Security</h3>
                <p className="text-muted-foreground">
                  End-to-end encryption, multi-factor authentication, and detailed audit logs.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 border shadow-sm">
                <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                  <MapPinIcon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Location Verification</h3>
                <p className="text-muted-foreground">
                  Verify device locations for compliance and security requirements.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 border shadow-sm">
                <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                  <ChartBarIcon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Advanced Analytics</h3>
                <p className="text-muted-foreground">
                  Comprehensive reporting and analytics for device usage and performance.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 border shadow-sm">
                <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                  <ServerIcon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Device Provisioning</h3>
                <p className="text-muted-foreground">
                  Pre-configured Samsung Galaxy A14 devices shipped directly to your location, ready for immediate use.
                </p>
              </div>

              <div className="bg-card rounded-lg p-6 border shadow-sm">
                <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                  <UserGroupIcon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">24/7 Support</h3>
                <p className="text-muted-foreground">
                  Dedicated customer support with comprehensive device management and troubleshooting assistance.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Trusted by Businesses Worldwide
              </h2>
              <p className="text-xl text-muted-foreground">
                See what our customers say about SPEAR's remote device management solutions
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-card rounded-lg p-6 border shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400">
                    {"★".repeat(5)}
                  </div>
                </div>
                <p className="text-muted-foreground mb-4">
                  "SPEAR has revolutionized how we manage our remote locations. The security
                  features and ease of use are exactly what we needed for our business."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mr-3">
                    <span className="text-primary font-bold">JD</span>
                  </div>
                  <div>
                    <p className="font-medium">John Davis</p>
                    <p className="text-sm text-muted-foreground">IT Director, TechCorp</p>
                  </div>
                </div>
              </div>

              <div className="bg-card rounded-lg p-6 border shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400">
                    {"★".repeat(5)}
                  </div>
                </div>
                <p className="text-muted-foreground mb-4">
                  "The device provisioning service is incredible. We received pre-configured
                  devices that worked perfectly right out of the box. Saved us weeks of setup time."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mr-3">
                    <span className="text-primary font-bold">SM</span>
                  </div>
                  <div>
                    <p className="font-medium">Sarah Martinez</p>
                    <p className="text-sm text-muted-foreground">Operations Manager, RetailPlus</p>
                  </div>
                </div>
              </div>

              <div className="bg-card rounded-lg p-6 border shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="flex text-yellow-400">
                    {"★".repeat(5)}
                  </div>
                </div>
                <p className="text-muted-foreground mb-4">
                  "Outstanding customer support and reliable service. SPEAR's team helped us
                  implement a complete remote management solution in just days."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mr-3">
                    <span className="text-primary font-bold">MR</span>
                  </div>
                  <div>
                    <p className="font-medium">Michael Rodriguez</p>
                    <p className="text-sm text-muted-foreground">CTO, StartupHub</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold text-primary mb-2">99.9%</div>
                <p className="text-muted-foreground">Uptime Guarantee</p>
              </div>
              <div>
                <div className="text-4xl font-bold text-primary mb-2">24/7</div>
                <p className="text-muted-foreground">Customer Support</p>
              </div>
              <div>
                <div className="text-4xl font-bold text-primary mb-2">256-bit</div>
                <p className="text-muted-foreground">Encryption</p>
              </div>
              <div>
                <div className="text-4xl font-bold text-primary mb-2">24/7</div>
                <p className="text-muted-foreground">Support</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Ready to Experience Secure Remote Access?
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                Join businesses worldwide who trust SPEAR for secure, reliable remote device management.
                Create account → Choose plan → Get your devices shipped.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button size="lg" asChild>
                  <Link href="/pricing">View Pricing</Link>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <Link href="/register">Create Account</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-muted py-12 mt-auto">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-6 md:mb-0">
                <div className="flex items-center">
                  <Image
                    src="/images/spear-logo.PNG"
                    alt="SPEAR Logo"
                    width={40}
                    height={40}
                    className="mr-2"
                    onError={(e) => {
                      // Fallback if image doesn't exist
                      const target = e.target as HTMLImageElement;
                      target.src = "https://placehold.co/40/slate/white?text=SPEAR";
                    }}
                  />
                  <span className="text-xl font-bold">SPEAR</span>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  Secure Platform for Extended Augmented Reality
                </p>
              </div>
              <div className="flex flex-wrap gap-6">
                <Link href="/pricing" className="text-sm hover:text-primary">Pricing</Link>
                <Link href="/about" className="text-sm hover:text-primary">About</Link>
                <Link href="/blog" className="text-sm hover:text-primary">Blog</Link>
                <Link href="/faq" className="text-sm hover:text-primary">FAQ</Link>
                <Link href="/contact" className="text-sm hover:text-primary">Contact</Link>
                <Link href="/privacy" className="text-sm hover:text-primary">Privacy</Link>
                <Link href="/terms" className="text-sm hover:text-primary">Terms</Link>
              </div>
            </div>
            <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
              &copy; {new Date().getFullYear()} SPEAR Platform. All rights reserved.
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
