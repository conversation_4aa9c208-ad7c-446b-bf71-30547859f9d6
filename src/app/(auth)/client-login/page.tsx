"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { VRTitleSection } from "@/components/dashboard/vr-title-section";
import { motion } from "framer-motion";
import { QuestionMarkCircleIcon } from "@heroicons/react/24/outline";
import { signIn } from "next-auth/react";
import { getCsrfToken } from "next-auth/react";

export default function ClientLoginPage() {
  const router = useRouter();
  const [formState, setFormState] = useState({
    email: "<EMAIL>",
    password: "password"
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [csrfToken, setCsrfToken] = useState<string | null>(null);
  
  // Fetch CSRF token on component mount
  useEffect(() => {
    const fetchCsrfToken = async () => {
      try {
        const token = await getCsrfToken();
        console.log("CSRF Token fetched:", token);
        setCsrfToken(token);
      } catch (err) {
        console.error("Error fetching CSRF token:", err);
      }
    };
    
    fetchCsrfToken();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormState((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Check if CSRF token is available
    if (!csrfToken) {
      console.error("CSRF token not available");
      setError("Authentication error: CSRF token not available. Please refresh the page and try again.");
      setIsLoading(false);
      return;
    }

    const { email, password } = formState;
    console.log("Submitting with email:", email, "and password:", password, "and CSRF token:", csrfToken);

    try {
      // Determine the callback URL based on the email
      const callbackUrl = "/dashboard";

      // Use NextAuth signIn function with redirect: false first to check for errors
      const result = await signIn("credentials", {
        email,
        password,
        callbackUrl,
        csrfToken,
        redirect: false
      });
      
      // Check for errors before redirecting
      if (result?.error) {
        console.error("SignIn error:", result.error);
        setError("Invalid email or password");
        setIsLoading(false);
        return;
      }
      
      // If no errors, redirect manually
      window.location.href = callbackUrl;
      
      // Log the result for debugging
      console.log("SignIn result:", result);
    } catch (err) {
      console.error("Error during sign in:", err);
      setError("An error occurred during sign in. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 flex flex-col justify-center items-center p-4 md:p-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card className="border-slate-200 dark:border-slate-800 shadow-xl">
            <CardHeader className="space-y-1 text-center">
              <div className="flex justify-center mb-4">
                <Image
                  src="/images/spear-logo.PNG"
                  alt="Spear Logo"
                  width={120}
                  height={120}
                  className="rounded-lg"
                />
              </div>
              <CardTitle className="text-2xl font-bold">Client Login</CardTitle>
              <CardDescription>
                Enter your credentials to access your devices
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {error && (
                <div className="p-3 text-sm text-white bg-red-500 rounded">
                  {error}
                </div>
              )}
              {/* Hidden CSRF Token Input */}
              <input name="csrfToken" type="hidden" defaultValue={csrfToken || ""} />
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formState.email}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password">Password</Label>
                    <Link
                      href="/forgot-password"
                      className="text-sm text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      Forgot password?
                    </Link>
                  </div>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    placeholder="••••••••"
                    value={formState.password}
                    onChange={handleChange}
                    required
                  />
                </div>
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? "Signing in..." : "Sign In"}
                </Button>
              </form>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <div className="text-sm text-center text-slate-500 dark:text-slate-400">
                Don&apos;t have an account?{" "}
                <Link
                  href="/register"
                  className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Sign up
                </Link>
              </div>
              <div className="text-xs text-center text-slate-500 dark:text-slate-400">
                By signing in, you agree to our{" "}
                <Link
                  href="/terms"
                  className="underline hover:text-slate-800 dark:hover:text-slate-200"
                >
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link
                  href="/privacy"
                  className="underline hover:text-slate-800 dark:hover:text-slate-200"
                >
                  Privacy Policy
                </Link>
              </div>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
