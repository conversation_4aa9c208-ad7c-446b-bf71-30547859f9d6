"use client";

import { useState, useRef, useEffect, Suspense } from "react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";

function DirectConnectContent() {
  // Router and search params
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get device ID and password from URL params or use defaults
  const deviceId = searchParams.get('id') || '1681512408';
  const password = searchParams.get('password') || '82AirmaN@$';

  // State
  const [isLoading, setIsLoading] = useState(true);
  const [statusMessage, setStatusMessage] = useState('Connecting to your device...');
  const [connectionAttempt, setConnectionAttempt] = useState(0);

  // Refs
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Configuration
  const config = {
    rustDeskUrl: 'https://rustdesk.com/web/',
    deviceIconSelector: '.device-icon, .device-card, [class*="device-card"], [class*="device-icon"]',
    maxAttempts: 20,
    attemptInterval: 500, // ms
    hideElements: [
      '.header-bar',
      '.sidebar',
      '.rustdesk-header',
      '[class*="header-bar"]',
      '[class*="sidebar"]',
      '[class*="rustdesk-header"]'
    ]
  };

  // Build the connection URL
  const connectionUrl = `${config.rustDeskUrl}?id=${deviceId}${password ? `&password=${encodeURIComponent(password)}` : ''}`;

  // End session handler
  const handleEndSession = () => {
    if (window.confirm('Are you sure you want to end this remote control session?')) {
      router.push('/bypass');
    }
  };

  // Attempt to click the device icon
  const attemptClickDeviceIcon = (attempt = 0) => {
    if (attempt >= config.maxAttempts) {
      setStatusMessage('Could not automatically connect. Please click on your device manually.');
      setIsLoading(false);
      return;
    }

    try {
      // Try to access iframe content (will likely fail due to cross-origin policy)
      if (!iframeRef.current) return;

      const frameDocument = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;

      if (!frameDocument) {
        throw new Error('Cannot access iframe document');
      }

      // Find device icon
      const deviceIcon = frameDocument.querySelector(config.deviceIconSelector);

      if (deviceIcon) {
        // Click the device icon
        (deviceIcon as HTMLElement).click();

        // Hide RustDesk UI elements
        const style = document.createElement('style');
        style.textContent = config.hideElements.map(selector => `${selector} { display: none !important; }`).join('\n');
        frameDocument.head.appendChild(style);

        // Hide loading overlay after a short delay
        setTimeout(() => setIsLoading(false), 1000);

        setStatusMessage('Connected successfully!');
      } else {
        // Device icon not found yet, try again
        setConnectionAttempt(attempt + 1);
        setStatusMessage(`Waiting for device to appear... (${attempt + 1}/${config.maxAttempts})`);
        setTimeout(() => attemptClickDeviceIcon(attempt + 1), config.attemptInterval);
      }
    } catch (e) {
      // Cross-origin error, try again
      setConnectionAttempt(attempt + 1);
      setStatusMessage(`Waiting for connection... (${attempt + 1}/${config.maxAttempts})`);
      setTimeout(() => attemptClickDeviceIcon(attempt + 1), config.attemptInterval);
    }
  };

  // Handle iframe load
  const handleIframeLoad = () => {
    setStatusMessage('RustDesk loaded. Attempting to connect...');
    attemptClickDeviceIcon();
  };

  return (
    <div className="flex flex-col h-screen w-full bg-slate-900">
      <div className="bg-slate-800 p-3 flex items-center justify-between shadow-md">
        <div className="flex items-center gap-3">
          <Image
            src="/images/spear-logo.PNG"
            alt="Spear Logo"
            width={40}
            height={20}
            className="h-10 w-auto"
          />
          <h1 className="text-lg font-semibold text-white">Remote Control</h1>
        </div>
        <Button
          variant="destructive"
          onClick={handleEndSession}
        >
          End Session
        </Button>
      </div>

      <div className="relative flex-1">
        <iframe
          ref={iframeRef}
          src={connectionUrl}
          className="w-full h-full border-0"
          onLoad={handleIframeLoad}
        />

        {isLoading && (
          <div className="absolute inset-0 bg-slate-900/90 flex flex-col items-center justify-center">
            <div className="w-12 h-12 border-4 border-t-transparent border-indigo-600 rounded-full animate-spin mb-4"></div>
            <p className="text-lg font-medium text-white">{statusMessage}</p>
            {connectionAttempt > 0 && (
              <p className="text-sm text-slate-400 mt-2">
                Attempt {connectionAttempt} of {config.maxAttempts}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// Wrap the component in a Suspense boundary
export default function DirectConnectPage() {
  return (
    <Suspense fallback={
      <div className="flex flex-col h-screen w-full bg-slate-900">
        <div className="bg-slate-800 p-3 flex items-center justify-between shadow-md">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 bg-slate-700 animate-pulse rounded"></div>
            <div className="h-6 w-32 bg-slate-700 animate-pulse rounded"></div>
          </div>
          <div className="h-9 w-28 bg-slate-700 animate-pulse rounded"></div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="w-12 h-12 border-4 border-t-transparent border-indigo-600 rounded-full animate-spin"></div>
        </div>
      </div>
    }>
      <DirectConnectContent />
    </Suspense>
  );
}
