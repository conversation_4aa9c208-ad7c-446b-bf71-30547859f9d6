"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function RustDeskRedirect() {
  const router = useRouter();

  useEffect(() => {
    router.replace("/remote-connect");
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-slate-500 dark:text-slate-400">Redirecting to Remote Control...</p>
      </div>
    </div>
  );
}