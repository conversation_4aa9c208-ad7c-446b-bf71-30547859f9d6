"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";

export default function WebRemoteConnect() {
  const [deviceId, setDeviceId] = useState("");
  const [password, setPassword] = useState("");
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionTime, setConnectionTime] = useState<Date | null>(null);
  const [connectionType, setConnectionType] = useState<string | null>(null);

  const handleDesktopConnect = () => {
    setIsConnecting(true);
    setConnectionType("desktop");

    // Format the device ID by removing spaces if present
    const formattedDeviceId = deviceId.replace(/\s+/g, '');

    // Build the connection URL (using RustDesk protocol behind the scenes)
    let connectionUrl = `rustdesk://${formattedDeviceId}`;

    // If we have a password, add it to the URL
    if (password) {
      connectionUrl += `?password=${encodeURIComponent(password)}`;
    }

    // Show guidance message
    alert(`Connecting to device ${deviceId}. The remote control client will launch automatically. Please make sure the remote device is online and ready to accept connections.`);

    // Launch the remote control client
    window.location.href = connectionUrl;

    // Simulate the connection process
    setTimeout(() => {
      setIsConnecting(false);
      setIsConnected(true);
      setConnectionTime(new Date());
    }, 1000);
  };

  const handleWebConnect = () => {
    setIsConnecting(true);
    setConnectionType("web");

    // Format the device ID by removing spaces if present
    const formattedDeviceId = deviceId.replace(/\s+/g, '');

    // Build the web client URL - use the official RustDesk web client
    let webClientUrl = `https://rustdesk.com/web/`;

    // Note: The web client will ask for device ID and password in its interface
    // We'll provide instructions to the user

    // Show guidance message
    alert(`Opening RustDesk Web Client. In the new tab:\n1. Enter Device ID: ${formattedDeviceId}\n2. Enter Password: ${password}\n3. Click Connect`);

    // Open the web client in a new tab
    window.open(webClientUrl, '_blank');

    // Simulate the connection process
    setTimeout(() => {
      setIsConnecting(false);
      setIsConnected(true);
      setConnectionTime(new Date());
    }, 1000);
  };

  const handleDisconnect = () => {
    setIsConnected(false);
    setConnectionTime(null);
    setConnectionType(null);
  };

  // Format the connection time for display
  const formattedConnectionTime = connectionTime
    ? connectionTime.toLocaleTimeString()
    : null;

  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-100 dark:bg-slate-900 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Image
              src="/images/spear-logo.PNG"
              alt="Spear Logo"
              width={120}
              height={60}
              className="h-auto"
            />
          </div>
          <CardTitle>Remote Control</CardTitle>
        </CardHeader>
        <CardContent>
          {!isConnected ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="deviceId">Device ID</Label>
                <Input
                  id="deviceId"
                  placeholder="Enter device ID"
                  value={deviceId}
                  onChange={(e) => setDeviceId(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Access Code</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter access code"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Button
                  onClick={handleDesktopConnect}
                  disabled={isConnecting || !deviceId.trim()}
                  className="w-full"
                >
                  {isConnecting && connectionType === "desktop" ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Connecting...
                    </>
                  ) : (
                    "Desktop Client"
                  )}
                </Button>

                <Button
                  onClick={handleWebConnect}
                  disabled={isConnecting || !deviceId.trim()}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  {isConnecting && connectionType === "web" ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Connecting...
                    </>
                  ) : (
                    "Web Client"
                  )}
                </Button>
              </div>

              <p className="text-sm text-slate-500 dark:text-slate-400 text-center">
                Desktop client requires installation. Web client works directly in your browser.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-full mx-auto w-16 h-16 flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-8 h-8 text-green-600 dark:text-green-400">
                  <path fillRule="evenodd" d="M2.25 5.25a3 3 0 013-3h13.5a3 3 0 013 3V15a3 3 0 01-3 3h-3v.257c0 .597.237 1.17.659 1.591l.621.622a.75.75 0 01-.53 1.28h-9a.75.75 0 01-.53-1.28l.621-.622a2.25 2.25 0 00.659-1.59V18h-3a3 3 0 01-3-3V5.25zm1.5 0v9.75c0 .83.67 1.5 1.5 1.5h13.5c.83 0 1.5-.67 1.5-1.5V5.25c0-.83-.67-1.5-1.5-1.5H5.25c-.83 0-1.5.67-1.5 1.5z" clipRule="evenodd" />
                </svg>
              </div>

              <h3 className="text-xl font-medium text-center mb-3">Connected to Device</h3>
              <p className="text-slate-500 dark:text-slate-400 text-center mb-2">
                Connection initiated at {formattedConnectionTime}
              </p>
              <p className="text-slate-500 dark:text-slate-400 text-center mb-6">
                {connectionType === "desktop"
                  ? "The desktop client has been launched to connect to your device."
                  : "The web client has been launched in a new tab to connect to your device."}
              </p>

              <div className="mt-6">
                <Button
                  onClick={handleDisconnect}
                  variant="outline"
                  className="w-full text-red-500 border-red-500 hover:bg-red-50 dark:hover:bg-red-950"
                >
                  End Session
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
