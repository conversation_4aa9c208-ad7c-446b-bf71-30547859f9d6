import { prisma } from "@/lib/db";

/**
 * Get all devices with their assigned user information
 * @returns Array of devices with their assigned user information
 */
export async function getAllDevices() {
  try {
    const devices = await prisma.device.findMany({
      include: {
        assignedTo: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Format the devices data
    return devices.map((device) => ({
      id: device.id,
      name: device.name,
      deviceModel: device.model || "Unknown Model",
      deviceId: device.rustDeskId,
      assignedTo: device.assignedTo
        ? {
            id: device.assignedTo.id,
            name: device.assignedTo.name || "Unnamed Client",
            email: device.assignedTo.email,
          }
        : null,
      status: device.status,
      lastActive: device.updatedAt.toISOString(),
    }));
  } catch (error) {
    console.error("Error getting all devices:", error);
    throw error;
  }
}

/**
 * Get a device by ID with its assigned user information
 * @param deviceId The ID of the device to get
 * @returns Device with its assigned user information
 */
export async function getDeviceById(deviceId: string) {
  try {
    const device = await prisma.device.findUnique({
      where: {
        id: deviceId,
      },
      include: {
        assignedTo: true,
      },
    });

    if (!device) {
      throw new Error(`Device with ID ${deviceId} not found`);
    }

    // Format the device data
    return {
      id: device.id,
      name: device.name,
      deviceModel: device.model || "Unknown Model",
      deviceId: device.rustDeskId,
      assignedTo: device.assignedTo
        ? {
            id: device.assignedTo.id,
            name: device.assignedTo.name || "Unnamed Client",
            email: device.assignedTo.email,
          }
        : null,
      status: device.status,
      lastActive: device.updatedAt.toISOString(),
    };
  } catch (error) {
    console.error(`Error getting device with ID ${deviceId}:`, error);
    throw error;
  }
}

/**
 * Get all devices assigned to a specific client
 * @param clientId The ID of the client
 * @returns Array of devices assigned to the client
 */
export async function getDevicesByClientId(clientId: string) {
  try {
    const devices = await prisma.device.findMany({
      where: {
        userId: clientId,
      },
      include: {
        assignedTo: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Format the devices data
    return devices.map((device) => ({
      id: device.id,
      name: device.name,
      deviceModel: device.model || "Unknown Model",
      deviceId: device.rustDeskId,
      assignedTo: device.assignedTo
        ? {
            id: device.assignedTo.id,
            name: device.assignedTo.name || "Unnamed Client",
            email: device.assignedTo.email,
          }
        : null,
      status: device.status,
      lastActive: device.updatedAt.toISOString(),
    }));
  } catch (error) {
    console.error(`Error getting devices for client ${clientId}:`, error);
    throw error;
  }
}

/**
 * Assign a device to a client
 * @param deviceId The ID of the device to assign
 * @param clientId The ID of the client to assign the device to, or null to unassign
 * @returns Updated device
 */
export async function assignDeviceToClient(deviceId: string, clientId: string | null) {
  try {
    const updatedDevice = await prisma.device.update({
      where: {
        id: deviceId,
      },
      data: {
        userId: clientId,
        status: clientId ? "offline" : "unassigned",
      },
      include: {
        assignedTo: true,
      },
    });

    // Format the device data
    return {
      id: updatedDevice.id,
      name: updatedDevice.name,
      deviceModel: updatedDevice.model || "Unknown Model",
      deviceId: updatedDevice.rustDeskId,
      assignedTo: updatedDevice.assignedTo
        ? {
            id: updatedDevice.assignedTo.id,
            name: updatedDevice.assignedTo.name || "Unnamed Client",
            email: updatedDevice.assignedTo.email,
          }
        : null,
      status: updatedDevice.status,
      lastActive: updatedDevice.updatedAt.toISOString(),
    };
  } catch (error) {
    console.error(`Error assigning device ${deviceId} to client ${clientId}:`, error);
    throw error;
  }
}

/**
 * Create a new device
 * @param deviceData The device data to create
 * @returns Created device
 */
export async function createDevice(deviceData: {
  name: string;
  rustDeskId: string;
  password?: string;
  model?: string;
}) {
  try {
    const newDevice = await prisma.device.create({
      data: {
        name: deviceData.name,
        rustDeskId: deviceData.rustDeskId,
        password: deviceData.password,
        model: deviceData.model,
        status: "unassigned",
      },
    });

    return {
      id: newDevice.id,
      name: newDevice.name,
      deviceModel: newDevice.model || "Unknown Model",
      deviceId: newDevice.rustDeskId,
      assignedTo: null,
      status: newDevice.status,
      lastActive: newDevice.updatedAt.toISOString(),
    };
  } catch (error) {
    console.error("Error creating device:", error);
    throw error;
  }
}
