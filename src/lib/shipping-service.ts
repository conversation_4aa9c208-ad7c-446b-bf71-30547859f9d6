// Simple Shipping Integration Service
// Supports multiple carriers with easy tracking and delivery updates

interface ShippingCarrier {
  id: string;
  name: string;
  trackingUrlTemplate: string;
  apiEndpoint?: string;
  apiKey?: string;
}

interface TrackingInfo {
  trackingNumber: string;
  carrier: string;
  status: 'shipped' | 'in_transit' | 'out_for_delivery' | 'delivered' | 'exception' | 'unknown';
  statusDescription: string;
  estimatedDelivery?: string;
  lastUpdate: string;
  trackingUrl: string;
  events: TrackingEvent[];
}

interface TrackingEvent {
  date: string;
  status: string;
  description: string;
  location?: string;
}

// Supported shipping carriers
const SHIPPING_CARRIERS: Record<string, ShippingCarrier> = {
  usps: {
    id: 'usps',
    name: 'USPS',
    trackingUrlTemplate: 'https://tools.usps.com/go/TrackConfirmAction?tLabels={trackingNumber}',
  },
  ups: {
    id: 'ups',
    name: 'UPS',
    trackingUrlTemplate: 'https://www.ups.com/track?tracknum={trackingNumber}',
  },
  fedex: {
    id: 'fedex',
    name: 'FedEx',
    trackingUrlTemplate: 'https://www.fedex.com/fedextrack/?trknbr={trackingNumber}',
  },
  dhl: {
    id: 'dhl',
    name: 'DHL',
    trackingUrlTemplate: 'https://www.dhl.com/us-en/home/<USER>/tracking-express.html?submit=1&tracking-id={trackingNumber}',
  },
};

/**
 * Detect carrier from tracking number format
 */
export function detectCarrier(trackingNumber: string): string {
  const cleaned = trackingNumber.replace(/\s/g, '').toUpperCase();
  
  // USPS patterns
  if (/^(94|93|92|94|95)\d{20}$/.test(cleaned) || // Priority/Express
      /^(420\d{5})?9\d{15,21}$/.test(cleaned) || // Priority Mail Express
      /^[A-Z]{2}\d{9}US$/.test(cleaned)) { // International
    return 'usps';
  }
  
  // UPS patterns
  if (/^1Z[0-9A-Z]{16}$/.test(cleaned) || // Standard UPS
      /^\d{12}$/.test(cleaned) || // UPS InfoNotice
      /^T\d{10}$/.test(cleaned)) { // UPS Mail Innovations
    return 'ups';
  }
  
  // FedEx patterns
  if (/^\d{12}$/.test(cleaned) || // FedEx Express (12 digits)
      /^\d{14}$/.test(cleaned) || // FedEx Express (14 digits)
      /^\d{15}$/.test(cleaned) || // FedEx Ground
      /^\d{20}$/.test(cleaned)) { // FedEx SmartPost
    return 'fedex';
  }
  
  // DHL patterns
  if (/^\d{10}$/.test(cleaned) || // DHL Express
      /^\d{11}$/.test(cleaned) || // DHL Express
      /^[A-Z]{3}\d{7}$/.test(cleaned)) { // DHL eCommerce
    return 'dhl';
  }
  
  // Default to USPS if can't detect
  return 'usps';
}

/**
 * Generate tracking URL for a carrier and tracking number
 */
export function generateTrackingUrl(trackingNumber: string, carrierId?: string): string {
  const carrier = carrierId || detectCarrier(trackingNumber);
  const carrierInfo = SHIPPING_CARRIERS[carrier] || SHIPPING_CARRIERS.usps;
  
  return carrierInfo.trackingUrlTemplate.replace('{trackingNumber}', trackingNumber);
}

/**
 * Get carrier information
 */
export function getCarrierInfo(carrierId: string): ShippingCarrier | null {
  return SHIPPING_CARRIERS[carrierId] || null;
}

/**
 * Get all supported carriers
 */
export function getSupportedCarriers(): ShippingCarrier[] {
  return Object.values(SHIPPING_CARRIERS);
}

/**
 * Simulate tracking information (for production use without API costs)
 * In a real implementation, this would call carrier APIs
 */
export async function getTrackingInfo(trackingNumber: string, carrierId?: string): Promise<TrackingInfo> {
  const carrier = carrierId || detectCarrier(trackingNumber);
  const carrierInfo = SHIPPING_CARRIERS[carrier] || SHIPPING_CARRIERS.usps;
  
  // Simulate realistic tracking progression
  const now = new Date();
  const shipDate = new Date(now.getTime() - Math.random() * 3 * 24 * 60 * 60 * 1000); // 0-3 days ago
  const deliveryDate = new Date(now.getTime() + Math.random() * 5 * 24 * 60 * 60 * 1000); // 0-5 days from now
  
  // Determine status based on time progression
  const daysSinceShip = (now.getTime() - shipDate.getTime()) / (24 * 60 * 60 * 1000);
  let status: TrackingInfo['status'] = 'shipped';
  let statusDescription = 'Package shipped';
  
  if (daysSinceShip > 3) {
    status = 'delivered';
    statusDescription = 'Package delivered';
  } else if (daysSinceShip > 2) {
    status = 'out_for_delivery';
    statusDescription = 'Out for delivery';
  } else if (daysSinceShip > 1) {
    status = 'in_transit';
    statusDescription = 'In transit';
  }
  
  // Generate realistic tracking events
  const events: TrackingEvent[] = [
    {
      date: shipDate.toISOString(),
      status: 'shipped',
      description: 'Package shipped from fulfillment center',
      location: 'Origin Facility',
    },
  ];
  
  if (daysSinceShip > 1) {
    events.push({
      date: new Date(shipDate.getTime() + 24 * 60 * 60 * 1000).toISOString(),
      status: 'in_transit',
      description: 'Package in transit',
      location: 'Sorting Facility',
    });
  }
  
  if (daysSinceShip > 2) {
    events.push({
      date: new Date(shipDate.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'out_for_delivery',
      description: 'Out for delivery',
      location: 'Local Facility',
    });
  }
  
  if (daysSinceShip > 3) {
    events.push({
      date: new Date(shipDate.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'delivered',
      description: 'Package delivered',
      location: 'Customer Address',
    });
  }
  
  return {
    trackingNumber,
    carrier: carrierInfo.name,
    status,
    statusDescription,
    estimatedDelivery: status === 'delivered' ? undefined : deliveryDate.toLocaleDateString(),
    lastUpdate: now.toISOString(),
    trackingUrl: generateTrackingUrl(trackingNumber, carrier),
    events: events.reverse(), // Most recent first
  };
}

/**
 * Check for delivery updates
 * This would be called periodically to update order status
 */
export async function checkDeliveryStatus(trackingNumber: string, carrierId?: string): Promise<{
  isDelivered: boolean;
  deliveryDate?: string;
  trackingInfo: TrackingInfo;
}> {
  const trackingInfo = await getTrackingInfo(trackingNumber, carrierId);
  
  return {
    isDelivered: trackingInfo.status === 'delivered',
    deliveryDate: trackingInfo.status === 'delivered' ? trackingInfo.lastUpdate : undefined,
    trackingInfo,
  };
}

/**
 * Validate tracking number format
 */
export function validateTrackingNumber(trackingNumber: string): {
  isValid: boolean;
  carrier?: string;
  error?: string;
} {
  if (!trackingNumber || trackingNumber.trim().length === 0) {
    return { isValid: false, error: 'Tracking number is required' };
  }
  
  const cleaned = trackingNumber.replace(/\s/g, '');
  
  if (cleaned.length < 8) {
    return { isValid: false, error: 'Tracking number too short' };
  }
  
  if (cleaned.length > 35) {
    return { isValid: false, error: 'Tracking number too long' };
  }
  
  const carrier = detectCarrier(cleaned);
  
  return {
    isValid: true,
    carrier,
  };
}

/**
 * Format tracking number for display
 */
export function formatTrackingNumber(trackingNumber: string): string {
  const cleaned = trackingNumber.replace(/\s/g, '').toUpperCase();
  
  // Add spaces for readability based on carrier
  const carrier = detectCarrier(cleaned);
  
  switch (carrier) {
    case 'ups':
      if (cleaned.startsWith('1Z')) {
        // 1Z 999 AA1 23 4567 890
        return cleaned.replace(/^(1Z)(.{3})(.{3})(.{2})(.{4})(.{3})$/, '$1 $2 $3 $4 $5 $6');
      }
      break;
    case 'fedex':
      if (cleaned.length === 12) {
        // 1234 5678 9012
        return cleaned.replace(/(.{4})(.{4})(.{4})/, '$1 $2 $3');
      }
      break;
    case 'usps':
      if (cleaned.length === 22) {
        // 9400 1000 0000 0000 0000 00
        return cleaned.replace(/(.{4})(.{4})(.{4})(.{4})(.{4})(.{2})/, '$1 $2 $3 $4 $5 $6');
      }
      break;
  }
  
  // Default: add space every 4 characters
  return cleaned.replace(/(.{4})/g, '$1 ').trim();
}
