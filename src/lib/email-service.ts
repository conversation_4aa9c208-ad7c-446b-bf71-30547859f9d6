// Temporarily disable nodemailer for deployment
let nodemailer: any = null;
console.warn('Email service temporarily disabled for deployment');

// Email configuration
const EMAIL_CONFIG = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
};

const FROM_EMAIL = process.env.FROM_EMAIL || '<EMAIL>';
const FROM_NAME = process.env.FROM_NAME || 'SPEAR Team';

// Create transporter
let transporter: nodemailer.Transporter | null = null;

function getTransporter() {
  if (!transporter && nodemailer) {
    transporter = nodemailer.createTransporter(EMAIL_CONFIG);
  }
  return transporter;
}

// Email templates
export const emailTemplates = {
  paymentConfirmation: {
    subject: '🎉 Payment Confirmed - Your SPEAR Subscription is Active!',
    getHtml: (data: {
      customerName: string;
      planName: string;
      amount: number;
      orderId: string;
      dashboardUrl: string;
    }) => `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Payment Confirmed - SPEAR</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
            <h1 style="margin: 0; font-size: 28px;">🎉 Welcome to SPEAR!</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your payment has been confirmed</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #2c3e50; margin-top: 0;">Hi ${data.customerName},</h2>
            <p>Thank you for choosing SPEAR! Your payment has been successfully processed and your subscription is now active.</p>
            
            <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #2c3e50;">Order Details:</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Plan:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;">${data.planName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Amount:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;">$${data.amount.toFixed(2)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;"><strong>Order ID:</strong></td>
                  <td style="padding: 8px 0;">#${data.orderId.slice(-8)}</td>
                </tr>
              </table>
            </div>
          </div>
          
          <div style="background: #e8f5e8; border-left: 4px solid #28a745; padding: 20px; margin-bottom: 25px;">
            <h3 style="margin-top: 0; color: #155724;">What happens next?</h3>
            <ul style="margin: 0; padding-left: 20px;">
              <li>Your mobile device is being prepared with secure access credentials</li>
              <li>You'll receive shipping confirmation within 3-5 business days</li>
              <li>Once delivered, your device will be ready to use immediately</li>
              <li>Our team will connect your profile to enable remote access</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.dashboardUrl}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">View Your Subscription Status</a>
          </div>
          
          <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #666; font-size: 14px;">
            <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p style="margin: 5px 0;">SPEAR - Secure Remote Access Solutions</p>
          </div>
        </body>
      </html>
    `,
  },

  deviceShipped: {
    subject: '🚚 Your SPEAR Device Has Been Shipped!',
    getHtml: (data: {
      customerName: string;
      orderId: string;
      trackingNumber: string;
      trackingUrl?: string;
      estimatedDelivery?: string;
      shippingAddress: any;
    }) => `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Device Shipped - SPEAR</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #ff7b7b 0%, #ff9a56 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
            <h1 style="margin: 0; font-size: 28px;">🚚 Your Device is On Its Way!</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Tracking information included</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #2c3e50; margin-top: 0;">Hi ${data.customerName},</h2>
            <p>Great news! Your SPEAR device has been shipped and is on its way to you.</p>
            
            <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #2c3e50;">Shipping Details:</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Order ID:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;">#${data.orderId.slice(-8)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Tracking Number:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee; font-family: monospace;">${data.trackingNumber}</td>
                </tr>
                ${data.estimatedDelivery ? `
                <tr>
                  <td style="padding: 8px 0;"><strong>Estimated Delivery:</strong></td>
                  <td style="padding: 8px 0;">${data.estimatedDelivery}</td>
                </tr>
                ` : ''}
              </table>
            </div>
            
            <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #2c3e50;">Shipping Address:</h3>
              <div style="color: #555;">
                ${data.shippingAddress.line1}<br>
                ${data.shippingAddress.line2 ? data.shippingAddress.line2 + '<br>' : ''}
                ${data.shippingAddress.city}, ${data.shippingAddress.state} ${data.shippingAddress.postal_code}<br>
                ${data.shippingAddress.country}
              </div>
            </div>
          </div>
          
          ${data.trackingUrl ? `
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.trackingUrl}" style="background: #ff6b35; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">Track Your Package</a>
          </div>
          ` : ''}
          
          <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 20px; margin-bottom: 25px;">
            <h3 style="margin-top: 0; color: #856404;">What to expect:</h3>
            <ul style="margin: 0; padding-left: 20px;">
              <li>Your device comes pre-configured and ready to use</li>
              <li>Setup instructions will be included in the package</li>
              <li>Our team will activate your remote access once delivered</li>
              <li>You'll receive a confirmation email when service is active</li>
            </ul>
          </div>
          
          <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #666; font-size: 14px;">
            <p>Questions about your shipment? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p style="margin: 5px 0;">SPEAR - Secure Remote Access Solutions</p>
          </div>
        </body>
      </html>
    `,
  },

  deviceDelivered: {
    subject: '📦 Your SPEAR Device Has Been Delivered!',
    getHtml: (data: {
      customerName: string;
      orderId: string;
      setupUrl: string;
    }) => `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Device Delivered - SPEAR</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
            <h1 style="margin: 0; font-size: 28px;">📦 Device Delivered!</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Ready for setup</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #2c3e50; margin-top: 0;">Hi ${data.customerName},</h2>
            <p>Your SPEAR device has been delivered! You're just one step away from having secure remote access.</p>
            
            <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #2c3e50;">Order #${data.orderId.slice(-8)} - Delivered</h3>
              <p style="margin: 0; color: #28a745; font-weight: bold;">✅ Package delivered successfully</p>
            </div>
          </div>
          
          <div style="background: #d4edda; border-left: 4px solid #28a745; padding: 20px; margin-bottom: 25px;">
            <h3 style="margin-top: 0; color: #155724;">Next Steps:</h3>
            <ol style="margin: 0; padding-left: 20px;">
              <li>Unbox your device and follow the included setup guide</li>
              <li>Connect the device to power and internet</li>
              <li>Our team will activate your remote access within 24 hours</li>
              <li>You'll receive a final confirmation when everything is ready</li>
            </ol>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.setupUrl}" style="background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">View Setup Instructions</a>
          </div>
          
          <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #666; font-size: 14px;">
            <p>Need setup help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p style="margin: 5px 0;">SPEAR - Secure Remote Access Solutions</p>
          </div>
        </body>
      </html>
    `,
  },

  serviceActivated: {
    subject: '🎉 Your SPEAR Service is Now Active!',
    getHtml: (data: {
      customerName: string;
      orderId: string;
      dashboardUrl: string;
      deviceName: string;
    }) => `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Service Activated - SPEAR</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
            <h1 style="margin: 0; font-size: 28px;">🎉 You're All Set!</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Your SPEAR service is now active</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #2c3e50; margin-top: 0;">Hi ${data.customerName},</h2>
            <p>Congratulations! Your SPEAR remote access service is now fully active and ready to use.</p>
            
            <div style="background: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #2c3e50;">Service Details:</h3>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Order ID:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;">#${data.orderId.slice(-8)}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Device:</strong></td>
                  <td style="padding: 8px 0; border-bottom: 1px solid #eee;">${data.deviceName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0;"><strong>Status:</strong></td>
                  <td style="padding: 8px 0; color: #28a745; font-weight: bold;">✅ Active</td>
                </tr>
              </table>
            </div>
          </div>
          
          <div style="background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 20px; margin-bottom: 25px;">
            <h3 style="margin-top: 0; color: #0c5460;">You can now:</h3>
            <ul style="margin: 0; padding-left: 20px;">
              <li>Access your device remotely from anywhere</li>
              <li>View your device status in the dashboard</li>
              <li>Manage your subscription settings</li>
              <li>Contact support for any assistance</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.dashboardUrl}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">Access Your Dashboard</a>
          </div>
          
          <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #666; font-size: 14px;">
            <p>Welcome to SPEAR! Questions? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p style="margin: 5px 0;">SPEAR - Secure Remote Access Solutions</p>
          </div>
        </body>
      </html>
    `,
  },
};

// Email sending functions
export async function sendEmail(
  to: string,
  subject: string,
  html: string,
  text?: string
) {
  try {
    if (!nodemailer) {
      console.warn('Nodemailer not available, skipping email send');
      return { success: false, error: 'Email service not available' };
    }

    if (!EMAIL_CONFIG.auth.user || !EMAIL_CONFIG.auth.pass) {
      console.warn('Email credentials not configured, skipping email send');
      return { success: false, error: 'Email not configured' };
    }

    const transporter = getTransporter();
    
    const mailOptions = {
      from: `${FROM_NAME} <${FROM_EMAIL}>`,
      to,
      subject,
      html,
      text: text || html.replace(/<[^>]*>/g, ''), // Strip HTML for text version
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

export async function sendPaymentConfirmationEmail(data: {
  customerEmail: string;
  customerName: string;
  planName: string;
  amount: number;
  orderId: string;
}) {
  const dashboardUrl = `${process.env.NEXTAUTH_URL || 'https://spear-global.com'}/dashboard/subscription`;
  
  const template = emailTemplates.paymentConfirmation;
  const html = template.getHtml({
    ...data,
    dashboardUrl,
  });

  return sendEmail(data.customerEmail, template.subject, html);
}

export async function sendDeviceShippedEmail(data: {
  customerEmail: string;
  customerName: string;
  orderId: string;
  trackingNumber: string;
  trackingUrl?: string;
  estimatedDelivery?: string;
  shippingAddress: any;
}) {
  const template = emailTemplates.deviceShipped;
  const html = template.getHtml(data);

  return sendEmail(data.customerEmail, template.subject, html);
}

export async function sendDeviceDeliveredEmail(data: {
  customerEmail: string;
  customerName: string;
  orderId: string;
}) {
  const setupUrl = `${process.env.NEXTAUTH_URL || 'https://spear-global.com'}/dashboard/subscription`;
  
  const template = emailTemplates.deviceDelivered;
  const html = template.getHtml({
    ...data,
    setupUrl,
  });

  return sendEmail(data.customerEmail, template.subject, html);
}

export async function sendServiceActivatedEmail(data: {
  customerEmail: string;
  customerName: string;
  orderId: string;
  deviceName: string;
}) {
  const dashboardUrl = `${process.env.NEXTAUTH_URL || 'https://spear-global.com'}/dashboard/subscription`;
  
  const template = emailTemplates.serviceActivated;
  const html = template.getHtml({
    ...data,
    dashboardUrl,
  });

  return sendEmail(data.customerEmail, template.subject, html);
}
