import { prisma } from "@/lib/db";

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  userId?: string; // If null, it's for all admins
  read: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Create a new notification
 */
export async function createNotification(data: {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  userId?: string; // If not provided, notification goes to all admins
}) {
  try {
    const notification = await prisma.notification.create({
      data: {
        title: data.title,
        message: data.message,
        type: data.type,
        userId: data.userId,
        read: false,
      },
    });
    
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Get notifications for a user (or all admin notifications if user is admin)
 */
export async function getNotifications(userId: string, userRole: string) {
  try {
    const whereClause = userRole === 'ADMIN' 
      ? {
          OR: [
            { userId: userId }, // Personal notifications
            { userId: null },   // Global admin notifications
          ]
        }
      : { userId: userId }; // Only personal notifications for clients

    const notifications = await prisma.notification.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      take: 50, // Limit to 50 most recent notifications
    });

    return notifications;
  } catch (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }
}

/**
 * Mark a notification as read
 */
export async function markNotificationAsRead(notificationId: string, userId: string) {
  try {
    const notification = await prisma.notification.update({
      where: { 
        id: notificationId,
        OR: [
          { userId: userId },
          { userId: null }, // Global notifications can be marked as read by any admin
        ]
      },
      data: { read: true },
    });

    return notification;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
}

/**
 * Mark all notifications as read for a user
 */
export async function markAllNotificationsAsRead(userId: string, userRole: string) {
  try {
    const whereClause = userRole === 'ADMIN' 
      ? {
          OR: [
            { userId: userId },
            { userId: null },
          ]
        }
      : { userId: userId };

    const result = await prisma.notification.updateMany({
      where: {
        ...whereClause,
        read: false,
      },
      data: { read: true },
    });

    return result;
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
}

/**
 * Get unread notification count for a user
 */
export async function getUnreadNotificationCount(userId: string, userRole: string) {
  try {
    const whereClause = userRole === 'ADMIN' 
      ? {
          OR: [
            { userId: userId },
            { userId: null },
          ],
          read: false,
        }
      : { 
          userId: userId,
          read: false,
        };

    const count = await prisma.notification.count({
      where: whereClause,
    });

    return count;
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    throw error;
  }
}

/**
 * Create a notification for new user signup (admin notification)
 */
export async function createNewUserSignupNotification(newUser: {
  name: string;
  email: string;
  role: string;
}) {
  return await createNotification({
    title: 'New User Signup',
    message: `${newUser.name} (${newUser.email}) has signed up as a ${newUser.role.toLowerCase()}.`,
    type: 'info',
    // No userId means it goes to all admins
  });
}

/**
 * Create a notification for new device assignment
 */
export async function createDeviceAssignmentNotification(device: {
  name: string;
  rustDeskId: string;
}, client: {
  name: string;
  email: string;
}) {
  return await createNotification({
    title: 'Device Assigned',
    message: `Device "${device.name}" (${device.rustDeskId}) has been assigned to ${client.name} (${client.email}).`,
    type: 'success',
  });
}

/**
 * Create a notification for subscription events
 */
export async function createSubscriptionNotification(user: {
  name: string;
  email: string;
}, event: 'created' | 'cancelled' | 'updated', plan?: string) {
  const eventMessages = {
    created: `${user.name} (${user.email}) has subscribed to the ${plan} plan.`,
    cancelled: `${user.name} (${user.email}) has cancelled their subscription.`,
    updated: `${user.name} (${user.email}) has updated their subscription${plan ? ` to the ${plan} plan` : ''}.`,
  };

  return await createNotification({
    title: `Subscription ${event.charAt(0).toUpperCase() + event.slice(1)}`,
    message: eventMessages[event],
    type: event === 'cancelled' ? 'warning' : 'success',
  });
}
