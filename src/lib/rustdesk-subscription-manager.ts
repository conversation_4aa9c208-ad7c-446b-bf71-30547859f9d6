import { prisma } from '@/lib/db';
import { getRustDeskConfig } from './rustdesk-config';

interface SubscriptionStatus {
  userId: string;
  email: string;
  isActive: boolean;
  planType: string;
  expiresAt?: Date;
  rustDeskUserId?: string;
}

interface RustDeskAccessControl {
  enable: (userId: string, email: string) => Promise<boolean>;
  disable: (userId: string) => Promise<boolean>;
  createUser: (email: string, name: string) => Promise<string | null>;
  deleteUser: (userId: string) => Promise<boolean>;
  assignDevice: (userId: string, deviceId: string) => Promise<boolean>;
}

/**
 * RustDesk Subscription Manager
 * Automatically manages RustDesk access based on Spear subscription status
 */
export class RustDeskSubscriptionManager {
  private accessControl: RustDeskAccessControl;

  constructor() {
    this.accessControl = {
      enable: this.enableRustDeskAccess.bind(this),
      disable: this.disableRustDeskAccess.bind(this),
      createUser: this.createRustDeskUser.bind(this),
      deleteUser: this.deleteRustDeskUser.bind(this),
      assignDevice: this.assignDeviceToUser.bind(this)
    };
  }

  /**
   * Sync all subscription statuses with RustDesk access
   */
  async syncAllSubscriptions(): Promise<{ success: number; failed: number; errors: string[] }> {
    const results = { success: 0, failed: 0, errors: [] as string[] };

    try {
      // Get all users with subscriptions
      const users = await prisma.user.findMany({
        include: {
          subscriptions: {
            where: {
              status: { in: ['active', 'past_due', 'canceled', 'unpaid'] }
            },
            orderBy: { createdAt: 'desc' },
            take: 1
          },
          devices: true
        }
      });

      for (const user of users) {
        try {
          const subscription = user.subscriptions[0];
          const isActive = subscription && ['active', 'past_due'].includes(subscription.status);
          
          await this.syncUserAccess(user.id, user.email, isActive, user.devices);
          results.success++;
        } catch (error) {
          results.failed++;
          results.errors.push(`Failed to sync user ${user.email}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      console.log(`Subscription sync completed: ${results.success} success, ${results.failed} failed`);
      return results;

    } catch (error) {
      console.error('Error syncing subscriptions:', error);
      results.errors.push(`Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return results;
    }
  }

  /**
   * Sync individual user access based on subscription status
   */
  async syncUserAccess(
    userId: string, 
    email: string, 
    hasActiveSubscription: boolean,
    devices: any[] = []
  ): Promise<boolean> {
    try {
      // Get user's current RustDesk status
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { name: true, rustDeskUserId: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      if (hasActiveSubscription) {
        // Enable access
        let rustDeskUserId = user.rustDeskUserId;
        
        // Create RustDesk user if doesn't exist
        if (!rustDeskUserId) {
          rustDeskUserId = await this.accessControl.createUser(email, user.name || email);
          if (rustDeskUserId) {
            await prisma.user.update({
              where: { id: userId },
              data: { rustDeskUserId }
            });
          }
        }

        if (rustDeskUserId) {
          // Enable the user
          await this.accessControl.enable(rustDeskUserId, email);
          
          // Assign devices to the user
          for (const device of devices) {
            if (device.rustDeskId) {
              await this.accessControl.assignDevice(rustDeskUserId, device.rustDeskId);
            }
          }
        }

        console.log(`Enabled RustDesk access for ${email}`);
        return true;

      } else {
        // Disable access
        if (user.rustDeskUserId) {
          await this.accessControl.disable(user.rustDeskUserId);
          console.log(`Disabled RustDesk access for ${email}`);
        }
        return true;
      }

    } catch (error) {
      console.error(`Error syncing access for ${email}:`, error);
      throw error;
    }
  }

  /**
   * Handle subscription payment success
   */
  async onSubscriptionActivated(userId: string): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { devices: true }
      });

      if (user) {
        await this.syncUserAccess(userId, user.email, true, user.devices);
      }
    } catch (error) {
      console.error('Error handling subscription activation:', error);
    }
  }

  /**
   * Handle subscription cancellation or payment failure
   */
  async onSubscriptionDeactivated(userId: string): Promise<void> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (user) {
        await this.syncUserAccess(userId, user.email, false);
      }
    } catch (error) {
      console.error('Error handling subscription deactivation:', error);
    }
  }

  /**
   * Get subscription status for all users
   */
  async getSubscriptionStatuses(): Promise<SubscriptionStatus[]> {
    try {
      const users = await prisma.user.findMany({
        include: {
          subscriptions: {
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      });

      return users.map(user => {
        const subscription = user.subscriptions[0];
        return {
          userId: user.id,
          email: user.email,
          isActive: subscription ? ['active', 'past_due'].includes(subscription.status) : false,
          planType: subscription?.planType || 'No Plan',
          expiresAt: subscription?.currentPeriodEnd,
          rustDeskUserId: user.rustDeskUserId
        };
      });
    } catch (error) {
      console.error('Error getting subscription statuses:', error);
      return [];
    }
  }

  // Private methods for RustDesk API integration

  private async enableRustDeskAccess(userId: string, email: string): Promise<boolean> {
    try {
      const response = await this.makeRustDeskApiRequest(`/users/${userId}`, 'PATCH', {
        enabled: true
      });
      return response.success;
    } catch (error) {
      console.error(`Error enabling RustDesk access for ${email}:`, error);
      return false;
    }
  }

  private async disableRustDeskAccess(userId: string): Promise<boolean> {
    try {
      const response = await this.makeRustDeskApiRequest(`/users/${userId}`, 'PATCH', {
        enabled: false
      });
      return response.success;
    } catch (error) {
      console.error(`Error disabling RustDesk access for user ${userId}:`, error);
      return false;
    }
  }

  private async createRustDeskUser(email: string, name: string): Promise<string | null> {
    try {
      const userId = `spear_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const response = await this.makeRustDeskApiRequest(`/users/${userId}`, 'PUT', {
        name,
        email,
        enabled: true,
        isAdmin: false,
        password: this.generateRandomPassword()
      });
      
      return response.success ? userId : null;
    } catch (error) {
      console.error(`Error creating RustDesk user for ${email}:`, error);
      return null;
    }
  }

  private async deleteRustDeskUser(userId: string): Promise<boolean> {
    try {
      const response = await this.makeRustDeskApiRequest(`/users/${userId}`, 'DELETE');
      return response.success;
    } catch (error) {
      console.error(`Error deleting RustDesk user ${userId}:`, error);
      return false;
    }
  }

  private async assignDeviceToUser(userId: string, deviceId: string): Promise<boolean> {
    try {
      const response = await this.makeRustDeskApiRequest(`/devices/${deviceId}/assign`, 'POST', {
        userId
      });
      return response.success;
    } catch (error) {
      console.error(`Error assigning device ${deviceId} to user ${userId}:`, error);
      return false;
    }
  }

  private async makeRustDeskApiRequest(endpoint: string, method: string = 'GET', body?: any) {
    const config = getRustDeskConfig();
    
    if (!config.serverIp || !config.apiPort) {
      throw new Error('RustDesk server not configured');
    }

    const baseUrl = config.serverIp.includes('localhost') || config.serverIp.includes('192.168')
      ? `http://${config.serverIp}:${config.apiPort}`
      : `https://${config.serverIp}:${config.apiPort}`;

    const url = `${baseUrl}/api${endpoint}`;
    
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.RUSTDESK_API_TOKEN || 'admin-token'}`,
        'User-Agent': 'SPEAR-Subscription-Manager/1.0'
      },
      body: body ? JSON.stringify(body) : undefined,
      signal: AbortSignal.timeout(10000)
    });

    if (!response.ok) {
      throw new Error(`RustDesk API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return { success: true, data };
  }

  private generateRandomPassword(length: number = 16): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }
}

// Export singleton instance
export const rustDeskSubscriptionManager = new RustDeskSubscriptionManager();
