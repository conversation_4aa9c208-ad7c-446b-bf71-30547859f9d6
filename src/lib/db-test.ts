import { prisma } from "@/lib/db";

/**
 * Test database connection and return status
 * @returns Object with connection status and error message if any
 */
export async function testDatabaseConnection() {
  try {
    // Try to query the database
    await prisma.$queryRaw`SELECT 1`;
    return { connected: true, error: null };
  } catch (error) {
    console.error("Database connection test failed:", error);
    return { 
      connected: false, 
      error: error instanceof Error ? error.message : "Unknown database error" 
    };
  }
}

/**
 * Get database connection info for debugging
 * @returns Object with database connection info
 */
export function getDatabaseInfo() {
  // Get database URL from environment (redact password for security)
  const dbUrl = process.env.DATABASE_URL || "";
  const redactedUrl = dbUrl.replace(/\/\/[^:]+:[^@]+@/, "//[username]:[password]@");
  
  return {
    url: redactedUrl,
    provider: prisma._engineConfig?.activeProvider || "unknown",
    isConnected: prisma.$connect !== undefined
  };
}
