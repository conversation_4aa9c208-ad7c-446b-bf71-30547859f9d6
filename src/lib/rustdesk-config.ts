/**
 * RustDesk configuration
 *
 * This file provides access to RustDesk server information from environment variables.
 * In development, these values come from your .env.local file.
 * In production, they should be set in your hosting environment.
 *
 * It also provides functions to interact with RustDesk devices in the database.
 */

import { prisma } from "@/lib/db";

export const rustDeskConfig = {
  // SPEAR Self-Hosted RustDesk Server Configuration
  serverIp: process.env.NEXT_PUBLIC_RUSTDESK_SERVER_IP || process.env.RUSTDESK_SERVER_IP || 'rustdesk.spear-app.com',
  serverPort: process.env.NEXT_PUBLIC_RUSTDESK_SERVER_PORT || process.env.RUSTDESK_SERVER_PORT || '21116',
  apiPort: process.env.NEXT_PUBLIC_RUSTDESK_API_PORT || process.env.RUSTDESK_API_PORT || '21114',
  relayPort: process.env.NEXT_PUBLIC_RUSTDESK_RELAY_PORT || process.env.RUSTDESK_RELAY_PORT || '21117',
  serverKey: process.env.NEXT_PUBLIC_RUSTDESK_SERVER_KEY || process.env.RUSTDESK_SERVER_KEY || '',

  // Get Android device ID from environment variable
  get androidDeviceId() {
    const deviceId = process.env.RUSTDESK_ANDROID_DEVICE_ID || '';
    if (!deviceId && process.env.NODE_ENV === 'production') {
      console.error('RUSTDESK_ANDROID_DEVICE_ID is not set in environment variables');
    }
    return deviceId;
  },

  // Get Android device password from environment variable
  get androidPassword() {
    // In production, this should be stored securely, not in environment variables
    // For now, we'll use an environment variable for simplicity
    const password = process.env.RUSTDESK_ANDROID_PASSWORD || '';
    if (!password && process.env.NODE_ENV === 'production') {
      console.error('RUSTDESK_ANDROID_PASSWORD is not set in environment variables');
    }
    return password;
  },

  // Full server address for client configuration
  get serverAddress() {
    if (!this.serverIp || !this.serverPort) {
      // Return empty string to use public RustDesk servers
      return '';
    }
    return `${this.serverIp}:${this.serverPort}`;
  },

  // Full API address
  get apiAddress() {
    if (!this.serverIp || !this.apiPort) return '';
    return `${this.serverIp}:${this.apiPort}`;
  },

  // Check if using custom server or public servers
  get isUsingCustomServer() {
    return Boolean(this.serverIp && this.serverPort);
  },

  // Get relay server address
  get relayAddress() {
    if (!this.serverIp || !this.relayPort) return '';
    return `${this.serverIp}:${this.relayPort}`;
  },

  // Generate client configuration string for import
  get clientConfigString() {
    if (!this.isUsingCustomServer) return '';

    const config = {
      host: this.serverAddress,
      relay: this.relayAddress,
      api: this.apiAddress,
      key: this.serverKey
    };

    return btoa(JSON.stringify(config));
  },

  // Check if RustDesk is properly configured
  isConfigured: () => {
    // For production, we should check that all required configuration is set
    // At minimum, we need either a custom server or we're using public servers
    const usingCustomServer = Boolean(
      process.env.RUSTDESK_SERVER_IP &&
      process.env.RUSTDESK_SERVER_PORT
    );

    // If we're not using a custom server, we're using public servers which is fine
    return true;
  }
};

/**
 * Get the RustDesk configuration
 *
 * @returns The RustDesk configuration
 */
export function getRustDeskConfig() {
  return rustDeskConfig;
}

/**
 * Check if RustDesk is configured
 *
 * @returns boolean indicating if all required RustDesk server information is set
 */
export function isRustDeskConfigured() {
  return rustDeskConfig.isConfigured();
}

/**
 * Get RustDesk web client URL
 *
 * @param deviceId RustDesk device ID
 * @param password Optional password for the device
 * @returns URL for the RustDesk web client
 */
export function getRustDeskWebClientUrl(deviceId: string, password?: string): string {
  // Format the device ID by removing spaces if present
  const formattedDeviceId = deviceId.replace(/\s+/g, '');

  // Base URL for RustDesk web client
  let url = `https://rustdesk.com/web/?id=${formattedDeviceId}`;

  // Add password if provided
  if (password) {
    url += `&password=${encodeURIComponent(password)}`;
  }

  return url;
}

/**
 * Track RustDesk session
 *
 * @param deviceId RustDesk device ID
 * @param deviceName Name of the device
 */
export function trackRustDeskSession(deviceId: string, deviceName: string): void {
  console.log(`RustDesk session started for device ${deviceName} (${deviceId})`);

  // In a real implementation, this would track the session in a database
  // For now, we just log it to the console
}

/**
 * Format RustDesk device ID
 *
 * @param deviceId RustDesk device ID
 * @returns Formatted device ID
 */
export function formatRustDeskDeviceId(deviceId: string): string {
  // Format the device ID by removing spaces if present
  return deviceId.replace(/\s+/g, '');
}

/**
 * Get RustDesk devices from the database
 *
 * @param userId Optional user ID to filter devices by
 * @returns Promise with an array of RustDesk devices
 */
export async function getRustDeskDevices(userId?: string) {
  try {
    // Query the database for devices
    const devices = userId
      ? await prisma.device.findMany({
          where: {
            userId: userId
          }
        })
      : await prisma.device.findMany();

    return devices;
  } catch (error) {
    console.error("Error fetching RustDesk devices:", error);
    return [];
  }
}

/**
 * Get a RustDesk device by ID
 *
 * @param deviceId The RustDesk device ID
 * @returns Promise with the device or null if not found
 */
export async function getRustDeskDeviceById(deviceId: string) {
  try {
    const device = await prisma.device.findUnique({
      where: {
        rustDeskId: deviceId
      },
      include: {
        assignedTo: true
      }
    });

    return device;
  } catch (error) {
    console.error(`Error fetching RustDesk device with ID ${deviceId}:`, error);
    return null;
  }
}

/**
 * Update a RustDesk device's status
 *
 * @param deviceId The RustDesk device ID
 * @param status The new status (online, offline, unassigned)
 * @returns Promise with the updated device
 */
export async function updateRustDeskDeviceStatus(deviceId: string, status: string) {
  try {
    const device = await prisma.device.update({
      where: {
        rustDeskId: deviceId
      },
      data: {
        status: status
      }
    });

    return device;
  } catch (error) {
    console.error(`Error updating RustDesk device status for ID ${deviceId}:`, error);
    return null;
  }
}

/**
 * Generate installation instructions for SPEAR clients
 *
 * @returns String with detailed setup instructions
 */
export function generateSpearInstallInstructions(): string {
  const config = rustDeskConfig;

  if (!config.isUsingCustomServer) {
    return `
SPEAR RustDesk Setup Instructions (Public Servers):

1. Download RustDesk from: https://rustdesk.com/
2. Install and open RustDesk
3. Your device is ready to use with public RustDesk servers
4. Share your Device ID with SPEAR support for assistance

Note: SPEAR is currently using public RustDesk servers.
    `.trim();
  }

  return `
SPEAR RustDesk Setup Instructions (SPEAR Private Server):

1. Download RustDesk from: https://rustdesk.com/
2. Install and open RustDesk
3. Click the menu (⋮) next to your ID
4. Select "Network"
5. Click "Unlock" (may require admin privileges)
6. Enter the following SPEAR server settings:

   ID Server: ${config.serverAddress}
   Relay Server: ${config.relayAddress}
   API Server: ${config.apiAddress}
   Key: ${config.serverKey}

7. Click "Apply"

Quick Import Option:
Copy this configuration code: ${config.clientConfigString}

Then in RustDesk:
1. Go to Settings → Network
2. Click "Import Server Config"
3. Paste the configuration
4. Click "Apply"

✅ You're now connected to SPEAR's private RustDesk server for faster, more reliable connections!
  `.trim();
}

/**
 * Check SPEAR RustDesk server health
 *
 * @returns Promise with server status
 */
export async function checkSpearServerHealth() {
  if (!rustDeskConfig.isUsingCustomServer) {
    return {
      status: 'public',
      message: 'Using public RustDesk servers',
      server: 'public.rustdesk.com'
    };
  }

  try {
    // Try to connect to the ID server port
    const response = await fetch(`http://${rustDeskConfig.serverIp}:21115`, {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000)
    });

    return {
      status: response.ok ? 'online' : 'offline',
      message: response.ok ? 'SPEAR RustDesk server is healthy' : 'SPEAR RustDesk server is not responding',
      server: rustDeskConfig.serverAddress,
      lastChecked: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'offline',
      message: 'Failed to connect to SPEAR RustDesk server',
      server: rustDeskConfig.serverAddress,
      error: error instanceof Error ? error.message : 'Unknown error',
      lastChecked: new Date().toISOString()
    };
  }
}

/**
 * Get SPEAR RustDesk server configuration for admin dashboard
 *
 * @returns Server configuration object
 */
export function getSpearServerConfig() {
  return {
    isUsingCustomServer: rustDeskConfig.isUsingCustomServer,
    serverIp: rustDeskConfig.serverIp,
    serverPort: rustDeskConfig.serverPort,
    relayPort: rustDeskConfig.relayPort,
    apiPort: rustDeskConfig.apiPort,
    hasServerKey: !!rustDeskConfig.serverKey,
    serverAddress: rustDeskConfig.serverAddress,
    relayAddress: rustDeskConfig.relayAddress,
    apiAddress: rustDeskConfig.apiAddress,
    clientConfigString: rustDeskConfig.clientConfigString
  };
}
