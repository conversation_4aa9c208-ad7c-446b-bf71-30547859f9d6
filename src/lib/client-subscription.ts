import { prisma } from "@/lib/db";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

// Define the subscription structure
export interface ClientSubscription {
  id: string;
  plan: string;
  status: string;
  price: number;
  billingCycle: string;
  nextBillingDate: string;
  startDate: string;
  devices: number;
  maxDevices: number;
  features: {
    name: string;
    included: boolean;
  }[];
  paymentMethod: {
    type: string;
    brand: string;
    last4: string;
    expMonth: number;
    expYear: number;
  };
}

// Define the billing history structure
export interface BillingHistoryItem {
  id: string;
  date: string;
  amount: number;
  status: string;
  description: string;
}

// Define the available plans structure
export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  billingCycle: string;
  description: string;
  features: string[];
  current: boolean;
}

// Get features based on plan
function getFeaturesForPlan(plan: string): { name: string; included: boolean }[] {
  const allFeatures = [
    "Remote Access",
    "Location Verification",
    "Session Recording",
    "Priority Support",
    "Custom Branding",
    "API Access",
    "Dedicated Account Manager"
  ];
  
  let includedFeatures: string[] = [];
  
  switch (plan.toLowerCase()) {
    case "basic":
      includedFeatures = allFeatures.slice(0, 3);
      break;
    case "professional":
      includedFeatures = allFeatures.slice(0, 5);
      break;
    case "enterprise":
      includedFeatures = allFeatures;
      break;
    default:
      includedFeatures = allFeatures.slice(0, 3);
  }
  
  return allFeatures.map(feature => ({
    name: feature,
    included: includedFeatures.includes(feature)
  }));
}

// Get max devices based on plan
function getMaxDevicesForPlan(plan: string): number {
  switch (plan.toLowerCase()) {
    case "basic":
      return 5;
    case "professional":
      return 15;
    case "enterprise":
      return 999; // Unlimited
    default:
      return 5;
  }
}

// Get price based on plan
function getPriceForPlan(plan: string): number {
  switch (plan.toLowerCase()) {
    case "basic":
      return 350;
    case "professional":
      return 750;
    case "enterprise":
      return 1500;
    default:
      return 350;
  }
}

/**
 * Get client subscription
 * @returns Client subscription
 */
export async function getClientSubscription(): Promise<ClientSubscription | null> {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      throw new Error("User not authenticated");
    }
    
    // Get the user from the database
    const user = await prisma.user.findUnique({
      where: {
        email: session.user.email,
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
        devices: true,
        paymentMethods: {
          where: {
            isDefault: true,
          },
          take: 1,
        },
      },
    });
    
    if (!user) {
      throw new Error("User not found");
    }
    
    // If the user doesn't have a subscription, return null
    if (user.subscriptions.length === 0) {
      return null;
    }
    
    const subscription = user.subscriptions[0];
    const plan = subscription.plan || "basic";
    const features = getFeaturesForPlan(plan);
    const maxDevices = getMaxDevicesForPlan(plan);
    
    // Format the subscription data
    return {
      id: subscription.id,
      plan: `${plan.charAt(0).toUpperCase() + plan.slice(1)} Plan`,
      status: subscription.status,
      price: getPriceForPlan(plan),
      billingCycle: "monthly",
      nextBillingDate: subscription.currentPeriodEnd.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
      startDate: subscription.createdAt.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
      devices: user.devices.length,
      maxDevices,
      features,
      paymentMethod: user.paymentMethods.length > 0 ? {
        type: "credit_card",
        brand: user.paymentMethods[0].brand || "Visa",
        last4: user.paymentMethods[0].last4 || "4242",
        expMonth: user.paymentMethods[0].expMonth || 12,
        expYear: user.paymentMethods[0].expYear || 2024,
      } : {
        type: "credit_card",
        brand: "Visa",
        last4: "4242",
        expMonth: 12,
        expYear: 2024,
      },
    };
  } catch (error) {
    console.error("Error getting client subscription:", error);
    throw error;
  }
}

/**
 * Get client billing history
 * @returns Client billing history
 */
export async function getClientBillingHistory(): Promise<BillingHistoryItem[]> {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      throw new Error("User not authenticated");
    }
    
    // Get the user's invoices from the database
    const invoices = await prisma.invoice.findMany({
      where: {
        user: {
          email: session.user.email,
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });
    
    // Format the invoices data
    return invoices.map(invoice => ({
      id: invoice.id,
      date: invoice.createdAt.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      }),
      amount: parseFloat(invoice.amount.replace("$", "")),
      status: invoice.status,
      description: invoice.description,
    }));
  } catch (error) {
    console.error("Error getting client billing history:", error);
    return [];
  }
}

/**
 * Get available subscription plans
 * @returns Available subscription plans
 */
export async function getAvailablePlans(): Promise<SubscriptionPlan[]> {
  try {
    // Get the current user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      throw new Error("User not authenticated");
    }
    
    // Get the user's current subscription
    const user = await prisma.user.findUnique({
      where: {
        email: session.user.email,
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
      },
    });
    
    if (!user) {
      throw new Error("User not found");
    }
    
    const currentPlan = user.subscriptions.length > 0 ? user.subscriptions[0].plan : null;
    
    // Define the available plans
    const plans: SubscriptionPlan[] = [
      {
        id: "basic",
        name: "Basic Plan",
        price: 350,
        billingCycle: "monthly",
        description: "Essential features for small teams",
        features: [
          "Up to 5 devices",
          "Remote Access",
          "Location Verification",
          "Session Recording",
          "Standard Support",
        ],
        current: currentPlan === "basic",
      },
      {
        id: "professional",
        name: "Professional Plan",
        price: 750,
        billingCycle: "monthly",
        description: "Advanced features for growing businesses",
        features: [
          "Up to 15 devices",
          "Remote Access",
          "Location Verification",
          "Session Recording",
          "Priority Support",
          "Custom Branding",
        ],
        current: currentPlan === "professional",
      },
      {
        id: "enterprise",
        name: "Enterprise Plan",
        price: 1500,
        billingCycle: "monthly",
        description: "Complete solution for large organizations",
        features: [
          "Unlimited devices",
          "Remote Access",
          "Location Verification",
          "Session Recording",
          "Priority Support",
          "Custom Branding",
          "API Access",
          "Dedicated Account Manager",
        ],
        current: currentPlan === "enterprise",
      },
    ];
    
    return plans;
  } catch (error) {
    console.error("Error getting available plans:", error);
    throw error;
  }
}
