/**
 * Utility functions for authentication that work in both server and edge environments
 */

/**
 * Check if the code is running in an edge runtime
 */
export const isEdgeRuntime = () => {
  return process.env.NEXT_RUNTIME === 'edge';
};

/**
 * Hash a password using SHA-256 (for edge runtime)
 * This is less secure than bcrypt but works in edge runtime
 */
export const hashPasswordSha256 = async (password: string): Promise<string> => {
  if (typeof crypto !== 'undefined' && crypto.subtle) {
    // Use Web Crypto API (works in both edge and server runtime)
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } else {
    // Fallback for environments without Web Crypto API
    // This is a simple hash (not secure, but works for development)
    console.warn('Web Crypto API not available, using simple hash');
    return btoa(password).replace(/[^a-f0-9]/gi, '').substring(0, 64).padEnd(64, '0');
  }
};

/**
 * Verify a password against a SHA-256 hash (for edge runtime)
 */
export const verifyPasswordSha256 = async (password: string, hashedPassword: string): Promise<boolean> => {
  const hash = await hashPasswordSha256(password);
  return hash === hashedPassword;
};

/**
 * Hash a password using the appropriate method based on the runtime
 */
export const hashPassword = async (password: string): Promise<string> => {
  // Always use SHA-256 to avoid Edge Runtime issues
  return await hashPasswordSha256(password);
};

/**
 * Verify a password against a hashed password using the appropriate method
 */
export const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  // Check if the hashedPassword is in the SHA-256 format (64 hex characters)
  if (/^[a-f0-9]{64}$/i.test(hashedPassword)) {
    return await verifyPasswordSha256(password, hashedPassword);
  }

  // For bcrypt hashes, try to use bcrypt if available (server runtime)
  if (!isEdgeRuntime()) {
    try {
      // Dynamic import to avoid edge runtime issues
      const bcrypt = await import('bcrypt');
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      console.error('Error importing bcrypt:', error);
    }
  }

  // Fallback: assume it's a SHA-256 hash if we can't use bcrypt
  console.warn('Unable to verify bcrypt hash, falling back to SHA-256 verification');
  return await verifyPasswordSha256(password, hashedPassword);
};
