import { prisma } from "@/lib/db";

/**
 * Get devices assigned to a specific user
 * @param userId The ID of the user to get devices for
 * @returns Array of devices assigned to the user
 */
export async function getUserDevices(userId: string) {
  try {
    const devices = await prisma.device.findMany({
      where: {
        userId: userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Format the devices data
    return devices.map((device) => ({
      id: device.id,
      name: device.name,
      deviceModel: device.model || "Unknown Model",
      deviceId: device.rustDeskId,
      status: device.status,
      lastCheckIn: device.updatedAt.toISOString(),
    }));
  } catch (error) {
    console.error(`Error getting devices for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Update a device name
 * @param deviceId The ID of the device to update
 * @param name The new name for the device
 * @param userId The ID of the user who owns the device (for verification)
 * @returns Updated device
 */
export async function updateDeviceName(deviceId: string, name: string, userId: string) {
  try {
    // First, verify that the device belongs to the user
    const device = await prisma.device.findUnique({
      where: {
        id: deviceId,
      },
    });

    if (!device) {
      throw new Error(`Device with ID ${deviceId} not found`);
    }

    if (device.userId !== userId) {
      throw new Error(`Device with ID ${deviceId} does not belong to user ${userId}`);
    }

    // Update the device name
    const updatedDevice = await prisma.device.update({
      where: {
        id: deviceId,
      },
      data: {
        name,
      },
    });

    // Format the device data
    return {
      id: updatedDevice.id,
      name: updatedDevice.name,
      deviceModel: updatedDevice.model || "Unknown Model",
      deviceId: updatedDevice.rustDeskId,
      status: updatedDevice.status,
      lastCheckIn: updatedDevice.updatedAt.toISOString(),
    };
  } catch (error) {
    console.error(`Error updating device name for device ${deviceId}:`, error);
    throw error;
  }
}

/**
 * Get user subscription information
 * @param userId The ID of the user to get subscription information for
 * @returns User subscription information
 */
export async function getUserSubscription(userId: string) {
  try {
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: userId,
      },
      orderBy: {
        currentPeriodEnd: "desc",
      },
    });

    if (!subscription) {
      return {
        status: "none",
        currentPeriodEnd: new Date().toISOString(),
        deviceLimit: 0,
      };
    }

    // For now, we'll use a simple rule: active subscriptions get 2 devices, others get 0
    const deviceLimit = subscription.status === "active" ? 2 : 0;

    return {
      status: subscription.status,
      currentPeriodEnd: subscription.currentPeriodEnd.toISOString(),
      deviceLimit,
    };
  } catch (error) {
    console.error(`Error getting subscription for user ${userId}:`, error);
    throw error;
  }
}

/**
 * Check if a user can add more devices
 * @param userId The ID of the user to check
 * @returns Boolean indicating if the user can add more devices
 */
export async function canUserAddDevices(userId: string) {
  try {
    // Get the user's subscription
    const subscription = await getUserSubscription(userId);
    
    // Get the user's current device count
    const deviceCount = await prisma.device.count({
      where: {
        userId: userId,
      },
    });
    
    // Check if the user can add more devices
    return deviceCount < subscription.deviceLimit;
  } catch (error) {
    console.error(`Error checking if user ${userId} can add more devices:`, error);
    throw error;
  }
}
