import { PrismaClient } from "@prisma/client";

// This is to prevent multiple instances of Prisma Client in development
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Check if we're in a browser environment
const isServer = typeof window === 'undefined';

// Only instantiate PrismaClient on the server
export const prisma = isServer
  ? globalForPrisma.prisma || new PrismaClient()
  : (null as unknown as PrismaClient); // This will cause a runtime error if used on the client

if (isServer && process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;
}
