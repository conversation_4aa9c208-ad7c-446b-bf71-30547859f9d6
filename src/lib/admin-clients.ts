import { prisma } from "@/lib/db";

/**
 * Get all clients with their subscription and device information
 * @returns Array of clients with their subscription and device information
 */
export async function getAllClients() {
  try {
    const clients = await prisma.user.findMany({
      where: {
        role: "CLIENT",
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
        devices: true,
        _count: {
          select: {
            devices: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Format the clients data
    return clients.map((client) => ({
      id: client.id,
      name: client.name || "Unnamed Client",
      email: client.email,
      company: client.email.split("@")[1]?.split(".")[0] || "", // Extract company from email domain as a fallback
      phone: "", // We don't have phone in the schema, but the UI expects it
      subscription: client.subscriptions[0] || {
        id: "",
        status: "none",
        currentPeriodEnd: new Date(),
        plan: "No Plan",
        price: "$0.00",
      },
      devices: client.devices.map((device) => ({
        id: device.id,
        name: device.name,
        deviceModel: device.model || "Unknown Model",
        deviceId: device.rustDeskId,
        status: device.status,
      })),
      createdAt: client.createdAt.toISOString(),
    }));
  } catch (error) {
    console.error("Error getting all clients:", error);
    throw error;
  }
}

/**
 * Get a client by ID with their subscription and device information
 * @param clientId The ID of the client to get
 * @returns Client with their subscription and device information
 */
export async function getClientById(clientId: string) {
  try {
    const client = await prisma.user.findUnique({
      where: {
        id: clientId,
        role: "CLIENT",
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
        devices: true,
      },
    });

    if (!client) {
      throw new Error(`Client with ID ${clientId} not found`);
    }

    // Format the client data
    return {
      id: client.id,
      name: client.name || "Unnamed Client",
      email: client.email,
      company: client.email.split("@")[1]?.split(".")[0] || "", // Extract company from email domain as a fallback
      phone: "", // We don't have phone in the schema, but the UI expects it
      subscription: client.subscriptions[0] || {
        id: "",
        status: "none",
        currentPeriodEnd: new Date(),
        plan: "No Plan",
        price: "$0.00",
      },
      devices: client.devices.map((device) => ({
        id: device.id,
        name: device.name,
        deviceModel: device.model || "Unknown Model",
        deviceId: device.rustDeskId,
        status: device.status,
      })),
      createdAt: client.createdAt.toISOString(),
    };
  } catch (error) {
    console.error(`Error getting client with ID ${clientId}:`, error);
    throw error;
  }
}

/**
 * Update a client's subscription status
 * @param clientId The ID of the client to update
 * @param status The new subscription status
 * @returns Updated client
 */
export async function updateClientSubscriptionStatus(clientId: string, status: string) {
  try {
    // Get the client's subscription
    const client = await prisma.user.findUnique({
      where: {
        id: clientId,
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
      },
    });

    if (!client) {
      throw new Error(`Client with ID ${clientId} not found`);
    }

    if (client.subscriptions.length === 0) {
      throw new Error(`Client with ID ${clientId} has no subscription`);
    }

    // Update the subscription status
    const updatedSubscription = await prisma.subscription.update({
      where: {
        id: client.subscriptions[0].id,
      },
      data: {
        status,
      },
    });

    // If the subscription is canceled, update all devices to unassigned
    if (status === "canceled") {
      await prisma.device.updateMany({
        where: {
          userId: clientId,
        },
        data: {
          status: "unassigned",
        },
      });
    }

    return updatedSubscription;
  } catch (error) {
    console.error(`Error updating subscription status for client ${clientId}:`, error);
    throw error;
  }
}
