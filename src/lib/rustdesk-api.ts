import { getRustDeskConfig } from "./rustdesk-config";
import { prisma } from "@/lib/db";

// Define interfaces for RustDesk types
export interface RustDeskDevice {
  id: string;
  name: string;
  online_state: string;
  last_seen?: string;
  device_info?: {
    model?: string;
    os_version?: string;
  };
}

export interface RustDeskSession {
  id: string;
  state: string;
  created: string;
  device_id: string;
  connection_url: string;
}

/**
 * Get devices from the database
 *
 * @param userId Optional user ID to filter devices by
 * @returns Promise with an array of RustDesk devices
 */
export async function getRustDeskDevices(userId?: string): Promise<RustDeskDevice[]> {
  try {
    // Query the database for devices
    const devices = userId
      ? await prisma.device.findMany({
          where: {
            userId: userId
          }
        })
      : await prisma.device.findMany();

    // Convert to RustDeskDevice format
    return devices.map(device => ({
      id: device.rustDeskId,
      name: device.name,
      online_state: device.status || "offline",
      last_seen: device.updatedAt.toISOString(),
      device_info: {
        model: device.model || "Unknown"
      }
    }));
  } catch (error) {
    console.error("Error fetching RustDesk devices:", error);
    return [];
  }
}

/**
 * Format a RustDesk device ID for display
 *
 * @param deviceId The device ID to format
 * @returns The formatted device ID
 */
export function formatRustDeskDeviceId(deviceId: string): string {
  // RustDesk IDs are typically 9-digit numbers
  // We'll format them as XXX XXX XXX for readability
  return deviceId.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3');
}

/**
 * Connect to a RustDesk device
 *
 * @param deviceId The ID of the device to connect to
 * @param password Optional password for the connection
 * @returns The connection URL
 */
export function connectToRustDeskDevice(deviceId: string, password?: string): string {
  // Build the connection URL
  // RustDesk uses the rustdesk:// protocol for direct connections
  let connectionUrl = `rustdesk://${deviceId}`;

  // If we have a password, add it to the URL
  if (password) {
    connectionUrl += `?password=${encodeURIComponent(password)}`;
  }

  return connectionUrl;
}

/**
 * Get the web client URL for a RustDesk device
 *
 * @param deviceId The ID of the device to connect to
 * @param password Optional password for the connection
 * @returns The web client URL
 */
export function getRustDeskWebClientUrl(deviceId: string, password?: string): string {
  // Build the web client URL
  let webClientUrl = `https://rustdesk.com/web/?id=${deviceId}`;

  // If we have a password, add it to the URL
  if (password) {
    webClientUrl += `&password=${encodeURIComponent(password)}`;
  }

  return webClientUrl;
}

/**
 * Track a RustDesk session
 *
 * @param deviceId The ID of the device
 * @param deviceName The name of the device
 * @returns The session information
 */
export function trackRustDeskSession(deviceId: string, deviceName: string): RustDeskSession {
  // In a real implementation, you would store this in your database
  return {
    id: `session-${Date.now()}`,
    state: 'Open',
    created: new Date().toISOString(),
    device_id: deviceId,
    connection_url: connectToRustDeskDevice(deviceId)
  };
}

/**
 * Get a device by its RustDesk ID
 *
 * @param rustDeskId The RustDesk ID of the device
 * @returns Promise with the device or null if not found
 */
export async function getDeviceByRustDeskId(rustDeskId: string) {
  try {
    return await prisma.device.findUnique({
      where: {
        rustDeskId
      },
      include: {
        assignedTo: true
      }
    });
  } catch (error) {
    console.error("Error fetching device by RustDesk ID:", error);
    return null;
  }
}

/**
 * Create a new device
 *
 * @param data The device data
 * @returns Promise with the created device
 */
export async function createDevice(data: {
  name: string;
  rustDeskId: string;
  password?: string;
  model?: string;
  userId?: string;
}) {
  try {
    return await prisma.device.create({
      data
    });
  } catch (error) {
    console.error("Error creating device:", error);
    throw error;
  }
}

/**
 * Update a device
 *
 * @param id The device ID
 * @param data The device data to update
 * @returns Promise with the updated device
 */
export async function updateDevice(id: string, data: {
  name?: string;
  rustDeskId?: string;
  password?: string;
  model?: string;
  userId?: string;
  status?: string;
}) {
  try {
    return await prisma.device.update({
      where: { id },
      data
    });
  } catch (error) {
    console.error("Error updating device:", error);
    throw error;
  }
}

/**
 * Assign a device to a user
 *
 * @param deviceId The device ID
 * @param userId The user ID
 * @returns Promise with the updated device
 */
export async function assignDeviceToUser(deviceId: string, userId: string) {
  try {
    return await prisma.device.update({
      where: { id: deviceId },
      data: {
        userId,
        status: "offline" // Reset status when assigning
      }
    });
  } catch (error) {
    console.error("Error assigning device to user:", error);
    throw error;
  }
}

/**
 * Unassign a device from a user
 *
 * @param deviceId The device ID
 * @returns Promise with the updated device
 */
export async function unassignDevice(deviceId: string) {
  try {
    return await prisma.device.update({
      where: { id: deviceId },
      data: {
        userId: null,
        status: "unassigned"
      }
    });
  } catch (error) {
    console.error("Error unassigning device:", error);
    throw error;
  }
}
