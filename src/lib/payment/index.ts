/**
 * Payment Module Index
 * Exports all payment-related functionality
 */

// Core types and interfaces
export * from './types';

// Payment factory and service management
export { PaymentFactory, getPaymentService, getPaymentFactory } from './payment-factory';

// Payment service implementations
export { PayPalPaymentService } from './providers/paypal-service';

// Configuration
export { paypalConfig, validatePayPalConfig, getPayPalConfigForLogging } from './paypal-config';

// Utility functions
export { formatCurrency, parseCurrency, calculateDiscount } from './utils';

// Re-export commonly used types for convenience
export type {
  PaymentService,
  PaymentProvider,
  PaymentConfig,
  PaymentRequest,
  PaymentResult,
  SubscriptionRequest,
  SubscriptionResult,
  Coupon,
  PaymentAmount,
  BillingAddress,
  WebhookEvent
} from './types';
