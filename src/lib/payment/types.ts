/**
 * Payment Service Types and Interfaces
 * Defines the contract for payment processors in the SPEAR application
 */

export interface PaymentAmount {
  amount: number; // Amount in cents
  currency: string;
}

export interface PaymentCustomer {
  id?: string;
  email: string;
  name?: string;
  phone?: string;
}

export interface BillingAddress {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country?: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'bank_account';
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  amount: PaymentAmount;
  interval: 'month' | 'year';
  features: string[];
}

export interface Coupon {
  code: string;
  discountType: 'fixed' | 'percentage';
  discountValue: number; // Amount in cents for fixed, percentage for percentage
  isValid: boolean;
  expiresAt?: Date;
}

export interface PaymentRequest {
  customer: PaymentCustomer;
  amount: PaymentAmount;
  planId: string;
  couponCode?: string;
  billingAddress: BillingAddress;
  metadata?: Record<string, any>;
}

export interface SubscriptionRequest {
  customer: PaymentCustomer;
  planId: string;
  paymentMethodId?: string;
  couponCode?: string;
  billingAddress: BillingAddress;
  metadata?: Record<string, any>;
}

export interface PaymentResult {
  success: boolean;
  paymentId: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  amount: PaymentAmount;
  fees?: PaymentAmount;
  metadata?: Record<string, any>;
  error?: string;
}

export interface SubscriptionResult {
  success: boolean;
  subscriptionId: string;
  status: 'active' | 'pending' | 'cancelled' | 'past_due' | 'unpaid';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  amount: PaymentAmount;
  metadata?: Record<string, any>;
  error?: string;
}

export interface WebhookEvent {
  id: string;
  type: string;
  data: any;
  timestamp: Date;
  signature?: string;
}

export interface WebhookVerificationResult {
  isValid: boolean;
  event?: WebhookEvent;
  error?: string;
}

/**
 * Abstract Payment Service Interface
 * All payment processors must implement this interface
 */
export abstract class PaymentService {
  abstract readonly name: string;
  abstract readonly supportedFeatures: {
    oneTimePayments: boolean;
    subscriptions: boolean;
    webhooks: boolean;
    refunds: boolean;
    coupons: boolean;
  };

  // Payment Methods
  abstract processPayment(request: PaymentRequest): Promise<PaymentResult>;
  abstract refundPayment(paymentId: string, amount?: number): Promise<PaymentResult>;
  abstract getPaymentStatus(paymentId: string): Promise<PaymentResult>;

  // Subscription Methods
  abstract createSubscription(request: SubscriptionRequest): Promise<SubscriptionResult>;
  abstract cancelSubscription(subscriptionId: string): Promise<SubscriptionResult>;
  abstract updateSubscription(subscriptionId: string, updates: Partial<SubscriptionRequest>): Promise<SubscriptionResult>;
  abstract getSubscriptionStatus(subscriptionId: string): Promise<SubscriptionResult>;

  // Coupon Methods
  abstract validateCoupon(code: string, planId: string): Promise<Coupon>;
  abstract applyCoupon(amount: PaymentAmount, coupon: Coupon): PaymentAmount;

  // Webhook Methods
  abstract verifyWebhook(payload: string, signature: string): Promise<WebhookVerificationResult>;
  abstract handleWebhookEvent(event: WebhookEvent): Promise<void>;

  // Utility Methods
  abstract formatAmount(amount: number, currency: string): string;
  abstract parseAmount(formattedAmount: string): number;
}

/**
 * Payment Provider Types
 */
export type PaymentProvider = 'paypal' | 'stripe' | 'square';

/**
 * Payment Configuration
 */
export interface PaymentConfig {
  provider: PaymentProvider;
  apiKey: string;
  secretKey: string;
  webhookSecret: string;
  environment: 'sandbox' | 'production';
  currency: string;
}

/**
 * SPEAR-specific subscription plans
 */
export const SPEAR_SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'single-user',
    name: 'SPEAR Single User Plan',
    description: 'Remote access to one assigned device',
    amount: { amount: 29900, currency: 'USD' }, // $299.00 (SPEARMINT brings to $199)
    interval: 'month',
    features: [
      'One desktop device registration',
      'Remote access via web, desktop, and mobile',
      'Basic support',
      'Device management'
    ]
  },
  {
    id: 'two-user-bundle',
    name: 'SPEAR Two User Bundle',
    description: 'Remote access for two users (grandfathered pricing)',
    amount: { amount: 59800, currency: 'USD' }, // $598.00 (grandfathered at $298 for early customers)
    interval: 'month',
    features: [
      'Two desktop device registrations',
      'Remote access via web, desktop, and mobile',
      'Priority support',
      'Device management',
      'Multi-user coordination'
    ]
  }
];

/**
 * SPEAR coupon configuration
 */
export const SPEAR_COUPONS: Record<string, Omit<Coupon, 'isValid'>> = {
  'SPEARMINT': {
    code: 'SPEARMINT',
    discountType: 'fixed',
    discountValue: 10000, // $100.00 discount
  },
  'INSIDER2024': {
    code: 'INSIDER2024',
    discountType: 'fixed',
    discountValue: 28900, // $289.00 discount ($299 → $10)
  }
};
