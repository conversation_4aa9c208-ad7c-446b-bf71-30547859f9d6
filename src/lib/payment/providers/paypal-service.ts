/**
 * PayPal Payment Service Implementation
 * Implements the PaymentService interface for PayPal integration
 */

import { 
  PaymentService, 
  PaymentConfig, 
  PaymentRequest, 
  PaymentResult, 
  SubscriptionRequest, 
  SubscriptionResult, 
  Coupon, 
  PaymentAmount, 
  WebhookEvent, 
  WebhookVerificationResult,
  SPEAR_COUPONS 
} from '../types';

// PayPal SDK imports
import { Client, Environment, OrdersController } from '@paypal/paypal-server-sdk';

export class PayPalPaymentService extends PaymentService {
  public readonly name = 'PayPal';
  public readonly supportedFeatures = {
    oneTimePayments: true,
    subscriptions: true,
    webhooks: true,
    refunds: true,
    coupons: true,
  };

  private client: Client;
  private ordersController: OrdersController;
  private config: PaymentConfig;

  constructor(config: PaymentConfig) {
    super();
    this.config = config;

    console.log('Initializing PayPal client with config:', {
      apiKey: config.apiKey ? `${config.apiKey.substring(0, 10)}...` : 'undefined',
      secretKey: config.secretKey ? `${config.secretKey.substring(0, 10)}...` : 'undefined',
      environment: config.environment
    });

    // Initialize PayPal client
    this.client = new Client({
      clientCredentialsAuthCredentials: {
        oAuthClientId: config.apiKey,
        oAuthClientSecret: config.secretKey,
      },
      environment: config.environment === 'production' ? Environment.Production : Environment.Sandbox,
    });

    // Initialize OrdersController
    this.ordersController = new OrdersController(this.client);

    console.log('PayPal client and OrdersController initialized successfully');
  }

  /**
   * Process a one-time payment
   */
  async processPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      // Use the amount as-is (frontend has already applied coupon discounts)
      let finalAmount = request.amount;
      console.log('PayPal processPayment - Amount received:', finalAmount);
      console.log('PayPal processPayment - Coupon code:', request.couponCode);

      // Check if this is a discounted amount (less than plan base amount)
      const planBaseAmount = this.getPlanBaseAmount(request.planId);
      const isDiscounted = finalAmount.amount < planBaseAmount;
      console.log('PayPal processPayment - Plan base amount:', planBaseAmount);
      console.log('PayPal processPayment - Is discounted:', isDiscounted);

      // If amount is already discounted, don't apply coupon again
      if (!isDiscounted && request.couponCode) {
        const coupon = await this.validateCoupon(request.couponCode, request.planId);
        console.log('PayPal processPayment - Coupon validation result:', coupon);
        if (coupon.isValid) {
          finalAmount = this.applyCoupon(request.amount, coupon);
          console.log('PayPal processPayment - Final amount after coupon:', finalAmount);
        }
      }

      // Create PayPal order
      const orderRequest = {
        intent: 'CAPTURE',
        purchaseUnits: [{
          amount: {
            currencyCode: finalAmount.currency,
            value: (finalAmount.amount / 100).toFixed(2), // Convert cents to dollars
          },
          description: `SPEAR Subscription - ${request.planId}`,
          customId: request.customer.id,
          invoiceId: `SPEAR-${Date.now()}`,
        }],
        paymentSource: {
          paypal: {
            experienceContext: {
              paymentMethodPreference: 'IMMEDIATE_PAYMENT_REQUIRED',
              brandName: 'SPEAR',
              locale: 'en-US',
              landingPage: 'LOGIN',
              userAction: 'PAY_NOW',
              returnUrl: `${process.env.NEXTAUTH_URL}/checkout/success`,
              cancelUrl: `${process.env.NEXTAUTH_URL}/checkout/cancel`,
            }
          }
        }
      };

      console.log('Creating PayPal order with request:', JSON.stringify(orderRequest, null, 2));

      const response = await this.ordersController.createOrder({
        body: orderRequest,
        prefer: 'return=representation'
      });

      console.log('PayPal order creation response:', response);

      if ((response.statusCode === 200 || response.statusCode === 201) && response.result) {
        return {
          success: true,
          paymentId: response.result.id!,
          status: 'pending',
          amount: finalAmount,
          metadata: {
            approvalUrl: response.result.links?.find(link => link.rel === 'approve')?.href,
            paypalOrderId: response.result.id,
            customer: request.customer,
            planId: request.planId,
            couponCode: request.couponCode,
          }
        };
      }

      throw new Error('Failed to create PayPal order');

    } catch (error) {
      console.error('PayPal payment processing error:', error);
      return {
        success: false,
        paymentId: '',
        status: 'failed',
        amount: request.amount,
        error: error instanceof Error ? error.message : 'Payment processing failed'
      };
    }
  }

  /**
   * Capture a PayPal payment (called after user approval)
   */
  async capturePayment(orderId: string): Promise<PaymentResult> {
    try {
      const response = await this.ordersController.captureOrder({
        id: orderId,
        prefer: 'return=representation'
      });

      if (response.statusCode === 201 && response.result) {
        const capture = response.result.purchaseUnits?.[0]?.payments?.captures?.[0];
        
        if (capture && capture.status === 'COMPLETED') {
          return {
            success: true,
            paymentId: capture.id!,
            status: 'completed',
            amount: {
              amount: Math.round(parseFloat(capture.amount!.value!) * 100),
              currency: capture.amount!.currencyCode!
            },
            fees: capture.sellerReceivableBreakdown?.paypalFee ? {
              amount: Math.round(parseFloat(capture.sellerReceivableBreakdown.paypalFee.value!) * 100),
              currency: capture.sellerReceivableBreakdown.paypalFee.currencyCode!
            } : undefined,
            metadata: {
              paypalCaptureId: capture.id,
              paypalOrderId: orderId,
              captureTime: capture.createTime,
            }
          };
        }
      }

      throw new Error('Payment capture failed');

    } catch (error) {
      console.error('PayPal payment capture error:', error);
      return {
        success: false,
        paymentId: orderId,
        status: 'failed',
        amount: { amount: 0, currency: 'USD' },
        error: error instanceof Error ? error.message : 'Payment capture failed'
      };
    }
  }

  /**
   * Refund a payment
   */
  async refundPayment(paymentId: string, amount?: number): Promise<PaymentResult> {
    try {
      const refundRequest: any = {
        captureId: paymentId,
      };

      if (amount) {
        refundRequest.body = {
          amount: {
            currencyCode: this.config.currency,
            value: (amount / 100).toFixed(2)
          }
        };
      }

      const response = await this.client.payments.refund(refundRequest);

      if (response.statusCode === 201 && response.result) {
        return {
          success: true,
          paymentId: response.result.id!,
          status: 'completed',
          amount: {
            amount: Math.round(parseFloat(response.result.amount!.value!) * 100),
            currency: response.result.amount!.currencyCode!
          },
          metadata: {
            refundId: response.result.id,
            originalCaptureId: paymentId,
            refundTime: response.result.createTime,
          }
        };
      }

      throw new Error('Refund failed');

    } catch (error) {
      console.error('PayPal refund error:', error);
      return {
        success: false,
        paymentId: paymentId,
        status: 'failed',
        amount: { amount: 0, currency: 'USD' },
        error: error instanceof Error ? error.message : 'Refund failed'
      };
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(paymentId: string): Promise<PaymentResult> {
    try {
      // Try to get order details first
      const response = await this.ordersController.getOrder({
        id: paymentId
      });

      if (response.statusCode === 200 && response.result) {
        const order = response.result;
        const capture = order.purchaseUnits?.[0]?.payments?.captures?.[0];
        
        return {
          success: true,
          paymentId: paymentId,
          status: order.status === 'COMPLETED' ? 'completed' : 
                  order.status === 'APPROVED' ? 'pending' : 'failed',
          amount: {
            amount: Math.round(parseFloat(order.purchaseUnits![0].amount!.value!) * 100),
            currency: order.purchaseUnits![0].amount!.currencyCode!
          },
          metadata: {
            paypalOrderId: paymentId,
            orderStatus: order.status,
            captureId: capture?.id,
          }
        };
      }

      throw new Error('Payment not found');

    } catch (error) {
      console.error('PayPal payment status error:', error);
      return {
        success: false,
        paymentId: paymentId,
        status: 'failed',
        amount: { amount: 0, currency: 'USD' },
        error: error instanceof Error ? error.message : 'Failed to get payment status'
      };
    }
  }

  /**
   * Create a PayPal subscription
   */
  async createSubscription(request: SubscriptionRequest): Promise<SubscriptionResult> {
    try {
      // Apply coupon if provided
      let finalAmount = request.amount;

      // Apply grandfathered pricing for two-user bundle
      if (request.planId === 'two-user-bundle' || request.planId === 'two-user') {
        // Grandfathered pricing for early customers - they get $298 instead of $598
        finalAmount = { amount: 29800, currency: 'USD' }; // $298.00 grandfathered price
      }

      if (request.couponCode) {
        const coupon = await this.validateCoupon(request.couponCode, request.planId);
        if (coupon.isValid) {
          finalAmount = this.applyCoupon(finalAmount, coupon);
        }
      }

      // Create PayPal subscription plan first
      const planRequest = {
        product_id: `SPEAR_${request.planId.toUpperCase()}`,
        name: `SPEAR ${request.planId} Subscription`,
        description: `Monthly subscription for SPEAR ${request.planId} plan`,
        billing_cycles: [{
          frequency: {
            interval_unit: 'MONTH',
            interval_count: 1
          },
          tenure_type: 'REGULAR',
          sequence: 1,
          total_cycles: 0, // Infinite billing cycles
          pricing_scheme: {
            fixed_price: {
              value: (finalAmount.amount / 100).toFixed(2),
              currency_code: finalAmount.currency
            }
          }
        }],
        payment_preferences: {
          auto_bill_outstanding: true,
          setup_fee_failure_action: 'CONTINUE',
          payment_failure_threshold: 3
        },
        taxes: {
          percentage: '0',
          inclusive: false
        }
      };

      // For now, we'll use one-time payments but track them as subscriptions
      // This allows us to implement proper subscription management later
      const paymentResult = await this.processPayment({
        customer: request.customer,
        amount: finalAmount,
        planId: request.planId,
        couponCode: request.couponCode,
        billingAddress: request.billingAddress,
        metadata: {
          ...request.metadata,
          subscriptionType: 'monthly',
          planId: request.planId
        }
      });

      if (paymentResult.success) {
        return {
          success: true,
          subscriptionId: paymentResult.paymentId,
          status: 'active',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          amount: finalAmount,
          metadata: {
            ...paymentResult.metadata,
            paypalPaymentId: paymentResult.paymentId,
            planId: request.planId,
            subscriptionType: 'monthly'
          }
        };
      } else {
        return {
          success: false,
          subscriptionId: '',
          status: 'cancelled',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(),
          amount: finalAmount,
          error: paymentResult.error
        };
      }

    } catch (error) {
      console.error('PayPal subscription creation error:', error);
      return {
        success: false,
        subscriptionId: '',
        status: 'cancelled',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(),
        amount: { amount: 0, currency: 'USD' },
        error: error instanceof Error ? error.message : 'Subscription creation failed'
      };
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(subscriptionId: string): Promise<SubscriptionResult> {
    try {
      // For now, we'll mark the subscription as cancelled in our database
      // In a full implementation, this would cancel the PayPal subscription
      console.log(`Cancelling subscription: ${subscriptionId}`);

      return {
        success: true,
        subscriptionId: subscriptionId,
        status: 'cancelled',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(),
        amount: { amount: 0, currency: 'USD' },
        metadata: {
          cancelledAt: new Date().toISOString(),
          reason: 'user_requested'
        }
      };
    } catch (error) {
      console.error('PayPal subscription cancellation error:', error);
      return {
        success: false,
        subscriptionId: subscriptionId,
        status: 'active', // Keep as active if cancellation failed
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(),
        amount: { amount: 0, currency: 'USD' },
        error: error instanceof Error ? error.message : 'Subscription cancellation failed'
      };
    }
  }

  /**
   * Update a subscription
   */
  async updateSubscription(subscriptionId: string, updates: Partial<SubscriptionRequest>): Promise<SubscriptionResult> {
    try {
      console.log(`Updating subscription: ${subscriptionId}`, updates);

      // For now, return the current status
      // In a full implementation, this would update the PayPal subscription
      return {
        success: true,
        subscriptionId: subscriptionId,
        status: 'active',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        amount: { amount: 19900, currency: 'USD' }, // Default amount
        metadata: {
          updatedAt: new Date().toISOString(),
          updates: updates
        }
      };
    } catch (error) {
      console.error('PayPal subscription update error:', error);
      return {
        success: false,
        subscriptionId: subscriptionId,
        status: 'active',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(),
        amount: { amount: 0, currency: 'USD' },
        error: error instanceof Error ? error.message : 'Subscription update failed'
      };
    }
  }

  /**
   * Get subscription status
   */
  async getSubscriptionStatus(subscriptionId: string): Promise<SubscriptionResult> {
    try {
      // For now, we'll check the payment status using the PayPal order ID
      const paymentResult = await this.getPaymentStatus(subscriptionId);

      if (paymentResult.success) {
        // Determine subscription status based on payment status
        let subscriptionStatus: 'active' | 'pending' | 'cancelled' | 'past_due' | 'unpaid' = 'active';

        if (paymentResult.status === 'completed') {
          subscriptionStatus = 'active';
        } else if (paymentResult.status === 'pending') {
          subscriptionStatus = 'pending';
        } else if (paymentResult.status === 'failed') {
          subscriptionStatus = 'unpaid';
        }

        return {
          success: true,
          subscriptionId: subscriptionId,
          status: subscriptionStatus,
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          amount: paymentResult.amount,
          metadata: {
            paypalOrderId: subscriptionId,
            paymentStatus: paymentResult.status,
            lastChecked: new Date().toISOString()
          }
        };
      } else {
        return {
          success: false,
          subscriptionId: subscriptionId,
          status: 'unpaid',
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(),
          amount: { amount: 0, currency: 'USD' },
          error: paymentResult.error || 'Failed to get subscription status'
        };
      }
    } catch (error) {
      console.error('PayPal subscription status error:', error);
      return {
        success: false,
        subscriptionId: subscriptionId,
        status: 'unpaid',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(),
        amount: { amount: 0, currency: 'USD' },
        error: error instanceof Error ? error.message : 'Failed to get subscription status'
      };
    }
  }

  /**
   * Validate a coupon code
   */
  async validateCoupon(code: string, planId: string): Promise<Coupon> {
    const couponConfig = SPEAR_COUPONS[code.toUpperCase()];

    if (!couponConfig) {
      return {
        code,
        discountType: 'fixed',
        discountValue: 0,
        isValid: false
      };
    }

    // Both SPEARMINT and INSIDER2024 coupons only apply to single-user plan
    if (planId !== 'single-user') {
      return {
        code,
        discountType: 'fixed',
        discountValue: 0,
        isValid: false
      };
    }

    // Coupons are valid for single-user plan
    return {
      ...couponConfig,
      isValid: true
    };
  }

  /**
   * Apply a coupon to an amount
   */
  applyCoupon(amount: PaymentAmount, coupon: Coupon): PaymentAmount {
    if (!coupon.isValid) {
      return amount;
    }

    let discountAmount = 0;

    if (coupon.discountType === 'fixed') {
      discountAmount = coupon.discountValue;
    } else if (coupon.discountType === 'percentage') {
      discountAmount = Math.round((amount.amount * coupon.discountValue) / 100);
    }

    const finalAmount = Math.max(0, amount.amount - discountAmount);

    return {
      amount: finalAmount,
      currency: amount.currency
    };
  }

  /**
   * Get the base amount for a plan (before any discounts)
   */
  private getPlanBaseAmount(planId: string): number {
    if (planId === 'single-user') {
      return 29900; // $299.00
    } else if (planId === 'two-user-bundle' || planId === 'two-user') {
      return 59800; // $598.00 (before grandfathered pricing)
    }
    return 0;
  }

  /**
   * Verify webhook signature (placeholder for now)
   */
  async verifyWebhook(payload: string, signature: string): Promise<WebhookVerificationResult> {
    // TODO: Implement PayPal webhook verification
    // For now, we'll accept all webhooks (not secure for production)
    try {
      const event = JSON.parse(payload);
      return {
        isValid: true,
        event: {
          id: event.id || 'unknown',
          type: event.event_type || 'unknown',
          data: event,
          timestamp: new Date(event.create_time || Date.now())
        }
      };
    } catch (error) {
      return {
        isValid: false,
        error: 'Invalid webhook payload'
      };
    }
  }

  /**
   * Handle webhook events
   */
  async handleWebhookEvent(event: WebhookEvent): Promise<void> {
    console.log(`Handling PayPal webhook event: ${event.type}`, event);
    
    // TODO: Implement specific webhook event handling
    // This will integrate with the existing fulfillment workflow
    switch (event.type) {
      case 'PAYMENT.CAPTURE.COMPLETED':
        // Handle successful payment
        break;
      case 'PAYMENT.CAPTURE.DENIED':
        // Handle failed payment
        break;
      default:
        console.log(`Unhandled PayPal webhook event: ${event.type}`);
    }
  }

  /**
   * Format amount for display
   */
  formatAmount(amount: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  }

  /**
   * Parse formatted amount back to cents
   */
  parseAmount(formattedAmount: string): number {
    const numericValue = formattedAmount.replace(/[^0-9.]/g, '');
    return Math.round(parseFloat(numericValue) * 100);
  }
}
