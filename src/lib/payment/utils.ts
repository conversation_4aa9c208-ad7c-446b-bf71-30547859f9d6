/**
 * Payment Utility Functions
 * Common utilities for payment processing
 */

import { PaymentAmount, Coupon } from './types';

/**
 * Format currency amount for display
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount / 100);
}

/**
 * Parse formatted currency string back to cents
 */
export function parseCurrency(formattedAmount: string): number {
  const numericValue = formattedAmount.replace(/[^0-9.]/g, '');
  return Math.round(parseFloat(numericValue) * 100);
}

/**
 * Calculate discount amount
 */
export function calculateDiscount(amount: PaymentAmount, coupon: Coupon): number {
  if (!coupon.isValid) {
    return 0;
  }

  if (coupon.discountType === 'fixed') {
    return Math.min(coupon.discountValue, amount.amount);
  } else if (coupon.discountType === 'percentage') {
    return Math.round((amount.amount * coupon.discountValue) / 100);
  }

  return 0;
}

/**
 * Apply discount to amount
 */
export function applyDiscount(amount: PaymentAmount, coupon: Coupon): PaymentAmount {
  const discountAmount = calculateDiscount(amount, coupon);
  
  return {
    amount: Math.max(0, amount.amount - discountAmount),
    currency: amount.currency
  };
}

/**
 * Validate email address
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number (basic validation)
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

/**
 * Generate unique order ID
 */
export function generateOrderId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `SPEAR-${timestamp}-${random}`.toUpperCase();
}

/**
 * Generate unique invoice ID
 */
export function generateInvoiceId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 6);
  return `INV-${timestamp}-${random}`.toUpperCase();
}

/**
 * Validate ZIP code (US format)
 */
export function isValidZipCode(zipCode: string): boolean {
  const zipRegex = /^\d{5}(-\d{4})?$/;
  return zipRegex.test(zipCode);
}

/**
 * Format phone number for display
 */
export function formatPhoneNumber(phone: string): string {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  return phone;
}

/**
 * Sanitize billing address
 */
export function sanitizeBillingAddress(address: any) {
  return {
    fullName: (address.fullName || '').trim(),
    email: (address.email || '').trim().toLowerCase(),
    phone: (address.phone || '').trim(),
    address: (address.address || '').trim(),
    city: (address.city || '').trim(),
    state: (address.state || '').trim().toUpperCase(),
    zipCode: (address.zipCode || '').trim(),
    country: (address.country || 'US').trim().toUpperCase()
  };
}

/**
 * Validate billing address
 */
export function validateBillingAddress(address: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const sanitized = sanitizeBillingAddress(address);
  
  if (!sanitized.fullName) {
    errors.push('Full name is required');
  }
  
  if (!sanitized.email) {
    errors.push('Email address is required');
  } else if (!isValidEmail(sanitized.email)) {
    errors.push('Invalid email address format');
  }
  
  if (sanitized.phone && !isValidPhone(sanitized.phone)) {
    errors.push('Invalid phone number format');
  }
  
  if (sanitized.zipCode && !isValidZipCode(sanitized.zipCode)) {
    errors.push('Invalid ZIP code format');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Convert amount between currencies (placeholder for future implementation)
 */
export function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string
): Promise<number> {
  // For now, we only support USD
  if (fromCurrency === toCurrency) {
    return Promise.resolve(amount);
  }
  
  throw new Error(`Currency conversion from ${fromCurrency} to ${toCurrency} is not supported`);
}

/**
 * Get payment method display name
 */
export function getPaymentMethodDisplayName(provider: string): string {
  switch (provider.toLowerCase()) {
    case 'paypal':
      return 'PayPal';
    case 'stripe':
      return 'Credit Card (Stripe)';
    case 'square':
      return 'Credit Card (Square)';
    default:
      return provider;
  }
}

/**
 * Get payment status display information
 */
export function getPaymentStatusDisplay(status: string): { 
  label: string; 
  color: string; 
  description: string 
} {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'captured':
    case 'succeeded':
      return {
        label: 'Completed',
        color: 'green',
        description: 'Payment was successful'
      };
    case 'pending':
    case 'processing':
      return {
        label: 'Pending',
        color: 'yellow',
        description: 'Payment is being processed'
      };
    case 'failed':
    case 'declined':
    case 'denied':
      return {
        label: 'Failed',
        color: 'red',
        description: 'Payment was unsuccessful'
      };
    case 'cancelled':
    case 'canceled':
      return {
        label: 'Cancelled',
        color: 'gray',
        description: 'Payment was cancelled'
      };
    case 'refunded':
      return {
        label: 'Refunded',
        color: 'blue',
        description: 'Payment was refunded'
      };
    default:
      return {
        label: status,
        color: 'gray',
        description: 'Unknown payment status'
      };
  }
}
