/**
 * PayPal Configuration
 * Manages PayPal API credentials and environment settings
 */

export const paypalConfig = {
  clientId: process.env.PAYPAL_CLIENT_ID || '',
  clientSecret: process.env.PAYPAL_CLIENT_SECRET || '',
  webhookSecret: process.env.PAYPAL_WEBHOOK_SECRET || '',
  environment: (process.env.PAYPAL_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
  
  // Public configuration for frontend
  publicClientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || '',
  
  // Configuration validation
  isConfigured: () => {
    return Boolean(
      process.env.PAYPAL_CLIENT_ID &&
      process.env.PAYPAL_CLIENT_SECRET &&
      process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID
    );
  },
  
  // Get API base URL based on environment
  getApiBaseUrl: () => {
    return paypalConfig.environment === 'production'
      ? 'https://api-m.paypal.com'
      : 'https://api-m.sandbox.paypal.com';
  },
  
  // Get web SDK URL based on environment
  getWebSdkUrl: () => {
    return `https://www.paypal.com/sdk/js?client-id=${paypalConfig.publicClientId}&currency=USD&intent=capture`;
  }
};

/**
 * Validate PayPal configuration
 */
export function validatePayPalConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!paypalConfig.clientId) {
    errors.push('Missing PAYPAL_CLIENT_ID environment variable');
  }
  
  if (!paypalConfig.clientSecret) {
    errors.push('Missing PAYPAL_CLIENT_SECRET environment variable');
  }
  
  if (!paypalConfig.publicClientId) {
    errors.push('Missing NEXT_PUBLIC_PAYPAL_CLIENT_ID environment variable');
  }
  
  if (!paypalConfig.environment) {
    errors.push('Missing PAYPAL_ENVIRONMENT environment variable');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get PayPal configuration for logging (without sensitive data)
 */
export function getPayPalConfigForLogging() {
  return {
    hasClientId: !!paypalConfig.clientId,
    hasClientSecret: !!paypalConfig.clientSecret,
    hasPublicClientId: !!paypalConfig.publicClientId,
    hasWebhookSecret: !!paypalConfig.webhookSecret,
    environment: paypalConfig.environment,
    clientIdPrefix: paypalConfig.clientId ? paypalConfig.clientId.substring(0, 10) + '...' : 'missing',
    publicClientIdPrefix: paypalConfig.publicClientId ? paypalConfig.publicClientId.substring(0, 10) + '...' : 'missing',
    apiBaseUrl: paypalConfig.getApiBaseUrl(),
    isConfigured: paypalConfig.isConfigured()
  };
}
