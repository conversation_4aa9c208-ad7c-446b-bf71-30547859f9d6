/**
 * Payment Factory
 * Creates and manages payment service instances based on configuration
 */

import { PaymentService, PaymentProvider, PaymentConfig } from './types';
import { PayPalPaymentService } from './providers/paypal-service';
// Future imports:
// import { StripePaymentService } from './providers/stripe-service';
// import { SquarePaymentService } from './providers/square-service';

/**
 * Payment Factory Class
 * Implements the Factory Pattern for payment service creation
 */
export class PaymentFactory {
  private static instance: PaymentFactory;
  private services: Map<PaymentProvider, PaymentService> = new Map();
  private currentProvider: PaymentProvider;

  private constructor() {
    // Initialize with PayPal as the default provider
    this.currentProvider = 'paypal';
  }

  /**
   * Get singleton instance of PaymentFactory
   */
  public static getInstance(): PaymentFactory {
    if (!PaymentFactory.instance) {
      PaymentFactory.instance = new PaymentFactory();
    }
    return PaymentFactory.instance;
  }

  /**
   * Create a payment service instance for the specified provider
   */
  public createPaymentService(provider: PaymentProvider, config: PaymentConfig): PaymentService {
    switch (provider) {
      case 'paypal':
        return new PayPalPaymentService(config);
      
      // Future payment providers:
      // case 'stripe':
      //   return new StripePaymentService(config);
      // case 'square':
      //   return new SquarePaymentService(config);
      
      default:
        throw new Error(`Unsupported payment provider: ${provider}`);
    }
  }

  /**
   * Get or create a payment service instance
   */
  public getPaymentService(provider?: PaymentProvider): PaymentService {
    const targetProvider = provider || this.currentProvider;
    
    if (!this.services.has(targetProvider)) {
      const config = this.getPaymentConfig(targetProvider);
      const service = this.createPaymentService(targetProvider, config);
      this.services.set(targetProvider, service);
    }

    return this.services.get(targetProvider)!;
  }

  /**
   * Set the current payment provider
   */
  public setCurrentProvider(provider: PaymentProvider): void {
    this.currentProvider = provider;
  }

  /**
   * Get the current payment provider
   */
  public getCurrentProvider(): PaymentProvider {
    return this.currentProvider;
  }

  /**
   * Get available payment providers
   */
  public getAvailableProviders(): PaymentProvider[] {
    return ['paypal']; // Add more as they're implemented: 'stripe', 'square'
  }

  /**
   * Check if a payment provider is supported
   */
  public isProviderSupported(provider: PaymentProvider): boolean {
    return this.getAvailableProviders().includes(provider);
  }

  /**
   * Get payment configuration for a provider from environment variables
   */
  private getPaymentConfig(provider: PaymentProvider): PaymentConfig {
    switch (provider) {
      case 'paypal':
        return {
          provider: 'paypal',
          apiKey: process.env.PAYPAL_CLIENT_ID || '',
          secretKey: process.env.PAYPAL_CLIENT_SECRET || '',
          webhookSecret: process.env.PAYPAL_WEBHOOK_SECRET || '',
          environment: (process.env.PAYPAL_ENVIRONMENT as 'sandbox' | 'production') || 'sandbox',
          currency: 'USD'
        };

      // Future configurations:
      // case 'stripe':
      //   return {
      //     provider: 'stripe',
      //     apiKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
      //     secretKey: process.env.STRIPE_SECRET_KEY || '',
      //     webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
      //     environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
      //     currency: 'USD'
      //   };

      default:
        throw new Error(`No configuration found for payment provider: ${provider}`);
    }
  }

  /**
   * Validate payment configuration
   */
  public validateConfig(provider: PaymentProvider): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    try {
      const config = this.getPaymentConfig(provider);
      
      if (!config.apiKey) {
        errors.push(`Missing API key for ${provider}`);
      }
      
      if (!config.secretKey) {
        errors.push(`Missing secret key for ${provider}`);
      }
      
      if (!config.webhookSecret) {
        errors.push(`Missing webhook secret for ${provider}`);
      }
      
      if (!config.environment) {
        errors.push(`Missing environment configuration for ${provider}`);
      }
      
    } catch (error) {
      errors.push(`Configuration error for ${provider}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get payment service health status
   */
  public async getServiceHealth(provider?: PaymentProvider): Promise<{
    provider: PaymentProvider;
    isHealthy: boolean;
    configValid: boolean;
    errors: string[];
  }> {
    const targetProvider = provider || this.currentProvider;
    const configValidation = this.validateConfig(targetProvider);
    
    let isHealthy = false;
    const errors: string[] = [...configValidation.errors];
    
    if (configValidation.isValid) {
      try {
        const service = this.getPaymentService(targetProvider);
        // Test basic service functionality
        // This could be expanded to include actual API health checks
        isHealthy = true;
      } catch (error) {
        errors.push(`Service initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    
    return {
      provider: targetProvider,
      isHealthy,
      configValid: configValidation.isValid,
      errors
    };
  }

  /**
   * Clear cached services (useful for testing or configuration changes)
   */
  public clearCache(): void {
    this.services.clear();
  }
}

/**
 * Convenience function to get the default payment service
 */
export function getPaymentService(provider?: PaymentProvider): PaymentService {
  return PaymentFactory.getInstance().getPaymentService(provider);
}

/**
 * Convenience function to get payment factory instance
 */
export function getPaymentFactory(): PaymentFactory {
  return PaymentFactory.getInstance();
}
