import { prisma } from "@/lib/db";

/**
 * Get all clients with their subscription information
 * @returns Array of clients with their subscription information
 */
export async function getAllClientSubscriptions() {
  try {
    const clients = await prisma.user.findMany({
      where: {
        role: "CLIENT",
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
        devices: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    // Format the clients data
    return clients.map((client) => ({
      id: client.id,
      name: client.name || client.email.split("@")[0],
      service: "rustdesk", // Now using RustDesk instead of TeamViewer
      plan: client.subscriptions[0]?.plan || "basic",
      status: client.subscriptions[0]?.status || "inactive",
      expires: client.subscriptions[0]?.currentPeriodEnd.toISOString().split("T")[0] || "N/A",
      devices: client.devices.length,
    }));
  } catch (error) {
    console.error("Error getting all client subscriptions:", error);
    throw error;
  }
}

/**
 * Get a client's subscription information
 * @param clientId The ID of the client to get subscription information for
 * @returns Client subscription information
 */
export async function getClientSubscription(clientId: string) {
  try {
    const client = await prisma.user.findUnique({
      where: {
        id: clientId,
        role: "CLIENT",
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
        devices: true,
      },
    });

    if (!client) {
      throw new Error(`Client with ID ${clientId} not found`);
    }

    // Format the client data
    return {
      id: client.id,
      name: client.name || client.email.split("@")[0],
      service: "rustdesk", // Now using RustDesk instead of TeamViewer
      plan: client.subscriptions[0]?.plan || "basic",
      status: client.subscriptions[0]?.status || "inactive",
      expires: client.subscriptions[0]?.currentPeriodEnd.toISOString().split("T")[0] || "N/A",
      devices: client.devices.length,
    };
  } catch (error) {
    console.error(`Error getting subscription for client ${clientId}:`, error);
    throw error;
  }
}

/**
 * Update a client's subscription
 * @param clientId The ID of the client to update
 * @param subscriptionData The subscription data to update
 * @returns Updated client subscription
 */
export async function updateClientSubscription(
  clientId: string,
  subscriptionData: {
    plan?: string;
    status?: string;
    currentPeriodEnd?: Date;
  }
) {
  try {
    const client = await prisma.user.findUnique({
      where: {
        id: clientId,
        role: "CLIENT",
      },
      include: {
        subscriptions: {
          orderBy: {
            currentPeriodEnd: "desc",
          },
          take: 1,
        },
      },
    });

    if (!client) {
      throw new Error(`Client with ID ${clientId} not found`);
    }

    let subscription;

    if (client.subscriptions.length === 0) {
      // Create a new subscription if the client doesn't have one
      subscription = await prisma.subscription.create({
        data: {
          userId: clientId,
          plan: subscriptionData.plan || "basic",
          status: subscriptionData.status || "active",
          currentPeriodEnd: subscriptionData.currentPeriodEnd || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          price: subscriptionData.plan === "enterprise" ? "$650.00" : subscriptionData.plan === "professional" ? "$350.00" : "$150.00",
        },
      });
    } else {
      // Update the existing subscription
      subscription = await prisma.subscription.update({
        where: {
          id: client.subscriptions[0].id,
        },
        data: {
          plan: subscriptionData.plan,
          status: subscriptionData.status,
          currentPeriodEnd: subscriptionData.currentPeriodEnd,
          price: subscriptionData.plan === "enterprise" ? "$650.00" : subscriptionData.plan === "professional" ? "$350.00" : "$150.00",
        },
      });
    }

    return {
      id: client.id,
      name: client.name || client.email.split("@")[0],
      service: "rustdesk", // Now using RustDesk instead of TeamViewer
      plan: subscription.plan,
      status: subscription.status,
      expires: subscription.currentPeriodEnd.toISOString().split("T")[0],
      devices: await prisma.device.count({
        where: {
          userId: clientId,
        },
      }),
    };
  } catch (error) {
    console.error(`Error updating subscription for client ${clientId}:`, error);
    throw error;
  }
}
