import NextAuth from "next-auth";
import Credentials from "next-auth/providers/credentials";
import { prisma } from "@/lib/db";
import { hashPassword, verifyPassword } from "@/lib/auth-utils";

export const authConfig = {
  pages: {
    signIn: "/login",
    signOut: "/",
  },
  // Add CSRF protection configuration
  secret: process.env.NEXTAUTH_SECRET || "your-secret-key-for-development-only",
  useSecureCookies: process.env.NODE_ENV === "production",
  // Trust the production domain
  trustHost: true,
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
  providers: [
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            console.log("Auth: Missing email or password");
            return null;
          }

          // EMERGENCY ADMIN HARDCODED ACCESS
          // This ensures you can ALWAYS get into your admin account
          if (credentials.email === '<EMAIL>' &&
              credentials.password === 'SpearAdmin2024!') {
            console.log("Auth: EMERGENCY ADMIN ACCESS GRANTED");
            return {
              id: 'emergency-admin',
              name: 'Marquise Holton',
              email: '<EMAIL>',
              role: 'ADMIN',
            };
          }

          // Try normal database authentication
          try {
            // Find the user in the database
            const user = await prisma.user.findUnique({
              where: {
                email: credentials.email,
              },
            });

            // If user not found, return null
            if (!user || !user.password) {
              console.log("Auth: User not found or no password set");
              return null;
            }

            // Verify the password
            const isValidPassword = await verifyPassword(credentials.password, user.password);

            if (isValidPassword) {
              console.log("Auth: Authentication successful for user:", user.email);
              return {
                id: user.id,
                name: user.name || "",
                email: user.email,
                role: user.role,
              };
            }

            console.log("Auth: Invalid password for user:", user.email);
            return null;
          } catch (dbError) {
            console.error("Database authentication error:", dbError);

            // If database fails and this is the admin, allow emergency access
            if (credentials.email === '<EMAIL>' &&
                credentials.password === 'SpearAdmin2024!') {
              console.log("Auth: DATABASE FAILED - EMERGENCY ADMIN ACCESS GRANTED");
              return {
                id: 'emergency-admin-db-fail',
                name: 'Marquise Holton',
                email: '<EMAIL>',
                role: 'ADMIN',
              };
            }

            return null;
          }
        } catch (error) {
          console.error("Error in authorize function:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.role = token.role as string;
        session.user.id = token.id as string;
      }
      return session;
    },
  },
};

export const { handlers, auth, signIn, signOut } = NextAuth(authConfig);
