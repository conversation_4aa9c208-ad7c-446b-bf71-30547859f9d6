import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { auth } from '@/lib/auth';

export async function middleware(request: NextRequest) {
  // Protect the viewer endpoint with basic session check
  if (request.nextUrl.pathname.startsWith('/viewer')) {
    return await protectViewer(request);
  }

  // Protect client device routes
  if (request.nextUrl.pathname.startsWith('/client')) {
    return await protectClient(request);
  }
  // Bypass authentication for direct access routes
  if (request.nextUrl.pathname.startsWith('/direct-admin') ||
      request.nextUrl.pathname.startsWith('/direct-remote') ||
      request.nextUrl.pathname.startsWith('/bypass') ||
      request.nextUrl.pathname.startsWith('/web-remote') ||
      request.nextUrl.pathname.startsWith('/auto-remote') ||
      request.nextUrl.pathname.startsWith('/auto-click') ||
      request.nextUrl.pathname.startsWith('/direct-connect') ||
      request.nextUrl.pathname.startsWith('/connect-window') ||
      request.nextUrl.pathname.startsWith('/api/remote-connect') ||
      request.nextUrl.pathname.startsWith('/spear-remote.html') ||
      request.nextUrl.pathname.startsWith('/spear-auto-connect.html') ||
      request.nextUrl.pathname.startsWith('/direct-connect.html') ||
      request.nextUrl.pathname.startsWith('/connect-window.html') ||
      request.nextUrl.pathname.startsWith('/auto-click.html') ||
      request.nextUrl.pathname.startsWith('/register')) {
    return NextResponse.next();
  }

  // Bypass authentication for the admin route when coming from direct access routes
  if (request.nextUrl.pathname.startsWith('/admin') &&
      (request.headers.get('referer')?.includes('/direct-admin') ||
       request.headers.get('referer')?.includes('/direct-remote'))) {
    return NextResponse.next();
  }

  // For all other routes, use the default authentication
  const session = await auth();

  // If the user is not logged in and trying to access a protected route
  if (!session &&
      (request.nextUrl.pathname.startsWith('/admin') ||
       request.nextUrl.pathname.startsWith('/dashboard'))) {

    // Redirect to the login page
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // If the user is logged in but trying to access a route they don't have permission for
  if (session && session.user && session.user.role &&
      ((session.user.role !== 'ADMIN' && request.nextUrl.pathname.startsWith('/admin')) ||
       (session.user.role !== 'CLIENT' && request.nextUrl.pathname.startsWith('/dashboard')))) {

    // Redirect to the appropriate dashboard based on role
    if (session.user.role === 'ADMIN') {
      return NextResponse.redirect(new URL('/admin', request.url));
    } else if (session.user.role === 'CLIENT') {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }

  return NextResponse.next();
}

async function protectViewer(request: NextRequest) {
  try {
    // Basic session check - detailed JWT verification will be done in the viewer page
    const session = await auth();

    if (!session?.user?.email) {
      return redirectToLogin(request);
    }

    // Check for required parameters
    const url = new URL(request.url);
    const deviceToken = url.searchParams.get('token');
    const deviceId = url.searchParams.get('device');

    if (!deviceToken || !deviceId) {
      return new NextResponse('Access denied: Invalid parameters', { status: 403 });
    }

    // Allow access - detailed verification will happen in the viewer page
    return NextResponse.next();

  } catch (error) {
    console.error('Viewer protection error:', error);
    return new NextResponse('Access denied', { status: 403 });
  }
}

async function protectClient(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.email) {
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // All authenticated users can access client routes
    return NextResponse.next();

  } catch (error) {
    console.error('Client protection error:', error);
    return NextResponse.redirect(new URL('/login', request.url));
  }
}

function redirectToLogin(request: NextRequest) {
  const loginUrl = new URL('/login', request.url);
  loginUrl.searchParams.set('callbackUrl', request.url);
  return NextResponse.redirect(loginUrl);
}
