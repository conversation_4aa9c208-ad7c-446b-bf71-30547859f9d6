package com.spear.devicemanager

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.os.Handler
import android.os.Looper
import android.util.Log
import kotlinx.coroutines.*
import okhttp3.*
import org.json.JSONObject
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * SPEAR Device Manager Service
 * 
 * This service runs in the background and:
 * 1. Checks subscription status every 5 minutes
 * 2. Disables RustDesk if subscription is inactive
 * 3. Manages device enrollment and compliance
 * 4. Implements Samsung Knox security policies
 */
class SpearDeviceManagerService : Service() {
    
    companion object {
        private const val TAG = "SpearDeviceManager"
        private const val CHECK_INTERVAL = 5 * 60 * 1000L // 5 minutes
        private const val SPEAR_API_BASE = "https://spear-production.vercel.app/api"
        private const val RUSTDESK_PACKAGE = "com.carriez.flutter_hbb"
    }
    
    private val handler = Handler(Looper.getMainLooper())
    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()
    
    private var deviceId: String? = null
    private var isRunning = false
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "SPEAR Device Manager Service created")
        
        // Get device ID from shared preferences or generate
        deviceId = getDeviceId()
        
        startSubscriptionMonitoring()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "Service started")
        return START_STICKY // Restart if killed
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        isRunning = false
        Log.d(TAG, "Service destroyed")
    }
    
    private fun startSubscriptionMonitoring() {
        isRunning = true
        checkSubscriptionStatus()
    }
    
    private fun checkSubscriptionStatus() {
        if (!isRunning) return
        
        Log.d(TAG, "Checking subscription status for device: $deviceId")
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val isActive = checkSubscriptionWithServer()
                
                withContext(Dispatchers.Main) {
                    handleSubscriptionStatus(isActive)
                    
                    // Schedule next check
                    handler.postDelayed({
                        checkSubscriptionStatus()
                    }, CHECK_INTERVAL)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error checking subscription", e)
                
                // Retry in 1 minute on error
                handler.postDelayed({
                    checkSubscriptionStatus()
                }, 60 * 1000L)
            }
        }
    }
    
    private suspend fun checkSubscriptionWithServer(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val request = Request.Builder()
                    .url("$SPEAR_API_BASE/device/subscription-status")
                    .addHeader("X-Device-ID", deviceId ?: "")
                    .addHeader("X-Device-Type", "samsung-a14")
                    .addHeader("User-Agent", "SPEAR-Device-Manager/1.0")
                    .build()
                
                val response = httpClient.newCall(request).execute()
                
                if (response.isSuccessful) {
                    val responseBody = response.body?.string()
                    val json = JSONObject(responseBody ?: "{}")
                    
                    val isActive = json.optBoolean("isActive", false)
                    val message = json.optString("message", "Unknown status")
                    
                    Log.d(TAG, "Subscription status: $isActive - $message")
                    return@withContext isActive
                    
                } else {
                    Log.w(TAG, "Server returned ${response.code}: ${response.message}")
                    
                    // If server returns 402 (Payment Required), subscription is definitely inactive
                    if (response.code == 402) {
                        return@withContext false
                    }
                    
                    // For other errors, assume active to avoid false positives
                    return@withContext true
                }
                
            } catch (e: IOException) {
                Log.e(TAG, "Network error checking subscription", e)
                // On network error, assume active to avoid disrupting service
                return@withContext true
            }
        }
    }
    
    private fun handleSubscriptionStatus(isActive: Boolean) {
        if (isActive) {
            enableRustDeskAccess()
        } else {
            disableRustDeskAccess()
        }
    }
    
    private fun enableRustDeskAccess() {
        try {
            Log.d(TAG, "✅ Subscription active - ensuring RustDesk is enabled")
            
            // Enable RustDesk app if it was disabled
            val packageManager = packageManager
            val componentName = android.content.ComponentName(
                RUSTDESK_PACKAGE,
                "$RUSTDESK_PACKAGE.MainActivity"
            )
            
            packageManager.setComponentEnabledSetting(
                componentName,
                android.content.pm.PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                android.content.pm.PackageManager.DONT_KILL_APP
            )
            
            // Remove any Knox restrictions
            removeKnoxRestrictions()
            
        } catch (e: Exception) {
            Log.e(TAG, "Error enabling RustDesk access", e)
        }
    }
    
    private fun disableRustDeskAccess() {
        try {
            Log.w(TAG, "❌ Subscription inactive - disabling RustDesk access")
            
            // Method 1: Disable the app component
            val packageManager = packageManager
            val componentName = android.content.ComponentName(
                RUSTDESK_PACKAGE,
                "$RUSTDESK_PACKAGE.MainActivity"
            )
            
            packageManager.setComponentEnabledSetting(
                componentName,
                android.content.pm.PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                android.content.pm.PackageManager.DONT_KILL_APP
            )
            
            // Method 2: Apply Knox restrictions
            applyKnoxRestrictions()
            
            // Method 3: Kill any running RustDesk processes
            killRustDeskProcesses()
            
            Log.w(TAG, "🔒 RustDesk access disabled due to inactive subscription")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error disabling RustDesk access", e)
        }
    }
    
    private fun applyKnoxRestrictions() {
        try {
            // Apply Samsung Knox policies to restrict RustDesk
            // This requires Knox SDK and appropriate permissions
            
            Log.d(TAG, "Applying Knox restrictions to RustDesk")
            
            // Example Knox restrictions:
            // - Hide app from launcher
            // - Prevent app from running
            // - Block network access for the app
            // - Add to blacklist
            
            // Note: Actual Knox implementation would go here
            // This is a placeholder for the Knox SDK calls
            
        } catch (e: Exception) {
            Log.e(TAG, "Error applying Knox restrictions", e)
        }
    }
    
    private fun removeKnoxRestrictions() {
        try {
            Log.d(TAG, "Removing Knox restrictions from RustDesk")
            
            // Remove Knox policies that were restricting RustDesk
            // Note: Actual Knox implementation would go here
            
        } catch (e: Exception) {
            Log.e(TAG, "Error removing Knox restrictions", e)
        }
    }
    
    private fun killRustDeskProcesses() {
        try {
            // Force stop RustDesk if it's running
            val activityManager = getSystemService(ACTIVITY_SERVICE) as android.app.ActivityManager
            
            // This requires KILL_BACKGROUND_PROCESSES permission
            activityManager.killBackgroundProcesses(RUSTDESK_PACKAGE)
            
            Log.d(TAG, "Killed RustDesk background processes")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error killing RustDesk processes", e)
        }
    }
    
    private fun getDeviceId(): String {
        // Get device ID from shared preferences or generate new one
        val prefs = getSharedPreferences("spear_device", MODE_PRIVATE)
        var deviceId = prefs.getString("device_id", null)
        
        if (deviceId == null) {
            // Generate new device ID
            deviceId = "spear_${System.currentTimeMillis()}_${(1000..9999).random()}"
            prefs.edit().putString("device_id", deviceId).apply()
        }
        
        return deviceId
    }
}

/**
 * Device Manager Configuration
 */
data class SpearDeviceConfig(
    val deviceId: String,
    val serverUrl: String,
    val checkInterval: Long,
    val knoxEnabled: Boolean,
    val strictMode: Boolean
)

/**
 * Subscription Status Response
 */
data class SubscriptionStatus(
    val isActive: Boolean,
    val planType: String?,
    val expiresAt: String?,
    val message: String
)
