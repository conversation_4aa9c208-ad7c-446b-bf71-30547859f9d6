# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SPEAR (Secure Platform for Extended Augmented Reality) is a production enterprise remote device management platform processing real customer payments. Built with Next.js 15, TypeScript, and PostgreSQL, it provides secure remote access to devices (particularly Samsung Galaxy A14) through RustDesk integration, with monthly subscriptions ($199-$299/month) via PayPal.

## Key Commands

### Development
```bash
pnpm dev                    # Start development server (port 3000)
pnpm build                  # Build for production (includes Prisma generate)
pnpm start                  # Start production server
pnpm lint                   # Run ESLint
```

### Database Management
```bash
pnpm db:seed-js            # Seed database with initial data
prisma generate            # Generate Prisma client
prisma db push             # Push schema changes to database
prisma studio              # Open Prisma Studio GUI
```

### Deployment & Environment
```bash
pnpm generate:secret       # Generate NextAuth secret
pnpm validate-env          # Validate environment variables
pnpm generate-sitemap      # Generate sitemap.xml
```

## Architecture Overview

### Tech Stack
- **Frontend**: Next.js 14 with App Router, Type<PERSON>, Tailwind CSS
- **Backend**: Next.js API Routes with server actions
- **Database**: PostgreSQL (Railway) with Prisma ORM
- **Payments**: PayPal SDK (production environment)
- **Auth**: NextAuth.js with database sessions (not JWT)
- **Deployment**: Vercel (spear-global.com)

### Critical Business Logic

#### Payment Flow
1. User selects plan → PayPal checkout → Payment capture
2. PayPal webhook → Order creation in database
3. Subscription activation → Device access granted
4. Monthly billing cycle with 7-day grace period
5. Non-payment → Automatic device access revocation

#### Admin System
- Single admin: `<EMAIL>`
- Admin dashboard: `/admin/subscription-monitor`
- Test payment system: $0.01-$10.00 for safe testing
- Real-time PayPal subscription verification

### Database Schema Core

```prisma
User (role: ADMIN/CLIENT)
├── Subscription (paypalOrderId, status, currentPeriodEnd)
├── Order (paypalCaptureId, amount, paymentStatus)
├── Device (rustDeskId, status: active/inactive)
└── ShippingAddress (fulfillment tracking)
```

### API Structure

```
/api/paypal/
  ├── create-payment/     # Initialize PayPal payment
  ├── capture-payment/    # Capture approved payment
  └── webhook/           # Process PayPal webhooks

/api/admin/
  ├── subscriptions/     # Manage user subscriptions
  └── orders/           # Order management

/api/test-payment/      # Admin test payment system
```

## Critical Security & Business Rules

1. **Payment Processing**: All payments go through PayPal production API
2. **Subscription Validation**: Server-side only, never trust client
3. **Device Access**: Tied directly to payment status
4. **Admin Access**: Restricted by email, not role alone
5. **Webhook Security**: PayPal signature verification required
6. **Test Payments**: Limited to $0.01-$10.00 for admin only

## Common Development Tasks

### Testing Payment Flow
1. Use admin account to access `/admin/subscription-monitor`
2. Create test payment ($0.01-$10.00)
3. Monitor webhook processing in logs
4. Verify subscription status update

### Adding New Features
1. Check existing patterns in `src/app/` for similar functionality
2. Use server actions for data mutations
3. Implement proper error boundaries
4. Add to admin dashboard if business-critical

### Debugging Subscriptions
1. Check `/api/admin/subscriptions` for user status
2. Verify PayPal webhook logs
3. Review `Order` table for payment history
4. Check `currentPeriodEnd` for billing cycle

## Environment Variables Required

```env
# Database
DATABASE_URL=             # PostgreSQL connection string
DIRECT_URL=              # Direct database connection

# Authentication
NEXTAUTH_SECRET=         # Generated secret for sessions
NEXTAUTH_URL=           # Application URL

# PayPal (Production)
PAYPAL_CLIENT_ID=       # PayPal app client ID
PAYPAL_CLIENT_SECRET=   # PayPal app secret
PAYPAL_WEBHOOK_ID=      # PayPal webhook ID
PAYPAL_ENVIRONMENT=     # "production" or "sandbox"

# RustDesk Integration
RUSTDESK_SERVER_IP=     # RustDesk server IP
RUSTDESK_API_KEY=       # API authentication
```

## Production Considerations

1. **Real Money**: This system processes real customer payments
2. **Subscription Management**: Automatic billing and access control
3. **Customer Impact**: Changes affect paying customers immediately
4. **Admin Dashboard**: Primary tool for customer support
5. **Webhook Reliability**: Critical for payment processing

## File Structure Patterns

```
src/app/
  ├── (auth)/           # Authentication pages
  ├── admin/            # Admin-only pages
  ├── api/              # API routes
  └── [feature]/        # Feature pages

src/components/
  ├── admin/            # Admin-specific components
  ├── payment/          # Payment UI components
  └── ui/               # Reusable UI components

src/lib/
  ├── payment/          # Payment service layer
  ├── auth.ts           # Authentication config
  └── prisma.ts         # Database client
```

## Detailed Architecture Analysis

### Core Business Components

#### Device Management System
- **Device Lifecycle**: Complete management from procurement → shipping → customer activation
- **RustDesk Integration**: Web, desktop, mobile client support with secure connections
- **Status Tracking**: "pending", "active", "assigned", "shipped", "delivered"
- **Device Types**: Desktop and mobile (Samsung Galaxy A14 focus)
- **Assignment System**: Device-to-user mapping with tracking

#### Payment Processing Architecture
- **Factory Pattern**: PaymentFactory with multi-provider support
- **Primary Provider**: PayPal SDK (production environment)
- **Backup Providers**: Stripe, Square (configured but not primary)
- **Coupon System**: SPEARMINT ($100 off), INSIDER2024 ($289 off)
- **Subscription Plans**:
  - Single User: $299/month → $199/month with SPEARMINT
  - Two User Bundle: $598/month → $298/month (grandfathered)

#### Authentication & Security
- **Emergency Admin Access**: Hardcoded for `<EMAIL>`
- **Multi-layer Security**: Role-based access, emergency bypasses, webhook verification
- **NextAuth v5**: Database sessions with credentials provider
- **Password Security**: bcrypt hashing with secure utilities
- **Access Control**: Middleware-enforced route protection

### Database Schema (Prisma)

#### Core Models
```prisma
User (role: ADMIN/CLIENT)
├── Device (rustDeskId, status, deviceType)
├── Subscription (paypalOrderId, status, currentPeriodEnd)
├── Order (paypalCaptureId, amount, paymentStatus, fulfillmentStatus)
├── PaymentMethod (type, brand, last4)
├── SupportTicket (status, priority)
└── ShippingAddress (fulfillment tracking)
```

#### Business Logic Models
- **BlogPost/Comment**: Content management system
- **Coupon**: Discount code management
- **Invoice**: Billing and accounting
- **Notification**: Real-time admin notifications
- **DeviceSubmission**: Device onboarding requests
- **TradeInRequest**: Device exchange program

### API Architecture

#### Critical Endpoints
```
/api/auth/[...nextauth]/     # NextAuth authentication
/api/paypal/
  ├── create-payment/        # Initialize PayPal payment
  ├── capture-payment/       # Capture approved payment  
  └── webhook/              # Process PayPal webhooks

/api/admin/
  ├── subscriptions/        # Subscription management
  ├── orders/[id]/          # Order fulfillment
  ├── devices/             # Device inventory
  └── analytics/           # Revenue tracking

/api/client/
  ├── devices/             # Customer device access
  ├── subscription/        # Plan management
  └── remote-connect/      # RustDesk connections

/api/orders/[id]/
  └── status-stream/       # Real-time order updates
```

### Admin Dashboard Features

#### Comprehensive Management Interface
- **Subscription Monitor** (`/admin/subscription-monitor`): Real-time customer status
- **Device Management**: Bulk operations, inventory tracking, RustDesk console
- **Order Fulfillment**: Complete workflow from payment to delivery
- **Analytics Dashboard**: Revenue tracking, payment processing metrics
- **Support System**: Customer ticket management
- **Content Management**: Blog and knowledge base administration

### Security Considerations

#### Production Security Measures
1. **Payment Security**: PCI compliance via PayPal, webhook signature verification
2. **Data Protection**: Parameterized queries, secure environment variables
3. **Access Control**: Role-based middleware, emergency admin recovery
4. **Session Management**: Secure cookies, CSRF protection
5. **Business Protection**: Server-side subscription validation, automatic access revocation

### Development Environment

#### Key Configuration Files
- **Environment**: `.env.production` (PayPal, database, auth credentials)
- **Database**: `prisma/schema.prisma` (20+ models with relationships)
- **Deployment**: `railway.json`, `vercel.json`, multiple Docker configs
- **Next.js**: `next.config.js` with Prisma integration

#### Important Scripts Location
- **Database**: `scripts/seed-database.js`, `scripts/migrate-passwords.ts`
- **Admin Tools**: `emergency-admin-access.js`, `fix-admin-*.js`
- **Environment**: `scripts/validate-env.js`, `setup-paypal-env.sh`
- **Deployment**: `deploy-railway.sh`, `scripts/setup-vercel-env.js`

### Production Considerations

#### Critical Business Rules
1. **Real Money Processing**: Live PayPal production API with customer payments
2. **Device Fulfillment**: Physical device shipping and tracking
3. **Customer Impact**: All changes affect paying subscribers immediately
4. **Emergency Access**: Admin recovery mechanisms for system continuity
5. **Subscription Lifecycle**: 30-day billing with 7-day grace period
6. **Access Revocation**: Automatic device access termination for non-payment

This is a mature production system handling enterprise remote device management with comprehensive security, payment processing, and customer lifecycle management.