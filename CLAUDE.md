# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SPEAR is a production subscription-based remote access management platform processing real customer payments. Built with Next.js 14, TypeScript, and PostgreSQL, it manages monthly subscriptions ($199-$299/month) through PayPal integration.

## Key Commands

### Development
```bash
pnpm dev                    # Start development server (port 3000)
pnpm build                  # Build for production (includes Prisma generate)
pnpm start                  # Start production server
pnpm lint                   # Run ESLint
```

### Database Management
```bash
pnpm db:seed-js            # Seed database with initial data
prisma generate            # Generate Prisma client
prisma db push             # Push schema changes to database
prisma studio              # Open Prisma Studio GUI
```

### Deployment & Environment
```bash
pnpm generate:secret       # Generate NextAuth secret
pnpm validate-env          # Validate environment variables
pnpm generate-sitemap      # Generate sitemap.xml
```

## Architecture Overview

### Tech Stack
- **Frontend**: Next.js 14 with App Router, <PERSON><PERSON>, Tailwind CSS
- **Backend**: Next.js API Routes with server actions
- **Database**: PostgreSQL (Railway) with Prisma ORM
- **Payments**: PayPal SDK (production environment)
- **Auth**: NextAuth.js with database sessions (not JWT)
- **Deployment**: Vercel (spear-global.com)

### Critical Business Logic

#### Payment Flow
1. User selects plan → PayPal checkout → Payment capture
2. PayPal webhook → Order creation in database
3. Subscription activation → Device access granted
4. Monthly billing cycle with 7-day grace period
5. Non-payment → Automatic device access revocation

#### Admin System
- Single admin: `<EMAIL>`
- Admin dashboard: `/admin/subscription-monitor`
- Test payment system: $0.01-$10.00 for safe testing
- Real-time PayPal subscription verification

### Database Schema Core

```prisma
User (role: ADMIN/CLIENT)
├── Subscription (paypalOrderId, status, currentPeriodEnd)
├── Order (paypalCaptureId, amount, paymentStatus)
├── Device (rustDeskId, status: active/inactive)
└── ShippingAddress (fulfillment tracking)
```

### API Structure

```
/api/paypal/
  ├── create-payment/     # Initialize PayPal payment
  ├── capture-payment/    # Capture approved payment
  └── webhook/           # Process PayPal webhooks

/api/admin/
  ├── subscriptions/     # Manage user subscriptions
  └── orders/           # Order management

/api/test-payment/      # Admin test payment system
```

## Critical Security & Business Rules

1. **Payment Processing**: All payments go through PayPal production API
2. **Subscription Validation**: Server-side only, never trust client
3. **Device Access**: Tied directly to payment status
4. **Admin Access**: Restricted by email, not role alone
5. **Webhook Security**: PayPal signature verification required
6. **Test Payments**: Limited to $0.01-$10.00 for admin only

## Common Development Tasks

### Testing Payment Flow
1. Use admin account to access `/admin/subscription-monitor`
2. Create test payment ($0.01-$10.00)
3. Monitor webhook processing in logs
4. Verify subscription status update

### Adding New Features
1. Check existing patterns in `src/app/` for similar functionality
2. Use server actions for data mutations
3. Implement proper error boundaries
4. Add to admin dashboard if business-critical

### Debugging Subscriptions
1. Check `/api/admin/subscriptions` for user status
2. Verify PayPal webhook logs
3. Review `Order` table for payment history
4. Check `currentPeriodEnd` for billing cycle

## Environment Variables Required

```env
# Database
DATABASE_URL=             # PostgreSQL connection string
DIRECT_URL=              # Direct database connection

# Authentication
NEXTAUTH_SECRET=         # Generated secret for sessions
NEXTAUTH_URL=           # Application URL

# PayPal (Production)
PAYPAL_CLIENT_ID=       # PayPal app client ID
PAYPAL_CLIENT_SECRET=   # PayPal app secret
PAYPAL_WEBHOOK_ID=      # PayPal webhook ID
PAYPAL_ENVIRONMENT=     # "production" or "sandbox"

# RustDesk Integration
RUSTDESK_SERVER_IP=     # RustDesk server IP
RUSTDESK_API_KEY=       # API authentication
```

## Production Considerations

1. **Real Money**: This system processes real customer payments
2. **Subscription Management**: Automatic billing and access control
3. **Customer Impact**: Changes affect paying customers immediately
4. **Admin Dashboard**: Primary tool for customer support
5. **Webhook Reliability**: Critical for payment processing

## File Structure Patterns

```
src/app/
  ├── (auth)/           # Authentication pages
  ├── admin/            # Admin-only pages
  ├── api/              # API routes
  └── [feature]/        # Feature pages

src/components/
  ├── admin/            # Admin-specific components
  ├── payment/          # Payment UI components
  └── ui/               # Reusable UI components

src/lib/
  ├── payment/          # Payment service layer
  ├── auth.ts           # Authentication config
  └── prisma.ts         # Database client
```