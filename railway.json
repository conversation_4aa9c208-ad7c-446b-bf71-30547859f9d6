{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm ci --omit=dev && prisma generate && next build"}, "deploy": {"startCommand": "next start", "healthcheckPath": "/api/test-db", "healthcheckTimeout": 300, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}, "variables": {"NEXT_PUBLIC_RUSTDESK_SERVER_IP": "**************", "NEXT_PUBLIC_RUSTDESK_SERVER_PORT": "21116", "NEXT_PUBLIC_RUSTDESK_RELAY_PORT": "21117", "NEXT_PUBLIC_RUSTDESK_API_PORT": "21114", "NEXT_PUBLIC_RUSTDESK_SERVER_KEY": "61PhlnkYifqNNk1NgHvgKeCfrsILb8TULTiRtk8ZXho=", "RUSTDESK_SERVER_IP": "**************", "RUSTDESK_SERVER_PORT": "21116", "RUSTDESK_RELAY_PORT": "21117", "RUSTDESK_API_PORT": "21114", "RUSTDESK_SERVER_KEY": "61PhlnkYifqNNk1NgHvgKeCfrsILb8TULTiRtk8ZXho=", "RUSTDESK_API_TOKEN": "spear-admin-token-2024", "RUSTDESK_ANDROID_DEVICE_ID": "**********", "RUSTDESK_ANDROID_PASSWORD": "82AirmaN@$", "DATABASE_URL": "postgresql://postgres:<EMAIL>:31227/railway?schema=public", "DIRECT_URL": "postgresql://postgres:<EMAIL>:31227/railway?schema=public", "NEXTAUTH_URL": "https://spear-global.com", "NEXTAUTH_SECRET": "spear-production-secret-key-2025-railway-deployment", "NODE_ENV": "production", "PAYPAL_CLIENT_ID": "YOUR_PAYPAL_CLIENT_ID", "PAYPAL_CLIENT_SECRET": "YOUR_PAYPAL_CLIENT_SECRET", "PAYPAL_WEBHOOK_SECRET": "YOUR_PAYPAL_WEBHOOK_SECRET", "PAYPAL_ENVIRONMENT": "sandbox", "NEXT_PUBLIC_PAYPAL_CLIENT_ID": "YOUR_PAYPAL_CLIENT_ID"}}