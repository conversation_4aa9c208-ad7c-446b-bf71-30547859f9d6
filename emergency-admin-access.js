const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

// Emergency admin access script
// This will ensure you can always log in as admin

const prisma = new PrismaClient({
  datasourceUrl: process.env.DATABASE_URL
});

async function createEmergencyAdminAccess() {
  try {
    console.log('🚨 EMERGENCY ADMIN ACCESS SETUP 🚨');
    console.log('Setting up hardcoded admin access...');
    
    const adminEmail = '<EMAIL>';
    const adminPassword = 'SpearAdmin2024!'; // Strong password for production
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(adminPassword, 12);
    
    // Check if admin exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: adminEmail }
    });
    
    if (existingAdmin) {
      // Update existing admin
      const updatedAdmin = await prisma.user.update({
        where: { email: adminEmail },
        data: {
          password: hashedPassword,
          role: 'ADMIN',
          name: '<PERSON><PERSON>'
        }
      });
      
      console.log('✅ ADMIN ACCESS UPDATED');
      console.log('📧 Email:', adminEmail);
      console.log('🔑 Password:', adminPassword);
      console.log('🎭 Role:', updatedAdmin.role);
      console.log('👤 Name:', updatedAdmin.name);
    } else {
      // Create new admin
      const newAdmin = await prisma.user.create({
        data: {
          email: adminEmail,
          password: hashedPassword,
          role: 'ADMIN',
          name: 'Marquise Holton'
        }
      });
      
      console.log('✅ ADMIN ACCESS CREATED');
      console.log('📧 Email:', adminEmail);
      console.log('🔑 Password:', adminPassword);
      console.log('🎭 Role:', newAdmin.role);
      console.log('👤 Name:', newAdmin.name);
    }
    
    // Test the password immediately
    const testAdmin = await prisma.user.findUnique({
      where: { email: adminEmail }
    });
    
    if (testAdmin && testAdmin.password) {
      const isValidPassword = await bcrypt.compare(adminPassword, testAdmin.password);
      console.log('🧪 Password Test:', isValidPassword ? '✅ VALID' : '❌ INVALID');
    }
    
    console.log('\n🌐 LOGIN URLS:');
    console.log('Local: http://localhost:3000/login');
    console.log('Production: https://your-vercel-domain.vercel.app/login');
    
    console.log('\n🔐 EMERGENCY ACCESS CREDENTIALS:');
    console.log('Email:', adminEmail);
    console.log('Password:', adminPassword);
    
  } catch (error) {
    console.error('❌ Error setting up emergency admin access:', error);
    
    // If database connection fails, show the issue
    if (error.message.includes('connect')) {
      console.log('\n🔧 DATABASE CONNECTION ISSUE DETECTED');
      console.log('Your DATABASE_URL might be incorrect.');
      console.log('Current DATABASE_URL:', process.env.DATABASE_URL);
      console.log('\nPlease check your .env.local file and update the DATABASE_URL with the correct Supabase password.');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the emergency setup
createEmergencyAdminAccess();
