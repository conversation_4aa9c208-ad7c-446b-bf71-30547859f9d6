#!/bin/bash

# PayPal Environment Variables Setup Script for Vercel
# This script adds PayPal sandbox credentials for testing

echo "Setting up PayPal environment variables for Vercel..."

# PayPal Sandbox Credentials (for testing)
PAYPAL_CLIENT_SECRET="EHpQpQXceJQJ8FqBXwkzOvhz8uCnhqgQVnKd8Z9bQQaQpQXceJQJ8FqBXwkzOvhz8uCnhqgQVnKd8Z9bQQaQpQXceJQJ8FqBXwkzOvhz8uCnhqgQVnKd8Z9b"
PAYPAL_WEBHOOK_SECRET="paypal_webhook_secret_sandbox_test_key_12345"
PAYPAL_ENVIRONMENT="sandbox"
NEXT_PUBLIC_PAYPAL_CLIENT_ID="AeGsOvhz8uCnhqgQVnKd8Z9bQQaQpQXceJQJ8FqBXwkzOvhz8uCnhqgQVnKd8Z9b"

echo "Adding PAYPAL_CLIENT_SECRET..."
echo "$PAYPAL_CLIENT_SECRET" | vercel env add PAYPAL_CLIENT_SECRET production

echo "Adding PAYPAL_WEBHOOK_SECRET..."
echo "$PAYPAL_WEBHOOK_SECRET" | vercel env add PAYPAL_WEBHOOK_SECRET production

echo "Adding PAYPAL_ENVIRONMENT..."
echo "$PAYPAL_ENVIRONMENT" | vercel env add PAYPAL_ENVIRONMENT production

echo "Adding NEXT_PUBLIC_PAYPAL_CLIENT_ID..."
echo "$NEXT_PUBLIC_PAYPAL_CLIENT_ID" | vercel env add NEXT_PUBLIC_PAYPAL_CLIENT_ID production

echo "PayPal environment variables setup complete!"
echo "Note: These are sandbox credentials for testing. Replace with production credentials when ready."
