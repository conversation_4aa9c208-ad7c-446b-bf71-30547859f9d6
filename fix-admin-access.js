const bcrypt = require('bcrypt');

// Direct Supabase database connection without Prisma
// This will work even if <PERSON>rism<PERSON> is having issues

async function fixAdminAccess() {
  try {
    console.log('🔧 FIXING ADMIN ACCESS...');
    
    // We'll use the Supabase Management API to directly update the admin password
    const adminEmail = '<EMAIL>';
    const newPassword = 'SpearAdmin2024!';
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    console.log('✅ Password hashed successfully');
    console.log('📧 Admin Email:', adminEmail);
    console.log('🔑 New Password:', newPassword);
    console.log('🔐 Hashed Password:', hashedPassword);
    
    console.log('\n🚨 MANUAL DATABASE UPDATE REQUIRED:');
    console.log('Run this SQL query in your Supabase SQL Editor:');
    console.log('');
    console.log(`UPDATE "User" SET password = '${hashedPassword}' WHERE email = '${adminEmail}';`);
    console.log('');
    console.log('Or use the emergency admin access page:');
    console.log('http://localhost:3000/emergency-admin');
    console.log('Password: SpearEmergencyAccess2024!');
    
    // Also provide the correct DATABASE_URL format
    console.log('\n🔗 CORRECT DATABASE_URL FORMAT:');
    console.log('You need to replace YOUR_PASSWORD in your .env.local file with your actual Supabase password.');
    console.log('The format should be:');
    console.log('DATABASE_URL=postgres://postgres:<EMAIL>:5432/postgres?sslmode=require');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixAdminAccess();
