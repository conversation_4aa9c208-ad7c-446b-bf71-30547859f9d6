const { Client } = require('pg');
const crypto = require('crypto');

// Try different Supabase connection formats
const possibleUrls = [
  'postgres://postgres.serlglkpatyawryrvwaj:<EMAIL>:6543/postgres',
  'postgresql://postgres:[YOUR-PASSWORD]@db.serlglkpatyawryrvwaj.supabase.co:5432/postgres',
  'postgres://postgres:[YOUR-PASSWORD]@db.serlglkpatyawryrvwaj.supabase.co:5432/postgres?sslmode=require'
];

// Since we know the production app is working, let's try to use the same connection approach
// The actual password should be available in the Supabase dashboard
const databaseUrl = process.env.DATABASE_URL || possibleUrls[0];

console.log('Attempting to connect to Supabase...');
console.log('Note: You may need to update the password in this script with the actual Supabase password');

/**
 * Hash a password using SHA-256 for compatibility with the application
 */
function hashPassword(password) {
  return crypto.createHash('sha256').update(password).digest('hex');
}

async function migrateData() {
  const client = new Client({
    connectionString: databaseUrl,
    ssl: { rejectUnauthorized: false }
  });
  
  try {
    console.log('🔌 Connecting to Supabase database...');
    await client.connect();
    console.log('✅ Connected to Supabase successfully!');

    // Hash passwords
    const adminPassword = hashPassword('password');
    const clientPassword = hashPassword('password');

    console.log('🔧 Creating admin user...');
    
    // Create admin user
    const adminResult = await client.query(`
      INSERT INTO "User" (email, name, password, role, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, NOW(), NOW())
      ON CONFLICT (email) 
      DO UPDATE SET 
        name = EXCLUDED.name,
        password = EXCLUDED.password,
        role = EXCLUDED.role,
        "updatedAt" = NOW()
      RETURNING id, email, name, role;
    `, ['<EMAIL>', 'Marquise Holton', adminPassword, 'ADMIN']);
    
    console.log('✅ Admin user created:', adminResult.rows[0]);

    console.log('🔧 Creating client user...');
    
    // Create client user
    const clientResult = await client.query(`
      INSERT INTO "User" (email, name, password, role, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, NOW(), NOW())
      ON CONFLICT (email) 
      DO UPDATE SET 
        name = EXCLUDED.name,
        password = EXCLUDED.password,
        role = EXCLUDED.role,
        "updatedAt" = NOW()
      RETURNING id, email, name, role;
    `, ['<EMAIL>', 'Test Client', clientPassword, 'CLIENT']);
    
    console.log('✅ Client user created:', clientResult.rows[0]);

    const clientUserId = clientResult.rows[0].id;

    console.log('🔧 Creating device...');
    
    // Create device
    const deviceResult = await client.query(`
      INSERT INTO "Device" (name, "rustDeskId", password, model, status, "userId", "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      ON CONFLICT ("rustDeskId") 
      DO UPDATE SET 
        name = EXCLUDED.name,
        password = EXCLUDED.password,
        model = EXCLUDED.model,
        status = EXCLUDED.status,
        "userId" = EXCLUDED."userId",
        "updatedAt" = NOW()
      RETURNING id, name, "rustDeskId", status;
    `, ['Samsung A14', '1681512408', '82AirmaN@$', 'Android Device', 'online', clientUserId]);
    
    console.log('✅ Device created:', deviceResult.rows[0]);

    console.log('🔧 Creating subscription...');
    
    // Create subscription
    const subscriptionResult = await client.query(`
      INSERT INTO "Subscription" ("stripeSubId", plan, status, price, "currentPeriodEnd", "userId", "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      ON CONFLICT ("stripeSubId") 
      DO UPDATE SET 
        plan = EXCLUDED.plan,
        status = EXCLUDED.status,
        price = EXCLUDED.price,
        "currentPeriodEnd" = EXCLUDED."currentPeriodEnd",
        "userId" = EXCLUDED."userId",
        "updatedAt" = NOW()
      RETURNING id, plan, status, price;
    `, ['sub_test_client', 'basic', 'active', '$350.00', new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), clientUserId]);
    
    console.log('✅ Subscription created:', subscriptionResult.rows[0]);

    console.log('🔧 Creating payment method...');
    
    // Create payment method
    const paymentResult = await client.query(`
      INSERT INTO "PaymentMethod" (id, type, brand, "last4", "expMonth", "expYear", "isDefault", "userId", "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
      ON CONFLICT (id) 
      DO UPDATE SET 
        type = EXCLUDED.type,
        brand = EXCLUDED.brand,
        "last4" = EXCLUDED."last4",
        "expMonth" = EXCLUDED."expMonth",
        "expYear" = EXCLUDED."expYear",
        "isDefault" = EXCLUDED."isDefault",
        "userId" = EXCLUDED."userId",
        "updatedAt" = NOW()
      RETURNING id, type, brand, "last4";
    `, ['pm_test_client', 'credit_card', 'Visa', '4242', 12, 2024, true, clientUserId]);
    
    console.log('✅ Payment method created:', paymentResult.rows[0]);

    console.log('🔧 Creating invoice...');
    
    // Create invoice
    const invoiceResult = await client.query(`
      INSERT INTO "Invoice" (id, amount, status, description, "userId", "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      ON CONFLICT (id) 
      DO UPDATE SET 
        amount = EXCLUDED.amount,
        status = EXCLUDED.status,
        description = EXCLUDED.description,
        "userId" = EXCLUDED."userId",
        "updatedAt" = NOW()
      RETURNING id, amount, status, description;
    `, ['inv_test_client', '$350.00', 'paid', 'Basic Plan - Monthly Subscription', clientUserId]);
    
    console.log('✅ Invoice created:', invoiceResult.rows[0]);

    console.log('🔧 Creating settings...');
    
    // Create settings
    const settings = [
      { key: 'general.companyName', value: 'SPEAR Technologies', type: 'string' },
      { key: 'general.supportEmail', value: '<EMAIL>', type: 'string' },
      { key: 'general.supportPhone', value: '+****************', type: 'string' },
      { key: 'general.defaultDeviceLimit', value: '5', type: 'number' },
    ];

    for (const setting of settings) {
      await client.query(`
        INSERT INTO "Setting" (key, value, type, "createdAt", "updatedAt")
        VALUES ($1, $2, $3, NOW(), NOW())
        ON CONFLICT (key) 
        DO UPDATE SET 
          value = EXCLUDED.value,
          type = EXCLUDED.type,
          "updatedAt" = NOW();
      `, [setting.key, setting.value, setting.type]);
    }
    
    console.log('✅ Settings created');

    console.log('🎉 Database migration completed successfully!');
    console.log('');
    console.log('📊 Summary:');
    console.log('- Admin user: <EMAIL> (password: password)');
    console.log('- Client user: <EMAIL> (password: password)');
    console.log('- Device: Samsung A14 (ID: 1681512408)');
    console.log('- Subscription: Basic plan ($350.00/month)');
    console.log('- Payment method: Visa ending in 4242');
    console.log('- Invoice: $350.00 (paid)');
    console.log('- Settings: 4 general settings');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.error('Error details:', error.message);
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

// Run the migration
migrateData();
