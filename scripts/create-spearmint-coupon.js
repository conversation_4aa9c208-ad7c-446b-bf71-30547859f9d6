const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createSpearmintCoupon() {
  try {
    console.log('Creating SPEARMINT promotional coupon...');
    
    // Create the SPEARMINT coupon
    const spearmintCoupon = await prisma.coupon.upsert({
      where: { code: 'SPEARMINT' },
      update: {
        discountType: 'fixed',
        discountValue: 100,
        maxUses: 1000, // Limited promotional offer
        validFrom: new Date(),
        validTo: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        applicablePlans: ['single-user'], // Only applies to single user plan
        isActive: true,
      },
      create: {
        code: 'SPEARMINT',
        discountType: 'fixed',
        discountValue: 100,
        maxUses: 1000, // Limited promotional offer
        validFrom: new Date(),
        validTo: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        applicablePlans: ['single-user'], // Only applies to single user plan
        isActive: true,
      },
    });
    
    console.log(`✅ Created/Updated SPEARMINT coupon:`);
    console.log(`   Code: ${spearmintCoupon.code}`);
    console.log(`   Discount: $${spearmintCoupon.discountValue} off`);
    console.log(`   Max Uses: ${spearmintCoupon.maxUses}`);
    console.log(`   Valid Until: ${spearmintCoupon.validTo?.toISOString().split('T')[0]}`);
    console.log(`   Applicable Plans: ${spearmintCoupon.applicablePlans.join(', ')}`);
    
    // Test the coupon validation
    console.log('\n🧪 Testing coupon validation...');
    
    // Test valid case
    const validTest = await prisma.coupon.findUnique({
      where: { 
        code: 'SPEARMINT',
      },
    });
    
    if (validTest && validTest.isActive) {
      const now = new Date();
      const isValidDate = validTest.validFrom <= now && (!validTest.validTo || validTest.validTo >= now);
      const hasUsesLeft = !validTest.maxUses || validTest.usedCount < validTest.maxUses;
      const appliesToPlan = validTest.applicablePlans.length === 0 || validTest.applicablePlans.includes('single-user');
      
      console.log(`   ✅ Coupon is active: ${validTest.isActive}`);
      console.log(`   ✅ Date is valid: ${isValidDate}`);
      console.log(`   ✅ Has uses left: ${hasUsesLeft} (${validTest.usedCount}/${validTest.maxUses || 'unlimited'})`);
      console.log(`   ✅ Applies to single-user plan: ${appliesToPlan}`);
      
      if (isValidDate && hasUsesLeft && appliesToPlan) {
        console.log(`   🎉 SPEARMINT coupon is ready for use!`);
      }
    }
    
    // Show pricing calculation example
    console.log('\n💰 Pricing Examples:');
    console.log(`   Single User Regular: $299/month`);
    console.log(`   Single User with SPEARMINT: $199/month (Save $100)`);
    console.log(`   Two User Bundle: $298/month ($149 per user, Save $300 total)`);
    console.log(`   Note: Bundle pricing and coupons cannot be combined`);
    
    await prisma.$disconnect();
    console.log('\n✅ SPEARMINT coupon setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error creating SPEARMINT coupon:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

createSpearmintCoupon();
