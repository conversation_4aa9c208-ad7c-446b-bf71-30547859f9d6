const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const prisma = new PrismaClient();

async function hashPassword(password) {
  return await bcrypt.hash(password, 10);
}

async function fixUserPasswords() {
  try {
    console.log('Fixing user passwords with proper bcrypt hashing...');
    
    // Hash the password properly
    const hashedPassword = await hashPassword('password');
    console.log(`Generated bcrypt hash: ${hashedPassword.substring(0, 20)}... (${hashedPassword.length} chars)`);
    
    // Update admin user
    console.log('\n1. Updating admin user password:');
    const adminUser = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: { password: hashedPassword }
    });
    console.log(`✅ Updated admin user: ${adminUser.name} (${adminUser.email})`);
    
    // Update client user
    console.log('\n2. Updating client user password:');
    const clientUser = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: { password: hashedPassword }
    });
    console.log(`✅ Updated client user: ${clientUser.name} (${clientUser.email})`);
    
    // Test the passwords
    console.log('\n3. Testing updated passwords:');
    
    // Test admin password
    const adminCheck = await bcrypt.compare('password', adminUser.password);
    console.log(`   Admin password verification: ${adminCheck ? '✅ Valid' : '❌ Invalid'}`);
    
    // Test client password
    const clientCheck = await bcrypt.compare('password', clientUser.password);
    console.log(`   Client password verification: ${clientCheck ? '✅ Valid' : '❌ Invalid'}`);
    
    await prisma.$disconnect();
    console.log('\n✅ Password fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing passwords:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

fixUserPasswords();
