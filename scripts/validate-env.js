/**
 * Environment Variable Validation Script
 *
 * This script validates that all required environment variables are set.
 * It should be run before starting the application in production.
 */

// Required environment variables
const requiredVars = [
  'DATABASE_URL',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL',
  'NODE_ENV'
];

// Optional environment variables with default values
const optionalVars = {
  'RUSTDESK_SERVER_IP': '',
  'RUSTDESK_SERVER_PORT': '',
  'RUSTDESK_API_PORT': '',
  'NEXT_PUBLIC_RUSTDESK_SERVER_IP': '',
  'NEXT_PUBLIC_RUSTDESK_SERVER_PORT': '',
  'NEXT_PUBLIC_RUSTDESK_API_PORT': '',
  'RUSTDESK_ANDROID_DEVICE_ID': '',
  'RUSTDESK_ANDROID_PASSWORD': ''
};

// Validate environment variables
function validateEnv() {
  console.log('Validating environment variables...');

  const missingVars = [];

  // Check required variables
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  }

  // Check optional variables and set defaults if not provided
  for (const [varName, defaultValue] of Object.entries(optionalVars)) {
    if (!process.env[varName]) {
      console.log(`Optional variable ${varName} not set, using default value: "${defaultValue}"`);
      process.env[varName] = defaultValue;
    }
  }

  // If any required variables are missing, exit with an error
  if (missingVars.length > 0) {
    console.error('Error: The following required environment variables are missing:');
    missingVars.forEach(varName => console.error(`  - ${varName}`));
    console.error('\nPlease set these variables in your .env file or environment.');
    process.exit(1);
  }

  // Special validation for NEXTAUTH_SECRET
  if (process.env.NEXTAUTH_SECRET === 'generate-a-secure-random-string-for-production' ||
      process.env.NEXTAUTH_SECRET === 'your-secret-key-for-development-only' ||
      process.env.NEXTAUTH_SECRET === 'spear-app-production-secret-key-change-this-in-production' ||
      process.env.NEXTAUTH_SECRET.length < 32) {
    console.warn('\nWARNING: You are using an insecure NEXTAUTH_SECRET value.');
    console.warn('This is insecure and should be changed in production.');
    console.warn('Generate a secure random string using: npm run generate:secret\n');

    if (process.env.NODE_ENV === 'production') {
      console.error('Error: Insecure NEXTAUTH_SECRET in production environment.');
      console.error('Please generate a secure random string using: npm run generate:secret');
      process.exit(1);
    }
  }

  // Special validation for DATABASE_URL
  if (process.env.DATABASE_URL.includes('${PGPASSWORD}') ||
      process.env.DATABASE_URL.includes('${PGHOST}') ||
      process.env.DATABASE_URL.includes('${PGPORT}') ||
      process.env.DATABASE_URL.includes('${PGDATABASE}')) {
    console.error('Error: DATABASE_URL contains template variables that have not been replaced.');
    console.error('Please set the actual database connection string.');
    process.exit(1);
  }

  console.log('Environment validation successful!');
}

// Run validation
validateEnv();

module.exports = validateEnv;
