/**
 * Load environment variables from .env.production file
 * 
 * This script loads environment variables from the .env.production file
 * and then runs the validate-env.js script to validate them.
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env.production file (if it exists)
const envPath = path.resolve(process.cwd(), '.env.production');

// Check if we're in Vercel build environment
const isVercelBuild = process.env.VERCEL === '1' || process.env.CI === 'true';

if (fs.existsSync(envPath)) {
  console.log(`Loading environment variables from ${envPath}`);
  const envConfig = dotenv.parse(fs.readFileSync(envPath));

  // Set environment variables
  for (const key in envConfig) {
    process.env[key] = envConfig[key];
  }

  console.log('Environment variables loaded successfully');
} else if (isVercelBuild) {
  console.log('Running in Vercel build environment - using platform environment variables');
} else {
  console.error(`Error: ${envPath} not found`);
  process.exit(1);
}

// Run the validate-env.js script
require('./validate-env');
