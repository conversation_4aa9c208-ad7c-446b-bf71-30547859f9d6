#!/usr/bin/env node

/**
 * This script verifies that all required environment variables are set
 * Run it with: node scripts/verify-env-vars.js
 * 
 * It will check for required variables and warn about missing ones.
 * This is useful to run before deployment to ensure all necessary variables are set.
 */

// Define required environment variables
const requiredVars = [
  // Database
  'DATABASE_URL',
  
  // Authentication
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL',
  
  // RustDesk (optional but recommended for production)
  'RUSTDESK_ANDROID_DEVICE_ID',
  'RUSTDESK_ANDROID_PASSWORD',
  
  // Stripe (required for payment processing)
  'STRIPE_SECRET_KEY',
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
];

// Define optional environment variables
const optionalVars = [
  // RustDesk custom server (optional)
  'RUSTDESK_SERVER_IP',
  'RUSTDESK_SERVER_PORT',
  'RUSTDESK_API_PORT',
  'NEXT_PUBLIC_RUSTDESK_SERVER_IP',
  'NEXT_PUBLIC_RUSTDESK_SERVER_PORT',
  'NEXT_PUBLIC_RUSTDESK_API_PORT',
];

// Check for required variables
console.log('\nVerifying required environment variables:');
console.log('----------------------------------------');

let missingRequired = false;

requiredVars.forEach(varName => {
  if (!process.env[varName]) {
    console.log(`❌ Missing required variable: ${varName}`);
    missingRequired = true;
  } else {
    // Show first few characters of the value for verification
    const value = process.env[varName];
    const displayValue = value.length > 8 
      ? `${value.substring(0, 4)}...${value.substring(value.length - 4)}` 
      : '********';
    console.log(`✅ ${varName}: ${displayValue}`);
  }
});

// Check for optional variables
console.log('\nChecking optional environment variables:');
console.log('----------------------------------------');

optionalVars.forEach(varName => {
  if (!process.env[varName]) {
    console.log(`⚠️ Optional variable not set: ${varName}`);
  } else {
    console.log(`✅ ${varName} is set`);
  }
});

// Summary
console.log('\nEnvironment variables summary:');
console.log('----------------------------------------');
if (missingRequired) {
  console.log('❌ Some required environment variables are missing!');
  console.log('Please set them before deploying to production.');
  process.exit(1);
} else {
  console.log('✅ All required environment variables are set.');
  console.log('You can proceed with deployment.');
  process.exit(0);
}
