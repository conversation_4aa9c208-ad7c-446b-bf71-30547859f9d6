/**
 * Generate a secure random string for NEXTAUTH_SECRET
 * 
 * This script generates a secure random string that can be used as the NEXTAUTH_SECRET
 * environment variable in production. The secret is used to sign and encrypt cookies
 * and tokens for authentication.
 * 
 * Usage:
 * node scripts/generate-secret.js
 */

const crypto = require('crypto');

/**
 * Generate a secure random string of the specified length
 * @param {number} length - The length of the random string to generate
 * @returns {string} - A secure random string
 */
function generateSecureRandomString(length = 32) {
  return crypto.randomBytes(length).toString('base64');
}

// Generate a secure random string for NEXTAUTH_SECRET
const secret = generateSecureRandomString();

console.log('Generated NEXTAUTH_SECRET:');
console.log(secret);
console.log('\nAdd this to your production environment variables:');
console.log(`NEXTAUTH_SECRET=${secret}`);
