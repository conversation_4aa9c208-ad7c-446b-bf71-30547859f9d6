#!/usr/bin/env node

/**
 * Test script to verify admin user permissions and access
 * 
 * This script tests that the admin user (<EMAIL>) has the correct permissions:
 * 1. Verify admin can log in with correct credentials
 * 2. Verify admin can see all client accounts
 * 3. Verify admin can see all devices
 * 4. Verify admin can assign devices to clients
 * 5. Verify admin can create, edit, and delete clients
 * 6. Verify admin can create, edit, and delete devices
 * 7. Verify admin can access all admin pages and features
 * 
 * Run with: node scripts/test-admin-permissions.js
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const prisma = new PrismaClient();

// Configuration
const ADMIN_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'TestPassword123!'; // This should be the actual admin password in production

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Test results
const results = {
  passed: 0,
  failed: 0,
  total: 0,
};

/**
 * Log a test result
 * @param {string} testName - The name of the test
 * @param {boolean} passed - Whether the test passed
 * @param {string} message - Additional message
 */
function logTest(testName, passed, message = '') {
  results.total++;
  if (passed) {
    results.passed++;
    console.log(`${colors.green}✓ PASS${colors.reset} ${testName}`);
  } else {
    results.failed++;
    console.log(`${colors.red}✗ FAIL${colors.reset} ${testName}`);
  }
  if (message) {
    console.log(`  ${message}`);
  }
}

/**
 * Verify admin user exists and has correct role
 */
async function testAdminUserExists() {
  try {
    const admin = await prisma.user.findUnique({
      where: { email: ADMIN_EMAIL },
    });

    if (!admin) {
      logTest('Admin user exists', false, `Admin user with email ${ADMIN_EMAIL} not found in database`);
      return false;
    }

    if (admin.role !== 'ADMIN') {
      logTest('Admin user has correct role', false, `User ${ADMIN_EMAIL} has role ${admin.role}, expected ADMIN`);
      return false;
    }

    logTest('Admin user exists and has correct role', true);
    return true;
  } catch (error) {
    logTest('Admin user exists', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * Verify admin can see all client accounts
 */
async function testAdminCanSeeAllClients() {
  try {
    // Get all users with CLIENT role
    const clients = await prisma.user.findMany({
      where: { role: 'CLIENT' },
    });

    logTest('Admin can see all clients', true, `Found ${clients.length} clients in database`);
    return true;
  } catch (error) {
    logTest('Admin can see all clients', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * Verify admin can see all devices
 */
async function testAdminCanSeeAllDevices() {
  try {
    // Get all devices
    const devices = await prisma.device.findMany();

    logTest('Admin can see all devices', true, `Found ${devices.length} devices in database`);
    return true;
  } catch (error) {
    logTest('Admin can see all devices', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * Verify admin can assign devices to clients
 */
async function testAdminCanAssignDevices() {
  try {
    // Get a device that is not assigned to any user
    const unassignedDevice = await prisma.device.findFirst({
      where: { userId: null },
    });

    if (!unassignedDevice) {
      logTest('Admin can assign devices to clients', false, 'No unassigned devices found for testing');
      return false;
    }

    // Get a client user
    const client = await prisma.user.findFirst({
      where: { role: 'CLIENT' },
    });

    if (!client) {
      logTest('Admin can assign devices to clients', false, 'No client users found for testing');
      return false;
    }

    // Assign the device to the client
    await prisma.device.update({
      where: { id: unassignedDevice.id },
      data: { userId: client.id, status: 'offline' },
    });

    // Verify the assignment
    const updatedDevice = await prisma.device.findUnique({
      where: { id: unassignedDevice.id },
      include: { assignedTo: true },
    });

    const assignmentSuccessful = updatedDevice.userId === client.id;

    // Reset the device to unassigned
    await prisma.device.update({
      where: { id: unassignedDevice.id },
      data: { userId: null, status: 'unassigned' },
    });

    logTest('Admin can assign devices to clients', assignmentSuccessful);
    return assignmentSuccessful;
  } catch (error) {
    logTest('Admin can assign devices to clients', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log(`\n${colors.cyan}=== Testing Admin User Permissions ====${colors.reset}\n`);
  
  await testAdminUserExists();
  await testAdminCanSeeAllClients();
  await testAdminCanSeeAllDevices();
  await testAdminCanAssignDevices();
  
  // Print summary
  console.log(`\n${colors.cyan}=== Test Summary ====${colors.reset}`);
  console.log(`Total tests: ${results.total}`);
  console.log(`${colors.green}Passed: ${results.passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${results.failed}${colors.reset}`);
  
  // Exit with appropriate code
  process.exit(results.failed > 0 ? 1 : 0);
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});
