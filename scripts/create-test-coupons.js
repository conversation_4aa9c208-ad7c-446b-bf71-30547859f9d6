const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createTestCoupons() {
  try {
    console.log('Creating test coupons...');
    
    // Create percentage discount coupon
    const percentageCoupon = await prisma.coupon.upsert({
      where: { code: 'SAVE20' },
      update: {},
      create: {
        code: 'SAVE20',
        discountType: 'percentage',
        discountValue: 20,
        maxUses: 100,
        validFrom: new Date(),
        validTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        applicablePlans: ['basic', 'pro', 'enterprise'],
        isActive: true,
      },
    });
    console.log(`✅ Created percentage coupon: ${percentageCoupon.code} (${percentageCoupon.discountValue}% off)`);
    
    // Create fixed discount coupon
    const fixedCoupon = await prisma.coupon.upsert({
      where: { code: 'WELCOME10' },
      update: {},
      create: {
        code: 'WELCOME10',
        discountType: 'fixed',
        discountValue: 10,
        maxUses: 50,
        validFrom: new Date(),
        validTo: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        applicablePlans: ['basic', 'pro'],
        isActive: true,
      },
    });
    console.log(`✅ Created fixed coupon: ${fixedCoupon.code} ($${fixedCoupon.discountValue} off)`);
    
    // Create enterprise-only coupon
    const enterpriseCoupon = await prisma.coupon.upsert({
      where: { code: 'ENTERPRISE50' },
      update: {},
      create: {
        code: 'ENTERPRISE50',
        discountType: 'percentage',
        discountValue: 50,
        maxUses: 10,
        validFrom: new Date(),
        validTo: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        applicablePlans: ['enterprise'],
        isActive: true,
      },
    });
    console.log(`✅ Created enterprise coupon: ${enterpriseCoupon.code} (${enterpriseCoupon.discountValue}% off enterprise only)`);
    
    // Create unlimited usage coupon
    const unlimitedCoupon = await prisma.coupon.upsert({
      where: { code: 'BETA15' },
      update: {},
      create: {
        code: 'BETA15',
        discountType: 'percentage',
        discountValue: 15,
        maxUses: null, // unlimited
        validFrom: new Date(),
        validTo: null, // no expiry
        applicablePlans: [], // applies to all plans
        isActive: true,
      },
    });
    console.log(`✅ Created unlimited coupon: ${unlimitedCoupon.code} (${unlimitedCoupon.discountValue}% off, unlimited uses)`);
    
    // Create expired coupon for testing
    const expiredCoupon = await prisma.coupon.upsert({
      where: { code: 'EXPIRED' },
      update: {},
      create: {
        code: 'EXPIRED',
        discountType: 'percentage',
        discountValue: 25,
        maxUses: 100,
        validFrom: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
        validTo: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago (expired)
        applicablePlans: ['basic', 'pro', 'enterprise'],
        isActive: true,
      },
    });
    console.log(`✅ Created expired coupon: ${expiredCoupon.code} (for testing expired validation)`);
    
    // List all coupons
    console.log('\n📋 All coupons in database:');
    const allCoupons = await prisma.coupon.findMany({
      orderBy: { createdAt: 'desc' },
    });
    
    allCoupons.forEach((coupon, index) => {
      const status = coupon.isActive ? '🟢 Active' : '🔴 Inactive';
      const expiry = coupon.validTo ? `expires ${coupon.validTo.toISOString().split('T')[0]}` : 'no expiry';
      const usage = coupon.maxUses ? `${coupon.usedCount}/${coupon.maxUses} uses` : `${coupon.usedCount}/unlimited uses`;
      const discount = coupon.discountType === 'percentage' ? `${coupon.discountValue}%` : `$${coupon.discountValue}`;
      
      console.log(`${index + 1}. ${coupon.code} - ${discount} off (${status}, ${expiry}, ${usage})`);
    });
    
    await prisma.$disconnect();
    console.log('\n✅ Test coupons created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating test coupons:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

createTestCoupons();
