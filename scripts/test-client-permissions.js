#!/usr/bin/env node

/**
 * Test script to verify client user permissions and access
 * 
 * This script tests that client users have the correct permissions:
 * 1. Verify clients can log in with correct credentials
 * 2. Verify clients can only see their assigned devices
 * 3. Verify clients cannot access admin pages
 * 4. Verify clients cannot see other clients' devices
 * 5. Verify clients can access their subscription information
 * 
 * Run with: node scripts/test-client-permissions.js
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const prisma = new PrismaClient();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Test results
const results = {
  passed: 0,
  failed: 0,
  total: 0,
};

/**
 * Log a test result
 * @param {string} testName - The name of the test
 * @param {boolean} passed - Whether the test passed
 * @param {string} message - Additional message
 */
function logTest(testName, passed, message = '') {
  results.total++;
  if (passed) {
    results.passed++;
    console.log(`${colors.green}✓ PASS${colors.reset} ${testName}`);
  } else {
    results.failed++;
    console.log(`${colors.red}✗ FAIL${colors.reset} ${testName}`);
  }
  if (message) {
    console.log(`  ${message}`);
  }
}

/**
 * Verify client users exist
 */
async function testClientUsersExist() {
  try {
    const clients = await prisma.user.findMany({
      where: { role: 'CLIENT' },
    });

    if (clients.length === 0) {
      logTest('Client users exist', false, 'No client users found in database');
      return false;
    }

    logTest('Client users exist', true, `Found ${clients.length} client users in database`);
    return true;
  } catch (error) {
    logTest('Client users exist', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * Verify clients can only see their assigned devices
 */
async function testClientDeviceAccess() {
  try {
    // Get a client user with assigned devices
    const client = await prisma.user.findFirst({
      where: { 
        role: 'CLIENT',
        devices: {
          some: {}
        }
      },
      include: {
        devices: true,
      },
    });

    if (!client) {
      logTest('Client can only see assigned devices', false, 'No client users with assigned devices found for testing');
      return false;
    }

    // Get all devices
    const allDevices = await prisma.device.findMany();
    
    // Get devices assigned to this client
    const clientDevices = await prisma.device.findMany({
      where: { userId: client.id },
    });

    // Verify the client can only see their assigned devices
    const canOnlySeeAssigned = clientDevices.length > 0 && clientDevices.length < allDevices.length;

    logTest(
      'Client can only see assigned devices', 
      canOnlySeeAssigned, 
      `Client ${client.email} has ${clientDevices.length} devices out of ${allDevices.length} total devices`
    );
    return canOnlySeeAssigned;
  } catch (error) {
    logTest('Client can only see assigned devices', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * Verify clients cannot see other clients' devices
 */
async function testClientCannotSeeOtherDevices() {
  try {
    // Get two different clients with assigned devices
    const clients = await prisma.user.findMany({
      where: { 
        role: 'CLIENT',
        devices: {
          some: {}
        }
      },
      include: {
        devices: true,
      },
      take: 2,
    });

    if (clients.length < 2) {
      logTest('Client cannot see other clients\' devices', false, 'Not enough client users with devices for testing');
      return false;
    }

    const client1 = clients[0];
    const client2 = clients[1];

    // Check if the devices are different
    const client1DeviceIds = client1.devices.map(d => d.id);
    const client2DeviceIds = client2.devices.map(d => d.id);
    
    // Find any devices that are in both lists (should be none)
    const sharedDevices = client1DeviceIds.filter(id => client2DeviceIds.includes(id));
    
    const cannotSeeOtherDevices = sharedDevices.length === 0;

    logTest(
      'Client cannot see other clients\' devices', 
      cannotSeeOtherDevices, 
      `Client ${client1.email} and ${client2.email} have no shared devices`
    );
    return cannotSeeOtherDevices;
  } catch (error) {
    logTest('Client cannot see other clients\' devices', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * Verify clients can access their subscription information
 */
async function testClientSubscriptionAccess() {
  try {
    // Get a client user with a subscription
    const client = await prisma.user.findFirst({
      where: { 
        role: 'CLIENT',
        subscriptions: {
          some: {}
        }
      },
      include: {
        subscriptions: true,
      },
    });

    if (!client) {
      logTest('Client can access subscription information', false, 'No client users with subscriptions found for testing');
      return false;
    }

    logTest(
      'Client can access subscription information', 
      true, 
      `Client ${client.email} has subscription with status: ${client.subscriptions[0].status}`
    );
    return true;
  } catch (error) {
    logTest('Client can access subscription information', false, `Error: ${error.message}`);
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log(`\n${colors.cyan}=== Testing Client User Permissions ====${colors.reset}\n`);
  
  await testClientUsersExist();
  await testClientDeviceAccess();
  await testClientCannotSeeOtherDevices();
  await testClientSubscriptionAccess();
  
  // Print summary
  console.log(`\n${colors.cyan}=== Test Summary ====${colors.reset}`);
  console.log(`Total tests: ${results.total}`);
  console.log(`${colors.green}Passed: ${results.passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${results.failed}${colors.reset}`);
  
  // Exit with appropriate code
  process.exit(results.failed > 0 ? 1 : 0);
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});
