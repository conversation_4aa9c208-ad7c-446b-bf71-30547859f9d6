import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import crypto from 'crypto';

const prisma = new PrismaClient();

/**
 * Hash a password using bcrypt
 */
async function hashWithBcrypt(password: string): Promise<string> {
  return await bcrypt.hash(password, 10);
}

/**
 * Check if a hash is in SHA-256 format (64 hex characters)
 */
function isSha256Hash(hash: string): boolean {
  return /^[a-f0-9]{64}$/i.test(hash);
}

/**
 * Migrate user passwords from SHA-256 to bcrypt
 */
async function migratePasswords() {
  try {
    console.log('Starting password migration from SHA-256 to bcrypt...');
    
    // Get all users with passwords
    const users = await prisma.user.findMany({
      where: {
        password: {
          not: null
        }
      }
    });
    
    console.log(`Found ${users.length} users with passwords`);
    
    let migratedCount = 0;
    
    // Process each user
    for (const user of users) {
      if (user.password && isSha256Hash(user.password)) {
        console.log(`Migrating password for user: ${user.email}`);
        
        // For the admin user with the known password "password"
        if (user.email === '<EMAIL>' && 
            user.password === '5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8') {
          // Hash the known password with bcrypt
          const bcryptHash = await hashWithBcrypt('password');
          
          // Update the user with the new bcrypt hash
          await prisma.user.update({
            where: { id: user.id },
            data: { password: bcryptHash }
          });
          
          console.log(`Migrated admin user password to bcrypt`);
          migratedCount++;
        }
        // For other users with SHA-256 hashes
        else if (user.password) {
          // We can't recover the original password, so we'll set a temporary one
          // and require the user to reset their password
          const tempPassword = await hashWithBcrypt('TemporaryPassword123!');
          
          await prisma.user.update({
            where: { id: user.id },
            data: { 
              password: tempPassword,
              // You might want to add a flag to indicate the user needs to reset their password
              // requirePasswordReset: true
            }
          });
          
          console.log(`Set temporary password for user: ${user.email}`);
          migratedCount++;
        }
      } else {
        console.log(`User ${user.email} already has a bcrypt hash or no password`);
      }
    }
    
    console.log(`Migration completed. Migrated ${migratedCount} users.`);
  } catch (error) {
    console.error('Error during password migration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migratePasswords();
