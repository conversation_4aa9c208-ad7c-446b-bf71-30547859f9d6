const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUser() {
  try {
    console.log('Checking for user with email containing "2020opti"...');
    
    // Search for users with email containing "2020opti"
    const users = await prisma.user.findMany({
      where: {
        email: {
          contains: '2020opti',
          mode: 'insensitive'
        }
      }
    });
    
    if (users.length > 0) {
      console.log(`Found ${users.length} user(s) with email containing "2020opti":`);
      users.forEach((user, index) => {
        console.log(`User ${index + 1}:`);
        console.log(`  ID: ${user.id}`);
        console.log(`  Name: ${user.name}`);
        console.log(`  Email: ${user.email}`);
        console.log(`  Role: ${user.role}`);
        console.log(`  Created: ${user.createdAt}`);
        console.log(`  Updated: ${user.updatedAt}`);
        console.log('---');
      });
    } else {
      console.log('No users found with email containing "2020opti"');
      
      // Let's also check all users to see what we have
      console.log('\nAll users in database:');
      const allUsers = await prisma.user.findMany();
      allUsers.forEach((user, index) => {
        console.log(`User ${index + 1}: ${user.name} (${user.email}, Role: ${user.role})`);
      });
    }
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

checkUser();
