const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testNotifications() {
  try {
    console.log('Testing notification system...');
    
    // Create some test notifications
    console.log('\n1. Creating test notifications:');
    
    // Create a global admin notification (userId = null)
    const adminNotification = await prisma.notification.create({
      data: {
        title: 'System Update',
        message: 'The system has been updated with new features.',
        type: 'info',
        userId: null, // Global notification for all admins
        read: false,
      },
    });
    console.log(`✅ Created admin notification: ${adminNotification.title}`);
    
    // Create a new user signup notification
    const signupNotification = await prisma.notification.create({
      data: {
        title: 'New User Signup',
        message: 'Testing User (<EMAIL>) has signed up as a client.',
        type: 'info',
        userId: null, // Global notification for all admins
        read: false,
      },
    });
    console.log(`✅ Created signup notification: ${signupNotification.title}`);
    
    // Create a device assignment notification
    const deviceNotification = await prisma.notification.create({
      data: {
        title: 'Device Assigned',
        message: 'Samsung A14 (1681512408) has been assigned to Test Client.',
        type: 'success',
        userId: null, // Global notification for all admins
        read: false,
      },
    });
    console.log(`✅ Created device notification: ${deviceNotification.title}`);
    
    // Test fetching notifications
    console.log('\n2. Testing notification queries:');
    
    // Get all notifications
    const allNotifications = await prisma.notification.findMany({
      orderBy: { createdAt: 'desc' },
    });
    console.log(`✅ Found ${allNotifications.length} total notifications`);
    
    // Get admin notifications (userId = null OR userId = admin_id)
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (adminUser) {
      const adminNotifications = await prisma.notification.findMany({
        where: {
          OR: [
            { userId: adminUser.id },
            { userId: null },
          ]
        },
        orderBy: { createdAt: 'desc' },
      });
      console.log(`✅ Found ${adminNotifications.length} notifications for admin`);
      
      // Get unread count for admin
      const unreadCount = await prisma.notification.count({
        where: {
          OR: [
            { userId: adminUser.id },
            { userId: null },
          ],
          read: false,
        },
      });
      console.log(`✅ Admin has ${unreadCount} unread notifications`);
    }
    
    // Test marking notification as read
    console.log('\n3. Testing mark as read:');
    const updatedNotification = await prisma.notification.update({
      where: { id: adminNotification.id },
      data: { read: true },
    });
    console.log(`✅ Marked notification as read: ${updatedNotification.title}`);
    
    await prisma.$disconnect();
    console.log('\n✅ Notification system test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing notifications:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

testNotifications();
