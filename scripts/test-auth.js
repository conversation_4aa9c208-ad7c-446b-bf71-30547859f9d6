const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const prisma = new PrismaClient();

async function testAuth() {
  try {
    console.log('Testing authentication system...');
    
    // Test 1: Check if admin user exists and password is hashed
    console.log('\n1. Testing admin user authentication:');
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (adminUser) {
      console.log(`✅ Admin user found: ${adminUser.name} (${adminUser.email})`);
      console.log(`   Role: ${adminUser.role}`);
      console.log(`   Password hash length: ${adminUser.password?.length || 0} characters`);
      
      // Test password verification
      if (adminUser.password) {
        const isValidPassword = await bcrypt.compare('password', adminUser.password);
        console.log(`   Password verification: ${isValidPassword ? '✅ Valid' : '❌ Invalid'}`);
      } else {
        console.log('   ❌ No password set for admin user');
      }
    } else {
      console.log('❌ Admin user not found');
    }
    
    // Test 2: Check client user
    console.log('\n2. Testing client user authentication:');
    const clientUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (clientUser) {
      console.log(`✅ Client user found: ${clientUser.name} (${clientUser.email})`);
      console.log(`   Role: ${clientUser.role}`);
      console.log(`   Password hash length: ${clientUser.password?.length || 0} characters`);
      
      if (clientUser.password) {
        const isValidPassword = await bcrypt.compare('password', clientUser.password);
        console.log(`   Password verification: ${isValidPassword ? '✅ Valid' : '❌ Invalid'}`);
      } else {
        console.log('   ❌ No password set for client user');
      }
    } else {
      console.log('❌ Client user not found');
    }
    
    // Test 3: Check new user (<EMAIL>)
    console.log('\n3. Testing new user authentication:');
    const newUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (newUser) {
      console.log(`✅ New user found: ${newUser.name} (${newUser.email})`);
      console.log(`   Role: ${newUser.role}`);
      console.log(`   Password hash length: ${newUser.password?.length || 0} characters`);
      
      if (newUser.password) {
        // We don't know the password for this user, so we'll just check if it's hashed
        console.log(`   Password appears to be hashed: ${newUser.password.startsWith('$2b$') ? '✅ Yes' : '❌ No'}`);
      } else {
        console.log('   ❌ No password set for new user');
      }
    } else {
      console.log('❌ New user not found');
    }
    
    // Test 4: Database connection
    console.log('\n4. Testing database connection:');
    const userCount = await prisma.user.count();
    const deviceCount = await prisma.device.count();
    const subscriptionCount = await prisma.subscription.count();
    
    console.log(`✅ Database connection successful`);
    console.log(`   Users: ${userCount}`);
    console.log(`   Devices: ${deviceCount}`);
    console.log(`   Subscriptions: ${subscriptionCount}`);
    
    await prisma.$disconnect();
    console.log('\n✅ Authentication system test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing authentication:', error);
    await prisma.$disconnect();
    process.exit(1);
  }
}

testAuth();
