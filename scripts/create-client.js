// scripts/create-client.js
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

/**
 * Hash a password using SHA-256
 */
function hashPassword(password) {
  return crypto.createHash('sha256').update(password).digest('hex');
}

async function main() {
  console.log('Creating client user and assigning device...');

  // Create client user
  const clientUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      password: hashPassword('password'),
      role: 'CLIENT'
    },
    create: {
      email: '<EMAIL>',
      name: 'Test Client',
      password: hashPassword('password'),
      role: 'CLIENT'
    }
  });
  console.log('Client user created or updated:', clientUser);

  // Find the RustDesk device
  let device = await prisma.device.findFirst({
    where: { rustDeskId: '1681512408' }
  });

  if (device) {
    // Update the device to assign it to the client
    device = await prisma.device.update({
      where: { id: device.id },
      data: {
        userId: clientUser.id,
        status: 'online'
      }
    });
    console.log('Device assigned to client:', device);
  } else {
    // Create the device if it doesn't exist
    device = await prisma.device.create({
      data: {
        name: 'Samsung A14',
        rustDeskId: '1681512408',
        password: '82AirmaN@$',
        model: 'Android Device',
        status: 'online',
        userId: clientUser.id
      }
    });
    console.log('Device created and assigned to client:', device);
  }

  console.log('Client setup completed successfully.');
}

main()
  .catch((e) => {
    console.error('Error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
