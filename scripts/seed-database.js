/**
 * Database seeding script
 * 
 * This script seeds the database with initial data for development and testing.
 * It creates admin and client users, devices, and subscriptions.
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

/**
 * Hash a password using bcrypt
 * @param {string} password - The password to hash
 * @returns {Promise<string>} - The hashed password
 */
async function hashPassword(password) {
  return await bcrypt.hash(password, 10);
}

/**
 * Seed the database with initial data
 */
async function main() {
  console.log('Seeding database...');

  try {
    // Hash passwords
    const adminPassword = await hashPassword('password');
    const clientPassword = await hashPassword('password');

    // Create admin user
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: adminPassword,
      },
      create: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON>',
        password: adminPassword,
        role: 'ADMIN',
      },
    });
    console.log('Admin user created or updated:', adminUser.email);

    // Create client user
    const clientUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: clientPassword,
      },
      create: {
        email: '<EMAIL>',
        name: 'Test Client',
        password: clientPassword,
        role: 'CLIENT',
      },
    });
    console.log('Client user created or updated:', clientUser.email);

    // Create subscription for client
    const subscription = await prisma.subscription.upsert({
      where: { stripeSubId: 'sub_test123' },
      update: {
        status: 'active',
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      },
      create: {
        stripeSubId: 'sub_test123',
        status: 'active',
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        userId: clientUser.id,
      },
    });
    console.log('Subscription created or updated:', subscription.stripeSubId);

    // Create RustDesk device
    const device = await prisma.device.upsert({
      where: { rustDeskId: '1681512408' },
      update: {
        status: 'online',
        userId: clientUser.id,
      },
      create: {
        name: 'Samsung A14',
        rustDeskId: '1681512408',
        password: '82AirmaN@$',
        model: 'Android Device',
        status: 'online',
        userId: clientUser.id,
      },
    });
    console.log('Device created or updated:', device.name);

    // Create a second device for testing
    const device2 = await prisma.device.upsert({
      where: { rustDeskId: '1234567890' },
      update: {
        status: 'offline',
        userId: clientUser.id,
      },
      create: {
        name: 'Test Device',
        rustDeskId: '1234567890',
        password: 'testpassword',
        model: 'Test Model',
        status: 'offline',
        userId: clientUser.id,
      },
    });
    console.log('Second device created or updated:', device2.name);

    // Create an unassigned device
    const device3 = await prisma.device.upsert({
      where: { rustDeskId: '0987654321' },
      update: {
        status: 'unassigned',
        userId: null,
      },
      create: {
        name: 'Unassigned Device',
        rustDeskId: '0987654321',
        password: 'unassignedpassword',
        model: 'Unassigned Model',
        status: 'unassigned',
        userId: null,
      },
    });
    console.log('Unassigned device created or updated:', device3.name);

    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
main();
