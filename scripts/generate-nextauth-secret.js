/**
 * Generate a secure random string for NEXTAUTH_SECRET
 * 
 * This script generates a secure random string that can be used for NEXTAUTH_SECRET
 * in production. It uses the crypto module to generate a random buffer and
 * encodes it as base64.
 */

const crypto = require('crypto');

// Generate a secure random string
function generateSecureRandomString(length = 32) {
  return crypto.randomBytes(length).toString('base64');
}

// Generate and print the secret
const secret = generateSecureRandomString();
console.log('\nGenerated NEXTAUTH_SECRET for production:');
console.log('----------------------------------------');
console.log(secret);
console.log('----------------------------------------');
console.log('\nAdd this to your .env.production file or your hosting environment variables:');
console.log(`NEXTAUTH_SECRET="${secret}"\n`);

// Return the secret for use in other scripts
module.exports = { generateSecureRandomString };
