<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spear Remote Connect</title>
    <link rel="icon" href="/favicon.ico" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .card {
            background-color: #1e293b;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 500px;
            padding: 24px;
        }
        .logo {
            width: 120px;
            margin: 0 auto 16px;
            display: block;
        }
        h1 {
            text-align: center;
            margin-top: 0;
            color: #e2e8f0;
        }
        .form-group {
            margin-bottom: 16px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #94a3b8;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #475569;
            background-color: #334155;
            color: #e2e8f0;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #4f46e5;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #4338ca;
        }
        button:disabled {
            background-color: #6366f1;
            opacity: 0.7;
            cursor: not-allowed;
        }
        .status {
            margin-top: 16px;
            text-align: center;
            color: #94a3b8;
        }
        .instructions {
            margin-top: 24px;
            padding: 16px;
            background-color: #1e40af;
            border-radius: 4px;
            color: #bfdbfe;
        }
        .instructions h2 {
            margin-top: 0;
            font-size: 18px;
            color: #e0f2fe;
        }
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
        }
        .hidden {
            display: none;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="card">
        <img src="/images/spear-logo.PNG" alt="Spear Logo" class="logo">
        <h1>Remote Control</h1>
        
        <div id="connect-form">
            <div class="form-group">
                <label for="deviceId">Device ID</label>
                <input type="text" id="deviceId" value="1681512408">
            </div>
            
            <div class="form-group">
                <label for="password">Access Code</label>
                <input type="password" id="password" value="82AirmaN@$">
            </div>
            
            <button id="connect-button" onclick="connectToDevice()">Connect to Device</button>
            
            <div id="status" class="status"></div>
        </div>
        
        <div id="instructions" class="instructions hidden">
            <h2>Connection Instructions</h2>
            <ol>
                <li>A new window has opened with the RustDesk web client.</li>
                <li>Your device ID and password have been pre-filled.</li>
                <li>Click on your device icon (Android phone) to connect.</li>
                <li>You should now see your device screen and can control it remotely.</li>
            </ol>
            <p>If the window was blocked, please allow pop-ups for this site and try again.</p>
            <button onclick="connectToDevice()">Connect Again</button>
        </div>
    </div>
    
    <script>
        // Get elements
        const deviceIdInput = document.getElementById('deviceId');
        const passwordInput = document.getElementById('password');
        const connectButton = document.getElementById('connect-button');
        const statusElement = document.getElementById('status');
        const connectForm = document.getElementById('connect-form');
        const instructionsElement = document.getElementById('instructions');
        
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const urlDeviceId = urlParams.get('id');
        const urlPassword = urlParams.get('password');
        
        // Set input values from URL parameters if available
        if (urlDeviceId) {
            deviceIdInput.value = urlDeviceId;
        }
        if (urlPassword) {
            passwordInput.value = urlPassword;
        }
        
        // Connect to device
        function connectToDevice() {
            const deviceId = deviceIdInput.value.trim();
            const password = passwordInput.value;
            
            if (!deviceId) {
                showStatus('Please enter a device ID');
                return;
            }
            
            // Disable button and show loading
            connectButton.disabled = true;
            showStatus('<div class="spinner"></div> Opening RustDesk web client...');
            
            // Build the connection URL
            let connectionUrl = `https://rustdesk.com/web/?id=${deviceId}`;
            if (password) {
                connectionUrl += `&password=${encodeURIComponent(password)}`;
            }
            
            // Open in new window
            const rustDeskWindow = window.open(connectionUrl, '_blank');
            
            // Check if window was blocked
            if (!rustDeskWindow || rustDeskWindow.closed || typeof rustDeskWindow.closed === 'undefined') {
                showStatus('Popup blocked. Please allow popups and try again.');
                connectButton.disabled = false;
            } else {
                // Show instructions
                connectForm.classList.add('hidden');
                instructionsElement.classList.remove('hidden');
                
                // Focus the new window
                rustDeskWindow.focus();
            }
        }
        
        // Show status message
        function showStatus(message) {
            statusElement.innerHTML = message;
        }
        
        // Auto-connect if URL parameters are present
        if (urlDeviceId) {
            connectToDevice();
        }
    </script>
</body>
</html>
