<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spear Remote Control</title>
    <link rel="icon" href="/favicon.ico" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #0f172a; /* Dark blue background */
            color: #e2e8f0;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .card {
            background-color: #1e293b; /* Darker card background */
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 400px;
            padding: 24px;
        }
        .card-header {
            margin-bottom: 24px;
            text-align: center;
        }
        .card-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
            color: #e2e8f0;
        }
        .form-group {
            margin-bottom: 16px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #94a3b8;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #475569;
            background-color: #334155;
            color: #e2e8f0;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input::placeholder {
            color: #64748b;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #4f46e5; /* Indigo color for Spear */
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #4338ca;
        }
        button:disabled {
            background-color: #6366f1;
            opacity: 0.7;
            cursor: not-allowed;
        }
        .success-icon {
            width: 64px;
            height: 64px;
            background-color: #064e3b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px auto;
        }
        .success-icon svg {
            width: 32px;
            height: 32px;
            color: #10b981;
        }
        .text-center {
            text-align: center;
        }
        .text-gray {
            color: #94a3b8;
        }
        .mt-4 {
            margin-top: 16px;
        }
        .mt-6 {
            margin-top: 24px;
        }
        .hidden {
            display: none;
        }
        .spinner {
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .logo {
            width: 120px;
            margin: 0 auto 16px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="card-header">
            <img src="/images/spear-logo.PNG" alt="Spear Logo" class="logo">
            <h1 class="card-title">Remote Control</h1>
        </div>

        <div id="connect-form">
            <div class="form-group">
                <label for="deviceId">Device ID</label>
                <input type="text" id="deviceId" placeholder="Enter device ID" value="1681512408">
            </div>

            <div class="form-group">
                <label for="password">Access Code</label>
                <input type="password" id="password" placeholder="Enter access code" value="82AirmaN@$">
            </div>

            <button id="connect-button" onclick="handleConnect()">Connect to Device</button>
        </div>

        <div id="connected-view" class="hidden">
            <div class="success-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path fill-rule="evenodd" d="M2.25 5.25a3 3 0 013-3h13.5a3 3 0 013 3V15a3 3 0 01-3 3h-3v.257c0 .597.237 1.17.659 1.591l.621.622a.75.75 0 01-.53 1.28h-9a.75.75 0 01-.53-1.28l.621-.622a2.25 2.25 0 00.659-1.59V18h-3a3 3 0 01-3-3V5.25zm1.5 0v9.75c0 .83.67 1.5 1.5 1.5h13.5c.83 0 1.5-.67 1.5-1.5V5.25c0-.83-.67-1.5-1.5-1.5H5.25c-.83 0-1.5.67-1.5 1.5z" clip-rule="evenodd" />
                </svg>
            </div>

            <h3 class="text-center" style="font-size: 20px; margin-bottom: 12px;">Connected to Device</h3>
            <p class="text-center text-gray" id="connection-time"></p>
            <p class="text-center text-gray mt-4">
                Due to security restrictions, the remote control client cannot be embedded directly in this page.
                Click the button below to launch the remote control client and connect to your device.
            </p>

            <button class="mt-6" onclick="reconnect()">Launch Remote Control</button>

            <button class="mt-4" style="background-color: #1e293b; color: #ef4444; border: 1px solid #ef4444;" onclick="handleDisconnect()">End Session</button>
        </div>
    </div>

    <script>
        function handleConnect() {
            const deviceId = document.getElementById('deviceId').value.trim();
            const password = document.getElementById('password').value;

            if (!deviceId) {
                alert('Please enter a device ID');
                return;
            }

            const connectButton = document.getElementById('connect-button');
            connectButton.disabled = true;
            connectButton.innerHTML = '<svg class="spinner" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10" opacity="0.25"></circle><path d="M12 2a10 10 0 0 1 10 10" opacity="1"></path></svg> Connecting...';

            // Format the device ID by removing spaces if present
            const formattedDeviceId = deviceId.replace(/\s+/g, '');

            // Build the connection URL (using RustDesk protocol behind the scenes)
            let connectionUrl = `rustdesk://${formattedDeviceId}`;

            // If we have a password, add it to the URL
            if (password) {
                connectionUrl += `?password=${encodeURIComponent(password)}`;
            }

            // Show guidance message
            alert(`Connecting to device ${deviceId}. The remote control client will launch automatically. Please make sure the remote device is online and ready to accept connections.`);

            // Launch the remote control client
            window.location.href = connectionUrl;

            // Show connected view
            setTimeout(() => {
                document.getElementById('connect-form').classList.add('hidden');
                document.getElementById('connected-view').classList.remove('hidden');

                const now = new Date();
                document.getElementById('connection-time').textContent = `Connection established at ${now.toLocaleTimeString()}`;
            }, 1000);
        }

        function reconnect() {
            const deviceId = document.getElementById('deviceId').value.trim();
            const password = document.getElementById('password').value;

            // Format the device ID by removing spaces if present
            const formattedDeviceId = deviceId.replace(/\s+/g, '');

            // Build the connection URL (using RustDesk protocol behind the scenes)
            let connectionUrl = `rustdesk://${formattedDeviceId}`;

            // If we have a password, add it to the URL
            if (password) {
                connectionUrl += `?password=${encodeURIComponent(password)}`;
            }

            // Launch the remote control client
            window.location.href = connectionUrl;
        }

        function handleDisconnect() {
            document.getElementById('connected-view').classList.add('hidden');
            document.getElementById('connect-form').classList.remove('hidden');

            const connectButton = document.getElementById('connect-button');
            connectButton.disabled = false;
            connectButton.textContent = 'Connect to Device';
        }
    </script>
</body>
</html>
