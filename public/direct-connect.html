<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spear Direct Connect</title>
    <link rel="icon" href="/favicon.ico" />
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100vw;
        }
        
        .header {
            background-color: #1e293b;
            padding: 10px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 10;
        }
        
        .logo {
            height: 40px;
            width: auto;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        
        .controls {
            display: flex;
            gap: 10px;
        }
        
        .button {
            background-color: #4f46e5;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .button:hover {
            background-color: #4338ca;
        }
        
        .button.danger {
            background-color: #dc2626;
        }
        
        .button.danger:hover {
            background-color: #b91c1c;
        }
        
        .content {
            flex: 1;
            position: relative;
        }
        
        #rustdesk-frame {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(15, 23, 42, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 20;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #4f46e5;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        .loading-text {
            font-size: 18px;
            font-weight: 500;
            color: #e2e8f0;
            text-align: center;
            max-width: 80%;
        }
        
        .status-message {
            margin-top: 10px;
            font-size: 14px;
            color: #94a3b8;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="display: flex; align-items: center; gap: 10px;">
                <img src="/images/spear-logo.PNG" alt="Spear Logo" class="logo">
                <h1 class="title">Remote Control</h1>
            </div>
            <div class="controls">
                <button id="end-session-btn" class="button danger" onclick="endSession()">End Session</button>
            </div>
        </div>
        
        <div class="content">
            <iframe id="rustdesk-frame" title="RustDesk Remote Control"></iframe>
            
            <div id="loading-overlay" class="loading-overlay">
                <div class="spinner"></div>
                <div class="loading-text">Connecting to your device...</div>
                <div id="status-message" class="status-message"></div>
            </div>
        </div>
    </div>
    
    <script>
        // Configuration
        const config = {
            rustDeskUrl: 'https://rustdesk.com/web/',
            deviceIconSelector: '.device-icon, .device-card, [class*="device-card"], [class*="device-icon"]',
            maxAttempts: 20,
            attemptInterval: 500, // ms
            hideElements: [
                '.header-bar', 
                '.sidebar', 
                '.rustdesk-header',
                '[class*="header-bar"]',
                '[class*="sidebar"]',
                '[class*="rustdesk-header"]'
            ]
        };
        
        // Elements
        const rustDeskFrame = document.getElementById('rustdesk-frame');
        const loadingOverlay = document.getElementById('loading-overlay');
        const statusMessage = document.getElementById('status-message');
        
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const deviceId = urlParams.get('id') || '1681512408'; // Default to your device ID
        const password = urlParams.get('password') || '82AirmaN@$'; // Default to your password
        
        // Build the connection URL
        let connectionUrl = `${config.rustDeskUrl}?id=${deviceId}`;
        if (password) {
            connectionUrl += `&password=${encodeURIComponent(password)}`;
        }
        
        // Set iframe source
        rustDeskFrame.src = connectionUrl;
        
        // Update status message
        function updateStatus(message) {
            statusMessage.textContent = message;
        }
        
        // Hide loading overlay
        function hideLoading() {
            loadingOverlay.classList.add('hidden');
        }
        
        // Show loading overlay
        function showLoading() {
            loadingOverlay.classList.remove('hidden');
        }
        
        // End session
        function endSession() {
            if (confirm('Are you sure you want to end this remote control session?')) {
                window.close();
                // If window.close() doesn't work (which is common in modern browsers)
                // redirect to a different page
                window.location.href = '/bypass';
            }
        }
        
        // Attempt to click the device icon
        function attemptClickDeviceIcon(attempt = 0) {
            if (attempt >= config.maxAttempts) {
                updateStatus('Could not automatically connect. Please click on your device manually.');
                hideLoading();
                return;
            }
            
            try {
                // Try to access iframe content (will likely fail due to cross-origin policy)
                const frameDocument = rustDeskFrame.contentDocument || rustDeskFrame.contentWindow.document;
                
                // Find device icon
                const deviceIcon = frameDocument.querySelector(config.deviceIconSelector);
                
                if (deviceIcon) {
                    // Click the device icon
                    deviceIcon.click();
                    
                    // Hide RustDesk UI elements
                    const style = document.createElement('style');
                    style.textContent = config.hideElements.map(selector => `${selector} { display: none !important; }`).join('\n');
                    frameDocument.head.appendChild(style);
                    
                    // Hide loading overlay after a short delay
                    setTimeout(hideLoading, 1000);
                    
                    updateStatus('Connected successfully!');
                } else {
                    // Device icon not found yet, try again
                    updateStatus(`Waiting for device to appear... (${attempt + 1}/${config.maxAttempts})`);
                    setTimeout(() => attemptClickDeviceIcon(attempt + 1), config.attemptInterval);
                }
            } catch (e) {
                // Cross-origin error, try again
                updateStatus(`Waiting for connection... (${attempt + 1}/${config.maxAttempts})`);
                setTimeout(() => attemptClickDeviceIcon(attempt + 1), config.attemptInterval);
            }
        }
        
        // When iframe loads, try to click the device icon
        rustDeskFrame.onload = function() {
            updateStatus('RustDesk loaded. Attempting to connect...');
            attemptClickDeviceIcon();
        };
    </script>
</body>
</html>
