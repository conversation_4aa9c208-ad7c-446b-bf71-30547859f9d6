<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spear Auto Connect</title>
    <link rel="icon" href="/favicon.ico" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #0f172a;
            color: #e2e8f0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        .card {
            background-color: #1e293b;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 500px;
            padding: 24px;
        }
        .logo {
            width: 120px;
            margin: 0 auto 16px;
            display: block;
        }
        h1 {
            text-align: center;
            margin-top: 0;
            color: #e2e8f0;
        }
        .form-group {
            margin-bottom: 16px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #94a3b8;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #475569;
            background-color: #334155;
            color: #e2e8f0;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #4f46e5;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #4338ca;
        }
        button:disabled {
            background-color: #6366f1;
            opacity: 0.7;
            cursor: not-allowed;
        }
        .status {
            margin-top: 16px;
            text-align: center;
            color: #94a3b8;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(15, 23, 42, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(79, 70, 229, 0.3);
            border-radius: 50%;
            border-top-color: #4f46e5;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        .loading-text {
            font-size: 18px;
            font-weight: 500;
            color: #e2e8f0;
            text-align: center;
            max-width: 80%;
        }
        .loading-progress {
            margin-top: 10px;
            font-size: 14px;
            color: #94a3b8;
        }
        .hidden {
            display: none !important;
        }
        .error {
            background-color: rgba(220, 38, 38, 0.1);
            color: #ef4444;
            padding: 12px;
            border-radius: 4px;
            margin-top: 16px;
            border: 1px solid rgba(220, 38, 38, 0.3);
        }
        .success {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10b981;
            padding: 12px;
            border-radius: 4px;
            margin-top: 16px;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        .info {
            background-color: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            padding: 12px;
            border-radius: 4px;
            margin-top: 16px;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .debug-image {
            max-width: 100%;
            margin-top: 16px;
            border-radius: 4px;
            border: 1px solid #475569;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="card" id="connect-form">
        <img src="/images/spear-logo.PNG" alt="Spear Logo" class="logo">
        <h1>Auto Connect</h1>

        <div class="form-group">
            <label for="deviceId">Device ID</label>
            <input type="text" id="deviceId" value="1681512408">
        </div>

        <div class="form-group">
            <label for="password">Access Code</label>
            <input type="password" id="password" value="82AirmaN@$">
        </div>

        <button id="connect-button" onclick="connectToDevice()">Connect to Device</button>

        <div id="status" class="status"></div>
        <div id="debug-container" class="hidden">
            <img id="debug-image" class="debug-image" src="" alt="Debug Screenshot">
        </div>
    </div>

    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="spinner"></div>
        <div class="loading-text" id="loading-text">Connecting to your device...</div>
        <div class="loading-progress" id="loading-progress"></div>
    </div>

    <script>
        // Get elements
        const deviceIdInput = document.getElementById('deviceId');
        const passwordInput = document.getElementById('password');
        const connectButton = document.getElementById('connect-button');
        const statusElement = document.getElementById('status');
        const loadingOverlay = document.getElementById('loading-overlay');
        const loadingText = document.getElementById('loading-text');
        const loadingProgress = document.getElementById('loading-progress');
        const debugContainer = document.getElementById('debug-container');
        const debugImage = document.getElementById('debug-image');

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const urlDeviceId = urlParams.get('id');
        const urlPassword = urlParams.get('password');

        // Set input values from URL parameters if available
        if (urlDeviceId) {
            deviceIdInput.value = urlDeviceId;
        }
        if (urlPassword) {
            passwordInput.value = urlPassword;
        }

        // Show loading overlay
        function showLoading(message = 'Connecting to your device...') {
            loadingText.textContent = message;
            loadingProgress.textContent = '';
            loadingOverlay.classList.remove('hidden');
        }

        // Hide loading overlay
        function hideLoading() {
            loadingOverlay.classList.add('hidden');
        }

        // Update loading progress
        function updateProgress(message) {
            loadingProgress.textContent = message;
        }

        // Show status message
        function showStatus(message, type = '') {
            statusElement.textContent = message;
            statusElement.className = 'status';
            if (type) {
                statusElement.classList.add(type);
            }
        }

        // Show debug image
        function showDebugImage(imageUrl) {
            debugImage.src = imageUrl;
            debugContainer.classList.remove('hidden');
        }

        // Connect to device
        function connectToDevice() {
            const deviceId = deviceIdInput.value.trim();
            const password = passwordInput.value;

            if (!deviceId) {
                showStatus('Please enter a device ID', 'error');
                return;
            }

            // Disable button and show loading
            connectButton.disabled = true;
            showLoading('Initializing connection...');

            // Call the server API to automate the connection
            fetch('/api/remote-connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ deviceId, password }),
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Connection successful
                    updateProgress('Connection established!');
                    showStatus('Connection successful! Opening remote session...', 'success');

                    // Open the connection URL in a new window
                    setTimeout(() => {
                        const remoteWindow = window.open(data.connectionUrl, '_blank');

                        // Check if window was blocked
                        if (!remoteWindow || remoteWindow.closed || typeof remoteWindow.closed === 'undefined') {
                            hideLoading();
                            showStatus('Popup blocked. Please allow popups and try again.', 'error');
                        } else {
                            hideLoading();
                            showStatus('Remote session opened in a new window.', 'success');
                        }

                        // Re-enable the button
                        connectButton.disabled = false;
                    }, 1000);
                } else {
                    // Connection failed
                    hideLoading();
                    showStatus(`Connection failed: ${data.error || 'Unknown error'}`, 'error');
                    connectButton.disabled = false;

                    // Show debug screenshots if available
                    if (data.debugScreenshot) {
                        showDebugImage(data.debugScreenshot);
                    }
                    if (data.initialScreenshot) {
                        // Create another debug image element for the initial screenshot
                        const initialImage = document.createElement('img');
                        initialImage.src = data.initialScreenshot;
                        initialImage.className = 'debug-image';
                        initialImage.alt = 'Initial Screenshot';

                        // Add a label
                        const label = document.createElement('p');
                        label.textContent = 'Initial Page Screenshot:';
                        label.className = 'status';

                        // Add to the debug container
                        debugContainer.appendChild(label);
                        debugContainer.appendChild(initialImage);
                    }
                }
            })
            .catch(error => {
                hideLoading();
                showStatus(`Error: ${error.message}`, 'error');
                connectButton.disabled = false;
            });

            // Update progress messages
            let step = 0;
            const steps = [
                'Launching browser...',
                'Navigating to RustDesk...',
                'Waiting for device to appear...',
                'Clicking on device...',
                'Establishing connection...'
            ];

            const progressInterval = setInterval(() => {
                if (step < steps.length) {
                    updateProgress(steps[step]);
                    step++;
                } else {
                    clearInterval(progressInterval);
                }
            }, 3000);
        }

        // Auto-connect if URL parameters are present
        if (urlDeviceId) {
            connectToDevice();
        }
    </script>
</body>
</html>
