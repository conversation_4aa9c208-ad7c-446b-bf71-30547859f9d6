<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spear Remote Control</title>
    <link rel="icon" href="/favicon.ico" />
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #0f172a; /* Dark blue background */
            color: #e2e8f0;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .card {
            background-color: #1e293b; /* Darker card background */
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 400px;
            padding: 24px;
        }
        .card-header {
            margin-bottom: 24px;
            text-align: center;
        }
        .card-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
            color: #e2e8f0;
        }
        .form-group {
            margin-bottom: 16px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #94a3b8;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #475569;
            background-color: #334155;
            color: #e2e8f0;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input::placeholder {
            color: #64748b;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #4f46e5; /* Indigo color for Spear */
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #4338ca;
        }
        button:disabled {
            background-color: #6366f1;
            opacity: 0.7;
            cursor: not-allowed;
        }
        .success-icon {
            width: 64px;
            height: 64px;
            background-color: #064e3b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px auto;
        }
        .success-icon svg {
            width: 32px;
            height: 32px;
            color: #10b981;
        }
        .text-center {
            text-align: center;
        }
        .text-gray {
            color: #94a3b8;
        }
        .mt-4 {
            margin-top: 16px;
        }
        .mt-6 {
            margin-top: 24px;
        }
        .hidden {
            display: none;
        }
        .spinner {
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .logo {
            width: 120px;
            margin: 0 auto 16px;
            display: block;
        }
        .info-text {
            font-size: 14px;
            color: #94a3b8;
            margin-top: 16px;
            text-align: center;
        }
        .status-message {
            margin-top: 16px;
            padding: 12px;
            border-radius: 4px;
            text-align: center;
        }
        .status-message.info {
            background-color: #1e40af;
            color: #e0f2fe;
        }
        .status-message.success {
            background-color: #065f46;
            color: #d1fae5;
        }
        .status-message.error {
            background-color: #7f1d1d;
            color: #fee2e2;
        }
        #connection-frame {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            z-index: 100;
        }
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(15, 23, 42, 0.9);
            z-index: 200;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #4f46e5;
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        .loading-text {
            color: #e2e8f0;
            font-size: 18px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="card" id="connect-form">
        <div class="card-header">
            <img src="/images/spear-logo.PNG" alt="Spear Logo" class="logo">
            <h1 class="card-title">Remote Control</h1>
        </div>

        <div class="form-group">
            <label for="deviceId">Device ID</label>
            <input type="text" id="deviceId" placeholder="Enter device ID" value="1681512408">
        </div>

        <div class="form-group">
            <label for="password">Access Code</label>
            <input type="password" id="password" placeholder="Enter access code" value="82AirmaN@$">
        </div>

        <button id="connect-button" onclick="handleConnect()">Connect to Device</button>

        <div id="status-message" class="status-message hidden"></div>
    </div>

    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Connecting to your device...</div>
    </div>

    <iframe id="connection-frame" title="Remote Connection"></iframe>

    <script>
        // Configuration
        const config = {
            rustDeskUrl: 'https://rustdesk.com/web/',
            serverFallbackUrl: '/api/remote-connect', // Will implement this later if needed
            autoClickAttempts: 10,
            autoClickInterval: 500, // ms
            deviceIconSelector: '.device-icon, .device-card', // Multiple possible selectors
            connectionTimeout: 30000 // 30 seconds
        };

        // Elements
        const deviceIdInput = document.getElementById('deviceId');
        const passwordInput = document.getElementById('password');
        const connectButton = document.getElementById('connect-button');
        const statusMessage = document.getElementById('status-message');
        const connectForm = document.getElementById('connect-form');
        const connectionFrame = document.getElementById('connection-frame');
        const loadingOverlay = document.getElementById('loading-overlay');

        // Show status message
        function showStatus(message, type = 'info') {
            statusMessage.textContent = message;
            statusMessage.className = `status-message ${type}`;
            statusMessage.classList.remove('hidden');
        }

        // Hide status message
        function hideStatus() {
            statusMessage.classList.add('hidden');
        }

        // Show loading overlay
        function showLoading() {
            loadingOverlay.style.display = 'flex';
        }

        // Hide loading overlay
        function hideLoading() {
            loadingOverlay.style.display = 'none';
        }

        // Main connect function
        function handleConnect() {
            const deviceId = deviceIdInput.value.trim();
            const password = passwordInput.value;

            if (!deviceId) {
                showStatus('Please enter a device ID', 'error');
                return;
            }

            // Disable button and show loading
            connectButton.disabled = true;
            showStatus('Initiating connection...', 'info');

            // Format the device ID by removing spaces if present
            const formattedDeviceId = deviceId.replace(/\s+/g, '');

            // Skip client-side automation and go directly to server-side approach
            tryServerFallback(formattedDeviceId, password);
        }

        // Client-side automation attempt
        function tryClientSideAutomation(connectionUrl, deviceId, password) {
            showLoading();

            // Create a hidden iframe to load RustDesk
            connectionFrame.src = connectionUrl;
            connectionFrame.style.display = 'block';

            // Set up a timeout for the connection
            const connectionTimeout = setTimeout(() => {
                // If we reach the timeout, try server fallback
                hideLoading();
                connectionFrame.style.display = 'none';
                showStatus('Connection taking too long. Trying alternative method...', 'info');
                tryServerFallback(deviceId, password);
            }, config.connectionTimeout);

            // Listen for iframe load
            connectionFrame.onload = function() {
                // Try to access iframe content (will fail if cross-origin)
                try {
                    // This will throw an error due to cross-origin policy
                    const iframeDocument = connectionFrame.contentDocument || connectionFrame.contentWindow.document;

                    // If we get here, we can access the iframe (unlikely due to CORS)
                    clearTimeout(connectionTimeout);
                    attemptAutoClick(connectionFrame, 0);
                } catch (e) {
                    // Cross-origin error as expected, open in new window instead
                    clearTimeout(connectionTimeout);
                    hideLoading();
                    connectionFrame.style.display = 'none';

                    // Open in new window and provide instructions
                    openInNewWindowWithInstructions(connectionUrl);
                }
            };
        }

        // Attempt to auto-click the device icon (will likely fail due to CORS)
        function attemptAutoClick(frame, attempt) {
            if (attempt >= config.autoClickAttempts) {
                // Give up after max attempts
                hideLoading();
                openInNewWindowWithInstructions(frame.src);
                return;
            }

            try {
                const iframeDocument = frame.contentDocument || frame.contentWindow.document;
                const deviceIcon = iframeDocument.querySelector(config.deviceIconSelector);

                if (deviceIcon) {
                    // Found the device icon, click it
                    deviceIcon.click();

                    // Hide RustDesk branding
                    const style = document.createElement('style');
                    style.textContent = `
                        .header-bar, .sidebar, .rustdesk-header { display: none !important; }
                        body { overflow: hidden; }
                    `;
                    iframeDocument.head.appendChild(style);

                    // Success!
                    hideLoading();
                    connectForm.style.display = 'none';
                    showStatus('Connected successfully!', 'success');
                } else {
                    // Device icon not found yet, try again
                    setTimeout(() => {
                        attemptAutoClick(frame, attempt + 1);
                    }, config.autoClickInterval);
                }
            } catch (e) {
                // CORS error, fall back to new window
                hideLoading();
                openInNewWindowWithInstructions(frame.src);
            }
        }

        // Open in new window with instructions
        function openInNewWindowWithInstructions(url) {
            // Reset the UI
            connectButton.disabled = false;
            connectionFrame.style.display = 'none';

            // Show instructions
            showStatus('Opening remote control in a new window. Please click on your device when the page loads.', 'info');

            // Open in new window
            window.open(url, '_blank');
        }

        // Server-side automation implementation
        function tryServerFallback(deviceId, password) {
            showLoading();
            showStatus('Connecting to your device via server...', 'info');

            // Call our server API
            fetch(config.serverFallbackUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ deviceId, password }),
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();

                if (data.success) {
                    // Server successfully connected or provided a URL

                    // Check if we're in development mode
                    if (data.isDevelopment) {
                        showStatus('Opening RustDesk web client. Please click on your device when the page loads.', 'info');
                    } else {
                        showStatus('Connection established! Opening remote session...', 'success');
                    }

                    // If we have a connection URL, open it
                    if (data.connectionUrl) {
                        // Open the connection URL in a new window
                        const remoteWindow = window.open(data.connectionUrl, '_blank');

                        // Check if window was blocked
                        if (!remoteWindow) {
                            showStatus('Popup blocked. Please allow popups and try again.', 'error');
                            connectButton.disabled = false;
                            return;
                        }

                        // Reset the button after a short delay
                        setTimeout(() => {
                            connectButton.disabled = false;
                            if (data.isDevelopment) {
                                showStatus('RustDesk web client opened. Please click on your device to connect.', 'info');
                            } else {
                                showStatus('Remote control session opened in a new window.', 'success');
                            }
                        }, 2000);
                    } else {
                        // Fallback to direct connection if no URL was returned
                        const connectionUrl = `${config.rustDeskUrl}?id=${deviceId}`;
                        if (password) {
                            connectionUrl += `&password=${encodeURIComponent(password)}`;
                        }
                        window.open(connectionUrl, '_blank');
                        connectButton.disabled = false;
                    }
                } else {
                    // Server failed to connect
                    let errorMessage = data.error || 'Unknown error';

                    // If we have a debug screenshot, show it
                    if (data.debugScreenshot) {
                        // Create a debug image element
                        const debugImage = document.createElement('img');
                        debugImage.src = data.debugScreenshot;
                        debugImage.style.display = 'none';
                        document.body.appendChild(debugImage);

                        // Log the debug info
                        console.log('Debug screenshot available:', debugImage.src);
                    }

                    showStatus(`Connection failed: ${errorMessage}. Please try again or use the manual connection option.`, 'error');
                    connectButton.disabled = false;

                    // Fallback to direct connection after a short delay
                    setTimeout(() => {
                        const connectionUrl = `${config.rustDeskUrl}?id=${deviceId}`;
                        if (password) {
                            connectionUrl += `&password=${encodeURIComponent(password)}`;
                        }
                        window.open(connectionUrl, '_blank');
                    }, 3000);
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Server connection error:', error);
                showStatus('Connection error. Falling back to manual connection...', 'error');
                connectButton.disabled = false;

                // Fallback to direct connection
                setTimeout(() => {
                    const connectionUrl = `${config.rustDeskUrl}?id=${deviceId}`;
                    if (password) {
                        connectionUrl += `&password=${encodeURIComponent(password)}`;
                    }
                    window.open(connectionUrl, '_blank');
                }, 2000);
            });
        }
    </script>
</body>
</html>
