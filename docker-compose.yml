services:
  rustdesk-hbbs:
    image: rustdesk/rustdesk-server:latest
    container_name: spear-rustdesk-hbbs
    ports:
      - "21115:21115"  # NAT type test
      - "21116:21116"  # ID registration and heartbeat (TCP)
      - "21116:21116/udp"  # ID registration and heartbeat (UDP)
      - "21118:21118"  # Web client support
    volumes:
      - ./data:/root
    restart: unless-stopped
    command: hbbs -r rustdesk-hbbr:21117
    depends_on:
      - rustdesk-hbbr

  rustdesk-hbbr:
    image: rustdesk/rustdesk-server:latest
    container_name: spear-rustdesk-hbbr
    ports:
      - "21117:21117"  # Relay services
      - "21119:21119"  # Web client files (HTTP server)
    volumes:
      - ./data:/root
    restart: unless-stopped
    command: hbbr
