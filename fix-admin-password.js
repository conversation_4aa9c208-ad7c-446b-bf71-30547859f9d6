const crypto = require('crypto');

// Simple script to fix admin password - we'll use direct SQL
const adminEmail = '<EMAIL>';
const adminPassword = 'password';

// Try different hashing methods that might be used
const hashMethods = {
  sha256: crypto.createHash('sha256').update(adminPassword).digest('hex'),
  md5: crypto.createHash('md5').update(adminPassword).digest('hex'),
  plain: adminPassword,
  bcrypt_like: '$2b$10$' + crypto.createHash('sha256').update(adminPassword).digest('hex').substring(0, 53)
};

console.log('🔧 Admin Password Hash Options:');
console.log('Email:', adminEmail);
console.log('Password:', adminPassword);
console.log('');
console.log('Hash Methods:');
Object.entries(hashMethods).forEach(([method, hash]) => {
  console.log(`${method}: ${hash}`);
});

console.log('');
console.log('🎯 SQL Commands to try:');
console.log('');

// Generate SQL update commands for each hash method
Object.entries(hashMethods).forEach(([method, hash]) => {
  console.log(`-- Using ${method} hash:`);
  console.log(`UPDATE "User" SET password = '${hash}' WHERE email = '${adminEmail}';`);
  console.log('');
});

console.log('🔧 You can run these SQL commands directly in the Supabase SQL editor.');
console.log('Try the SHA-256 hash first, as that\'s what the seeding script uses.');
