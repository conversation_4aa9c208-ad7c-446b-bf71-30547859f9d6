# SPEAR - Remote Access Management Platform

## Overview

SPEAR is a subscription-based remote access management platform that provides secure device access through monthly subscriptions. Built with Next.js 14, TypeScript, and modern web technologies, SPEAR handles real customer payments and device provisioning with a focus on business model protection and revenue generation.

## 🎯 Business Model

### Pricing Structure
- **Single User Plan**: $299/month → $199/month with SPEARMINT coupon
- **Two User Bundle**: $598/month → $298/month (grandfathered for early customers)

### Revenue Protection
- 30-day billing cycles with 7-day grace period
- Automatic device access revocation for non-payment
- Admin-controlled subscription management
- PayPal webhook-based payment verification

## 🚀 Key Features

### Subscription Management
- PayPal-integrated monthly billing system
- SPEARMINT coupon support ($100 discount)
- Automatic payment processing and verification
- Grace period and access control automation

### Admin Dashboard
- Real-time subscription monitoring at `/admin/subscription-monitor`
- Payment status verification with PayPal
- Device access management and control
- Test payment system ($0.01-$10.00 safe testing)

### Remote Device Access
- RustDesk-powered secure connections
- Device provisioning and management
- Subscription-based access control
- Mobile device support (Samsung A14)

## 🏗️ Technology Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL (Railway)
- **Payments**: PayPal Production
- **Authentication**: NextAuth.js (database sessions)
- **Deployment**: Vercel (spear-global.com)
- **Remote Access**: RustDesk Pro Server

## 🔐 Admin System

- **Admin Email**: `<EMAIL>` (only admin)
- **Admin Dashboard**: `/admin/subscription-monitor`
- **Capabilities**: Subscription monitoring, payment verification, device access control
- **Test System**: Safe payment testing with real PayPal integration

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm, yarn, or pnpm

### Installation

1. Clone the repository
   ```bash
   git clone https://github.com/yourusername/spear.git
   cd spear
   ```

2. Install dependencies
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. Set up environment variables
   - Create a `.env.local` file in the root directory based on `.env.example`
   - Add your OpenAI API key: `OPENAI_API_KEY=your_openai_api_key_here`
   - Add remote access service API credentials (when available):
     ```
     # RustDesk Configuration
     RUSTDESK_SERVER_IP=your_rustdesk_server_ip
     RUSTDESK_SERVER_PORT=your_rustdesk_server_port
     RUSTDESK_API_PORT=your_rustdesk_api_port
     RUSTDESK_ANDROID_DEVICE_ID=your_android_device_id
     RUSTDESK_ANDROID_PASSWORD=your_android_device_password
     ```

4. Run the development server
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) with your browser

### Test Accounts

- **Admin**: <EMAIL> / password
- **Client**: <EMAIL> / password

## Project Structure

```
/src
  /app                 # Next.js App Router pages
    /(auth)            # Authentication routes
    /admin             # Admin dashboard and features
    /dashboard         # Client dashboard and features
  /components          # Reusable components
    /admin             # Admin-specific components
    /dashboard         # Client-specific components
    /layout            # Layout components (header, sidebar, etc.)
    /ui                # UI components (buttons, cards, etc.)
  /contexts            # React Context providers
  /data                # Mock data and data models
  /lib                 # Utility libraries
  /types               # TypeScript type definitions
  /utils               # Utility functions
```

## Key Features Implementation

### Remote Access Integrations

SPEAR supports multiple remote access services, giving you flexibility in how you connect to and manage remote devices.

#### RustDesk Integration

The RustDesk integration provides comprehensive tools for managing remote device connections:

- **Integration Dashboard**: Dedicated page for managing the RustDesk integration
  - Server configuration management
  - Connection settings
  - Allowlisted domains management for web client access
  - Connection logs and monitoring

- **Server Status Monitoring**: Real-time monitoring of RustDesk server status
  - Health status of server components
  - Connection status tracking
  - Manual status check triggering

- **Bulk Device Operations**: Perform operations on multiple devices at once
  - Import devices in bulk
  - Assign multiple devices to a client
  - Revoke access for multiple devices
  - Sync devices with RustDesk server

- **Device Provisioning**: Multiple methods for device setup
  - Manual provisioning
  - QR code-based provisioning
  - Bulk provisioning for multiple devices

- **Enhanced Remote Control**: Improved remote session experience
  - Clean interface for remote sessions
  - Connection status and elapsed time tracking
  - Web client support
  - Graceful disconnection handling



### Notification System

The notification system is implemented using React Context API to provide global state management for notifications. It includes:

- **NotificationContext**: Manages notification state and provides methods for interacting with notifications
- **NotificationProvider**: Wraps the application and provides role-based notification filtering
- **useNotifications**: Custom hook for accessing notification functionality

Notifications are categorized as either "admin" or "client" and are visually distinguished in the UI. Admin users can see both admin and client notifications, while client users can only see client notifications.

### AI Assistant

The AI Assistant is implemented using Mastra and OpenAI to provide intelligent assistance for administrative tasks. It includes:

- **Server Action**: A Next.js server action that handles AI processing on the server
- **Admin Agent**: A Mastra agent configured with specific instructions for helping administrators
- **Notification Tool**: A custom tool that allows the agent to send notifications to users
- **Conversation UI**: A chat interface for interacting with the AI assistant

The AI Assistant can help with various administrative tasks and can send both admin and client notifications when appropriate. The implementation uses Next.js server actions to handle the AI processing on the server, avoiding client-side Node.js module compatibility issues.

### Role-Based Access Control

The application implements role-based access control to ensure users only access appropriate features:

- **Admin Layout**: Checks for admin role and redirects non-admin users
- **Dashboard Layout**: Checks for client role and redirects admin users
- **Navigation Menu**: Displays different navigation options based on user role

### Subscription Management

The subscription system includes:

- **Subscription Plans**: Basic Plan ($350/month) and Additional Device Plan ($300/month/device)
- **Subscription States**: active, past_due, canceled, unpaid
- **Payment Processing**: Integration with payment processing (mock implementation)

## Deployment

The application can be deployed using various platforms:

1. Build the production version
   ```bash
   npm run build
   # or
   yarn build
   # or
   pnpm build
   ```

2. Start the production server
   ```bash
   npm start
   # or
   yarn start
   # or
   pnpm start
   ```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
