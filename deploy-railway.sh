#!/bin/bash

# SPEAR Railway Deployment Script
# This script deploys the SPEAR application to Railway with proper configuration

echo "🚀 Deploying SPEAR to Railway..."

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Installing..."
    npm install -g @railway/cli
fi

# Login to Railway (if not already logged in)
echo "🔐 Checking Railway authentication..."
railway login

# Check if we're in a Railway project
if [ ! -f "railway.json" ]; then
    echo "❌ railway.json not found. Make sure you're in the project root."
    exit 1
fi

echo "📋 Current Railway Configuration:"
echo "✅ Build Command: prisma generate && next build"
echo "✅ Start Command: next start"
echo "✅ Health Check: /api/test-db"
echo "✅ Environment Variables: Configured"

# Deploy to Railway
echo "🚀 Deploying to Railway..."
railway up

# Check deployment status
echo "📊 Checking deployment status..."
railway status

# Get the deployment URL
RAILWAY_URL=$(railway status --json 2>/dev/null | jq -r '.deployments[0].url' 2>/dev/null || echo "Unable to get URL")

echo ""
echo "✅ Deployment completed!"
echo "🌐 Application URL: $RAILWAY_URL"
echo ""
echo "🔧 Next steps:"
echo "1. Test the database connection: $RAILWAY_URL/api/test-db"
echo "2. Test Square integration: $RAILWAY_URL/api/test-square"
echo "3. Check admin login: $RAILWAY_URL/login"
echo "4. Monitor logs: railway logs"
echo ""
echo "🐛 If you encounter issues:"
echo "1. Check logs: railway logs"
echo "2. Verify environment variables: railway variables"
echo "3. Test database: curl $RAILWAY_URL/api/test-db"
echo ""
echo "📝 Important URLs:"
echo "- Admin Login: $RAILWAY_URL/login"
echo "- Client Login: $RAILWAY_URL/client-login"
echo "- Database Test: $RAILWAY_URL/api/test-db"
echo "- Square Test: $RAILWAY_URL/api/test-square"
