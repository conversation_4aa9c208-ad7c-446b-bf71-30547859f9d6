// Direct script to create admin user using the exact same method as the seeding script
const crypto = require('crypto');

/**
 * Hash a password using SHA-256 (same as auth-utils.ts hashPasswordSha256)
 */
async function hashPasswordSha256(password) {
  // Use Web Crypto API equivalent in Node.js
  const hash = crypto.createHash('sha256');
  hash.update(password);
  return hash.digest('hex');
}

async function createAdminUser() {
  const adminEmail = '<EMAIL>';
  const adminPassword = 'password';
  
  try {
    // Hash password using the same method as the application
    const hashedPassword = await hashPasswordSha256(adminPassword);
    
    console.log('🔧 Creating admin user with exact seeding script method...');
    console.log('Email:', adminEmail);
    console.log('Password:', adminPassword);
    console.log('Hashed Password:', hashedPassword);
    console.log('');
    
    // <PERSON><PERSON> commands to delete and recreate the user
    console.log('🎯 SQL Commands to run:');
    console.log('');
    console.log('-- Delete existing user:');
    console.log(`DELETE FROM "User" WHERE email = '${adminEmail}';`);
    console.log('');
    console.log('-- Create new admin user:');
    console.log(`INSERT INTO "User" (id, email, name, password, role, "createdAt", "updatedAt") VALUES ('admin-user-id-001', '${adminEmail}', 'Marquise Holton', '${hashedPassword}', 'ADMIN', NOW(), NOW());`);
    
    return hashedPassword;
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

createAdminUser();
