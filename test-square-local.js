#!/usr/bin/env node

const http = require('http');

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testSquareAPI() {
  console.log('🧪 Testing Square API locally...\n');

  try {
    // Test GET endpoint
    console.log('1. Testing GET /api/test-square');
    const getResponse = await makeRequest('GET', '/api/test-square');
    console.log('Status:', getResponse.statusCode);
    console.log('Response:', JSON.stringify(getResponse.body, null, 2));
    console.log('\n' + '='.repeat(50) + '\n');

    // Test POST endpoint
    console.log('2. Testing POST /api/test-square');
    const postResponse = await makeRequest('POST', '/api/test-square', {
      test: 'square-api-connection'
    });
    console.log('Status:', postResponse.statusCode);
    console.log('Response:', JSON.stringify(postResponse.body, null, 2));

  } catch (error) {
    console.error('❌ Error testing Square API:', error);
  }
}

testSquareAPI();
